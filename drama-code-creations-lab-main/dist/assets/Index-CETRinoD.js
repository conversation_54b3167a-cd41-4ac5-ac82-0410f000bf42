import{c as C,r as l,u as Ot,R as At,g as Pt,a as It,b as ke,d as re,j as t,P as B,e as Lt,f as q,h as ge,S as zt,D as Ft,i as _t,k as $t,l as Ht,m as T,X as Be,n as Wt,B as E,H as Ut,o as Bt}from"./index-Cw-G8SXJ.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ve=C("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U=C("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ce=C("Cog",[["path",{d:"M12 20a8 8 0 1 0 0-16 8 8 0 0 0 0 16Z",key:"sobvz5"}],["path",{d:"M12 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z",key:"11i496"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 22v-2",key:"1osdcq"}],["path",{d:"m17 20.66-1-1.73",key:"eq3orb"}],["path",{d:"M11 10.27 7 3.34",key:"16pf9h"}],["path",{d:"m20.66 17-1.73-1",key:"sg0v6f"}],["path",{d:"m3.34 7 1.73 1",key:"1ulond"}],["path",{d:"M14 12h8",key:"4f43i9"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"m20.66 7-1.73 1",key:"1ow05n"}],["path",{d:"m3.34 17 1.73-1",key:"nuk764"}],["path",{d:"m17 3.34-1 1.73",key:"2wel8s"}],["path",{d:"m11 13.73-4 6.93",key:"794ttg"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ee=C("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vt=C("Cpu",[["rect",{width:"16",height:"16",x:"4",y:"4",rx:"2",key:"14l7u7"}],["rect",{width:"6",height:"6",x:"9",y:"9",rx:"1",key:"5aljv4"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Se=C("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qt=C("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qe=C("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xe=C("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xe=C("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xt=C("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kt=C("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gt=C("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zt=C("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yt=C("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ke=C("Rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const De=C("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qt=C("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jt=C("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const er=C("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tr=C("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Te=C("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X=C("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);var rr=At.useId||(()=>{}),ar=0;function oe(e){const[r,a]=l.useState(rr());return Ot(()=>{e||a(n=>n??String(ar++))},[e]),e||(r?`radix-${r}`:"")}const nr=(e,r,a,n)=>{var o,c,i,u;const s=[a,{code:r,...n||{}}];if((c=(o=e==null?void 0:e.services)==null?void 0:o.logger)!=null&&c.forward)return e.services.logger.forward(s,"warn","react-i18next::",!0);z(s[0])&&(s[0]=`react-i18next:: ${s[0]}`),(u=(i=e==null?void 0:e.services)==null?void 0:i.logger)!=null&&u.warn?e.services.logger.warn(...s):console!=null&&console.warn&&console.warn(...s)},Me={},fe=(e,r,a,n)=>{z(a)&&Me[a]||(z(a)&&(Me[a]=new Date),nr(e,r,a,n))},Ge=(e,r)=>()=>{if(e.isInitialized)r();else{const a=()=>{setTimeout(()=>{e.off("initialized",a)},0),r()};e.on("initialized",a)}},pe=(e,r,a)=>{e.loadNamespaces(r,Ge(e,a))},Re=(e,r,a,n)=>{if(z(a)&&(a=[a]),e.options.preload&&e.options.preload.indexOf(r)>-1)return pe(e,a,n);a.forEach(s=>{e.options.ns.indexOf(s)<0&&e.options.ns.push(s)}),e.loadLanguages(r,Ge(e,n))},or=(e,r,a={})=>!r.languages||!r.languages.length?(fe(r,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:r.languages}),!0):r.hasLoadedNamespace(e,{lng:a.lng,precheck:(n,s)=>{var o;if(((o=a.bindI18n)==null?void 0:o.indexOf("languageChanging"))>-1&&n.services.backendConnector.backend&&n.isLanguageChangingTo&&!s(n.isLanguageChangingTo,e))return!1}}),z=e=>typeof e=="string",sr=e=>typeof e=="object"&&e!==null,ir=l.createContext();class lr{constructor(){this.usedNamespaces={}}addUsedNamespaces(r){r.forEach(a=>{this.usedNamespaces[a]||(this.usedNamespaces[a]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const cr=(e,r)=>{const a=l.useRef();return l.useEffect(()=>{a.current=e},[e,r]),a.current},Ze=(e,r,a,n)=>e.getFixedT(r,a,n),dr=(e,r,a,n)=>l.useCallback(Ze(e,r,a,n),[e,r,a,n]),V=(e,r={})=>{var j,k,S,R;const{i18n:a}=r,{i18n:n,defaultNS:s}=l.useContext(ir)||{},o=a||n||It();if(o&&!o.reportNamespaces&&(o.reportNamespaces=new lr),!o){fe(o,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const N=(A,P)=>z(P)?P:sr(P)&&z(P.defaultValue)?P.defaultValue:Array.isArray(A)?A[A.length-1]:A,D=[N,{},!1];return D.t=N,D.i18n={},D.ready=!1,D}(j=o.options.react)!=null&&j.wait&&fe(o,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const c={...Pt(),...o.options.react,...r},{useSuspense:i,keyPrefix:u}=c;let m=s||((k=o.options)==null?void 0:k.defaultNS);m=z(m)?[m]:m||["translation"],(R=(S=o.reportNamespaces).addUsedNamespaces)==null||R.call(S,m);const f=(o.isInitialized||o.initializedStoreOnce)&&m.every(N=>or(N,o,c)),h=dr(o,r.lng||null,c.nsMode==="fallback"?m:m[0],u),y=()=>h,x=()=>Ze(o,r.lng||null,c.nsMode==="fallback"?m:m[0],u),[v,d]=l.useState(y);let p=m.join();r.lng&&(p=`${r.lng}${p}`);const g=cr(p),b=l.useRef(!0);l.useEffect(()=>{const{bindI18n:N,bindI18nStore:D}=c;b.current=!0,!f&&!i&&(r.lng?Re(o,r.lng,m,()=>{b.current&&d(x)}):pe(o,m,()=>{b.current&&d(x)})),f&&g&&g!==p&&b.current&&d(x);const A=()=>{b.current&&d(x)};return N&&(o==null||o.on(N,A)),D&&(o==null||o.store.on(D,A)),()=>{b.current=!1,o&&(N==null||N.split(" ").forEach(P=>o.off(P,A))),D&&o&&D.split(" ").forEach(P=>o.store.off(P,A))}},[o,p]),l.useEffect(()=>{b.current&&f&&d(y)},[o,u,f]);const w=[v,o,f];if(w.t=v,w.i18n=o,w.ready=f,f||!f&&!i)return w;throw new Promise(N=>{r.lng?Re(o,r.lng,m,()=>N()):pe(o,m,()=>N())})},L={xs:0,sm:640,md:768,lg:1024,xl:1280,"2xl":1536},ur=e=>e<L.md?"mobile":e<L.lg?"tablet":"desktop",mr=e=>e>=L["2xl"]?"2xl":e>=L.xl?"xl":e>=L.lg?"lg":e>=L.md?"md":e>=L.sm?"sm":"xs",fr=()=>typeof window>"u"?!1:"ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0,pr=()=>typeof window>"u"?!1:window.devicePixelRatio>1,hr=(e,r)=>e>r?"landscape":"portrait",Oe=()=>{if(typeof window>"u")return{type:"desktop",breakpoint:"lg",width:1024,height:768,orientation:"landscape",isTouchDevice:!1,pixelRatio:1,isRetina:!1};const e=window.innerWidth,r=window.innerHeight,a=window.devicePixelRatio||1;return{type:ur(e),breakpoint:mr(e),width:e,height:r,orientation:hr(e,r),isTouchDevice:fr(),pixelRatio:a,isRetina:pr()}},Ye={getSize:()=>({width:window.innerWidth,height:window.innerHeight}),getVisibleHeight:()=>{var e;return typeof window>"u"?0:((e=window.visualViewport)==null?void 0:e.height)||window.innerHeight},isFullscreen:()=>typeof document>"u"?!1:!!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement),setViewportHeight:()=>{if(typeof window>"u")return;const e=()=>{const r=window.innerHeight*.01;document.documentElement.style.setProperty("--vh",`${r}px`)};e(),window.addEventListener("resize",e),window.addEventListener("orientationchange",e)}},gr={debounce:(e,r)=>{let a;return(...n)=>{clearTimeout(a),a=setTimeout(()=>e(...n),r)}},throttle:(e,r)=>{let a;return(...n)=>{a||(e(...n),a=!0,setTimeout(()=>a=!1,r))}},rafThrottle:e=>{let r;return(...a)=>{r||(r=requestAnimationFrame(()=>{e(...a),r=0}))}}},ae=()=>{const[e,r]=l.useState(()=>Oe()),[a,n]=l.useState(!1),s=l.useCallback(()=>{gr.debounce(()=>{r(Oe())},150)()},[]);l.useEffect(()=>(n(!0),Ye.setViewportHeight(),window.addEventListener("resize",s),window.addEventListener("orientationchange",s),s(),()=>{window.removeEventListener("resize",s),window.removeEventListener("orientationchange",s)}),[s]);const o=l.useCallback(d=>e.breakpoint===d,[e.breakpoint]),c=l.useCallback(d=>{const p=["xs","sm","md","lg","xl","2xl"],g=p.indexOf(e.breakpoint),b=p.indexOf(d);return g>=b},[e.breakpoint]),i=l.useCallback(d=>{const p=["xs","sm","md","lg","xl","2xl"],g=p.indexOf(e.breakpoint),b=p.indexOf(d);return g<=b},[e.breakpoint]),u=e.type==="mobile",m=e.type==="tablet",f=e.type==="desktop",h=e.orientation==="portrait",y=e.orientation==="landscape",x=e.isTouchDevice,v=e.isRetina;return{deviceInfo:e,isClient:a,isMobile:u,isTablet:m,isDesktop:f,isPortrait:h,isLandscape:y,isTouchDevice:x,isRetina:v,isBreakpoint:o,isBreakpointUp:c,isBreakpointDown:i,isXs:o("xs"),isSm:o("sm"),isMd:o("md"),isLg:o("lg"),isXl:o("xl"),is2Xl:o("2xl"),isSmUp:c("sm"),isMdUp:c("md"),isLgUp:c("lg"),isXlUp:c("xl"),isSmDown:i("sm"),isMdDown:i("md"),isLgDown:i("lg"),isXlDown:i("xl")}},xr=()=>{const{isTouchDevice:e}=ae();return l.useEffect(()=>{if(!e)return;document.documentElement.classList.add("touch-device");const r=document.createElement("style");return r.textContent=`
      .touch-device * {
        -webkit-tap-highlight-color: transparent;
      }

      .touch-device .scrollable {
        -webkit-overflow-scrolling: touch;
        overflow-scrolling: touch;
      }

      .touch-active {
        opacity: 0.7;
        transform: scale(0.98);
        transition: all 0.15s ease;
      }
    `,document.head.appendChild(r),()=>{document.documentElement.classList.remove("touch-device"),document.head.removeChild(r)}},[e]),{isTouchDevice:e}};var se="focusScope.autoFocusOnMount",ie="focusScope.autoFocusOnUnmount",Ae={bubbles:!1,cancelable:!0},vr="FocusScope",Qe=l.forwardRef((e,r)=>{const{loop:a=!1,trapped:n=!1,onMountAutoFocus:s,onUnmountAutoFocus:o,...c}=e,[i,u]=l.useState(null),m=ke(s),f=ke(o),h=l.useRef(null),y=re(r,d=>u(d)),x=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(n){let d=function(w){if(x.paused||!i)return;const j=w.target;i.contains(j)?h.current=j:I(h.current,{select:!0})},p=function(w){if(x.paused||!i)return;const j=w.relatedTarget;j!==null&&(i.contains(j)||I(h.current,{select:!0}))},g=function(w){if(document.activeElement===document.body)for(const k of w)k.removedNodes.length>0&&I(i)};document.addEventListener("focusin",d),document.addEventListener("focusout",p);const b=new MutationObserver(g);return i&&b.observe(i,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",d),document.removeEventListener("focusout",p),b.disconnect()}}},[n,i,x.paused]),l.useEffect(()=>{if(i){Ie.add(x);const d=document.activeElement;if(!i.contains(d)){const g=new CustomEvent(se,Ae);i.addEventListener(se,m),i.dispatchEvent(g),g.defaultPrevented||(yr(kr(Je(i)),{select:!0}),document.activeElement===d&&I(i))}return()=>{i.removeEventListener(se,m),setTimeout(()=>{const g=new CustomEvent(ie,Ae);i.addEventListener(ie,f),i.dispatchEvent(g),g.defaultPrevented||I(d??document.body,{select:!0}),i.removeEventListener(ie,f),Ie.remove(x)},0)}}},[i,m,f,x]);const v=l.useCallback(d=>{if(!a&&!n||x.paused)return;const p=d.key==="Tab"&&!d.altKey&&!d.ctrlKey&&!d.metaKey,g=document.activeElement;if(p&&g){const b=d.currentTarget,[w,j]=br(b);w&&j?!d.shiftKey&&g===j?(d.preventDefault(),a&&I(w,{select:!0})):d.shiftKey&&g===w&&(d.preventDefault(),a&&I(j,{select:!0})):g===b&&d.preventDefault()}},[a,n,x.paused]);return t.jsx(B.div,{tabIndex:-1,...c,ref:y,onKeyDown:v})});Qe.displayName=vr;function yr(e,{select:r=!1}={}){const a=document.activeElement;for(const n of e)if(I(n,{select:r}),document.activeElement!==a)return}function br(e){const r=Je(e),a=Pe(r,e),n=Pe(r.reverse(),e);return[a,n]}function Je(e){const r=[],a=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:n=>{const s=n.tagName==="INPUT"&&n.type==="hidden";return n.disabled||n.hidden||s?NodeFilter.FILTER_SKIP:n.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;a.nextNode();)r.push(a.currentNode);return r}function Pe(e,r){for(const a of e)if(!wr(a,{upTo:r}))return a}function wr(e,{upTo:r}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(r!==void 0&&e===r)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function jr(e){return e instanceof HTMLInputElement&&"select"in e}function I(e,{select:r=!1}={}){if(e&&e.focus){const a=document.activeElement;e.focus({preventScroll:!0}),e!==a&&jr(e)&&r&&e.select()}}var Ie=Nr();function Nr(){let e=[];return{add(r){const a=e[0];r!==a&&(a==null||a.pause()),e=Le(e,r),e.unshift(r)},remove(r){var a;e=Le(e,r),(a=e[0])==null||a.resume()}}}function Le(e,r){const a=[...e],n=a.indexOf(r);return n!==-1&&a.splice(n,1),a}function kr(e){return e.filter(r=>r.tagName!=="A")}var le=0;function Cr(){l.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??ze()),document.body.insertAdjacentElement("beforeend",e[1]??ze()),le++,()=>{le===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(r=>r.remove()),le--}},[])}function ze(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var O=function(){return O=Object.assign||function(r){for(var a,n=1,s=arguments.length;n<s;n++){a=arguments[n];for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])}return r},O.apply(this,arguments)};function et(e,r){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(e);s<n.length;s++)r.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(e,n[s])&&(a[n[s]]=e[n[s]]);return a}function Er(e,r,a){if(a||arguments.length===2)for(var n=0,s=r.length,o;n<s;n++)(o||!(n in r))&&(o||(o=Array.prototype.slice.call(r,0,n)),o[n]=r[n]);return e.concat(o||Array.prototype.slice.call(r))}var J="right-scroll-bar-position",ee="width-before-scroll-bar",Sr="with-scroll-bars-hidden",Dr="--removed-body-scroll-bar-size";function ce(e,r){return typeof e=="function"?e(r):e&&(e.current=r),e}function Tr(e,r){var a=l.useState(function(){return{value:e,callback:r,facade:{get current(){return a.value},set current(n){var s=a.value;s!==n&&(a.value=n,a.callback(n,s))}}}})[0];return a.callback=r,a.facade}var Mr=typeof window<"u"?l.useLayoutEffect:l.useEffect,Fe=new WeakMap;function Rr(e,r){var a=Tr(null,function(n){return e.forEach(function(s){return ce(s,n)})});return Mr(function(){var n=Fe.get(a);if(n){var s=new Set(n),o=new Set(e),c=a.current;s.forEach(function(i){o.has(i)||ce(i,null)}),o.forEach(function(i){s.has(i)||ce(i,c)})}Fe.set(a,e)},[e]),a}function Or(e){return e}function Ar(e,r){r===void 0&&(r=Or);var a=[],n=!1,s={read:function(){if(n)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return a.length?a[a.length-1]:e},useMedium:function(o){var c=r(o,n);return a.push(c),function(){a=a.filter(function(i){return i!==c})}},assignSyncMedium:function(o){for(n=!0;a.length;){var c=a;a=[],c.forEach(o)}a={push:function(i){return o(i)},filter:function(){return a}}},assignMedium:function(o){n=!0;var c=[];if(a.length){var i=a;a=[],i.forEach(o),c=a}var u=function(){var f=c;c=[],f.forEach(o)},m=function(){return Promise.resolve().then(u)};m(),a={push:function(f){c.push(f),m()},filter:function(f){return c=c.filter(f),a}}}};return s}function Pr(e){e===void 0&&(e={});var r=Ar(null);return r.options=O({async:!0,ssr:!1},e),r}var tt=function(e){var r=e.sideCar,a=et(e,["sideCar"]);if(!r)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var n=r.read();if(!n)throw new Error("Sidecar medium not found");return l.createElement(n,O({},a))};tt.isSideCarExport=!0;function Ir(e,r){return e.useMedium(r),tt}var rt=Pr(),de=function(){},ne=l.forwardRef(function(e,r){var a=l.useRef(null),n=l.useState({onScrollCapture:de,onWheelCapture:de,onTouchMoveCapture:de}),s=n[0],o=n[1],c=e.forwardProps,i=e.children,u=e.className,m=e.removeScrollBar,f=e.enabled,h=e.shards,y=e.sideCar,x=e.noIsolation,v=e.inert,d=e.allowPinchZoom,p=e.as,g=p===void 0?"div":p,b=e.gapMode,w=et(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),j=y,k=Rr([a,r]),S=O(O({},w),s);return l.createElement(l.Fragment,null,f&&l.createElement(j,{sideCar:rt,removeScrollBar:m,shards:h,noIsolation:x,inert:v,setCallbacks:o,allowPinchZoom:!!d,lockRef:a,gapMode:b}),c?l.cloneElement(l.Children.only(i),O(O({},S),{ref:k})):l.createElement(g,O({},S,{className:u,ref:k}),i))});ne.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};ne.classNames={fullWidth:ee,zeroRight:J};var Lr=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function zr(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var r=Lr();return r&&e.setAttribute("nonce",r),e}function Fr(e,r){e.styleSheet?e.styleSheet.cssText=r:e.appendChild(document.createTextNode(r))}function _r(e){var r=document.head||document.getElementsByTagName("head")[0];r.appendChild(e)}var $r=function(){var e=0,r=null;return{add:function(a){e==0&&(r=zr())&&(Fr(r,a),_r(r)),e++},remove:function(){e--,!e&&r&&(r.parentNode&&r.parentNode.removeChild(r),r=null)}}},Hr=function(){var e=$r();return function(r,a){l.useEffect(function(){return e.add(r),function(){e.remove()}},[r&&a])}},at=function(){var e=Hr(),r=function(a){var n=a.styles,s=a.dynamic;return e(n,s),null};return r},Wr={left:0,top:0,right:0,gap:0},ue=function(e){return parseInt(e||"",10)||0},Ur=function(e){var r=window.getComputedStyle(document.body),a=r[e==="padding"?"paddingLeft":"marginLeft"],n=r[e==="padding"?"paddingTop":"marginTop"],s=r[e==="padding"?"paddingRight":"marginRight"];return[ue(a),ue(n),ue(s)]},Br=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Wr;var r=Ur(e),a=document.documentElement.clientWidth,n=window.innerWidth;return{left:r[0],top:r[1],right:r[2],gap:Math.max(0,n-a+r[2]-r[0])}},Vr=at(),W="data-scroll-locked",qr=function(e,r,a,n){var s=e.left,o=e.top,c=e.right,i=e.gap;return a===void 0&&(a="margin"),`
  .`.concat(Sr,` {
   overflow: hidden `).concat(n,`;
   padding-right: `).concat(i,"px ").concat(n,`;
  }
  body[`).concat(W,`] {
    overflow: hidden `).concat(n,`;
    overscroll-behavior: contain;
    `).concat([r&&"position: relative ".concat(n,";"),a==="margin"&&`
    padding-left: `.concat(s,`px;
    padding-top: `).concat(o,`px;
    padding-right: `).concat(c,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(i,"px ").concat(n,`;
    `),a==="padding"&&"padding-right: ".concat(i,"px ").concat(n,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(J,` {
    right: `).concat(i,"px ").concat(n,`;
  }
  
  .`).concat(ee,` {
    margin-right: `).concat(i,"px ").concat(n,`;
  }
  
  .`).concat(J," .").concat(J,` {
    right: 0 `).concat(n,`;
  }
  
  .`).concat(ee," .").concat(ee,` {
    margin-right: 0 `).concat(n,`;
  }
  
  body[`).concat(W,`] {
    `).concat(Dr,": ").concat(i,`px;
  }
`)},_e=function(){var e=parseInt(document.body.getAttribute(W)||"0",10);return isFinite(e)?e:0},Xr=function(){l.useEffect(function(){return document.body.setAttribute(W,(_e()+1).toString()),function(){var e=_e()-1;e<=0?document.body.removeAttribute(W):document.body.setAttribute(W,e.toString())}},[])},Kr=function(e){var r=e.noRelative,a=e.noImportant,n=e.gapMode,s=n===void 0?"margin":n;Xr();var o=l.useMemo(function(){return Br(s)},[s]);return l.createElement(Vr,{styles:qr(o,!r,s,a?"":"!important")})},he=!1;if(typeof window<"u")try{var G=Object.defineProperty({},"passive",{get:function(){return he=!0,!0}});window.addEventListener("test",G,G),window.removeEventListener("test",G,G)}catch{he=!1}var _=he?{passive:!1}:!1,Gr=function(e){return e.tagName==="TEXTAREA"},nt=function(e,r){if(!(e instanceof Element))return!1;var a=window.getComputedStyle(e);return a[r]!=="hidden"&&!(a.overflowY===a.overflowX&&!Gr(e)&&a[r]==="visible")},Zr=function(e){return nt(e,"overflowY")},Yr=function(e){return nt(e,"overflowX")},$e=function(e,r){var a=r.ownerDocument,n=r;do{typeof ShadowRoot<"u"&&n instanceof ShadowRoot&&(n=n.host);var s=ot(e,n);if(s){var o=st(e,n),c=o[1],i=o[2];if(c>i)return!0}n=n.parentNode}while(n&&n!==a.body);return!1},Qr=function(e){var r=e.scrollTop,a=e.scrollHeight,n=e.clientHeight;return[r,a,n]},Jr=function(e){var r=e.scrollLeft,a=e.scrollWidth,n=e.clientWidth;return[r,a,n]},ot=function(e,r){return e==="v"?Zr(r):Yr(r)},st=function(e,r){return e==="v"?Qr(r):Jr(r)},ea=function(e,r){return e==="h"&&r==="rtl"?-1:1},ta=function(e,r,a,n,s){var o=ea(e,window.getComputedStyle(r).direction),c=o*n,i=a.target,u=r.contains(i),m=!1,f=c>0,h=0,y=0;do{var x=st(e,i),v=x[0],d=x[1],p=x[2],g=d-p-o*v;(v||g)&&ot(e,i)&&(h+=g,y+=v),i instanceof ShadowRoot?i=i.host:i=i.parentNode}while(!u&&i!==document.body||u&&(r.contains(i)||r===i));return(f&&(Math.abs(h)<1||!s)||!f&&(Math.abs(y)<1||!s))&&(m=!0),m},Z=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},He=function(e){return[e.deltaX,e.deltaY]},We=function(e){return e&&"current"in e?e.current:e},ra=function(e,r){return e[0]===r[0]&&e[1]===r[1]},aa=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},na=0,$=[];function oa(e){var r=l.useRef([]),a=l.useRef([0,0]),n=l.useRef(),s=l.useState(na++)[0],o=l.useState(at)[0],c=l.useRef(e);l.useEffect(function(){c.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(s));var d=Er([e.lockRef.current],(e.shards||[]).map(We),!0).filter(Boolean);return d.forEach(function(p){return p.classList.add("allow-interactivity-".concat(s))}),function(){document.body.classList.remove("block-interactivity-".concat(s)),d.forEach(function(p){return p.classList.remove("allow-interactivity-".concat(s))})}}},[e.inert,e.lockRef.current,e.shards]);var i=l.useCallback(function(d,p){if("touches"in d&&d.touches.length===2||d.type==="wheel"&&d.ctrlKey)return!c.current.allowPinchZoom;var g=Z(d),b=a.current,w="deltaX"in d?d.deltaX:b[0]-g[0],j="deltaY"in d?d.deltaY:b[1]-g[1],k,S=d.target,R=Math.abs(w)>Math.abs(j)?"h":"v";if("touches"in d&&R==="h"&&S.type==="range")return!1;var N=$e(R,S);if(!N)return!0;if(N?k=R:(k=R==="v"?"h":"v",N=$e(R,S)),!N)return!1;if(!n.current&&"changedTouches"in d&&(w||j)&&(n.current=k),!k)return!0;var D=n.current||k;return ta(D,p,d,D==="h"?w:j,!0)},[]),u=l.useCallback(function(d){var p=d;if(!(!$.length||$[$.length-1]!==o)){var g="deltaY"in p?He(p):Z(p),b=r.current.filter(function(k){return k.name===p.type&&(k.target===p.target||p.target===k.shadowParent)&&ra(k.delta,g)})[0];if(b&&b.should){p.cancelable&&p.preventDefault();return}if(!b){var w=(c.current.shards||[]).map(We).filter(Boolean).filter(function(k){return k.contains(p.target)}),j=w.length>0?i(p,w[0]):!c.current.noIsolation;j&&p.cancelable&&p.preventDefault()}}},[]),m=l.useCallback(function(d,p,g,b){var w={name:d,delta:p,target:g,should:b,shadowParent:sa(g)};r.current.push(w),setTimeout(function(){r.current=r.current.filter(function(j){return j!==w})},1)},[]),f=l.useCallback(function(d){a.current=Z(d),n.current=void 0},[]),h=l.useCallback(function(d){m(d.type,He(d),d.target,i(d,e.lockRef.current))},[]),y=l.useCallback(function(d){m(d.type,Z(d),d.target,i(d,e.lockRef.current))},[]);l.useEffect(function(){return $.push(o),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:y}),document.addEventListener("wheel",u,_),document.addEventListener("touchmove",u,_),document.addEventListener("touchstart",f,_),function(){$=$.filter(function(d){return d!==o}),document.removeEventListener("wheel",u,_),document.removeEventListener("touchmove",u,_),document.removeEventListener("touchstart",f,_)}},[]);var x=e.removeScrollBar,v=e.inert;return l.createElement(l.Fragment,null,v?l.createElement(o,{styles:aa(s)}):null,x?l.createElement(Kr,{gapMode:e.gapMode}):null)}function sa(e){for(var r=null;e!==null;)e instanceof ShadowRoot&&(r=e.host,e=e.host),e=e.parentNode;return r}const ia=Ir(rt,oa);var it=l.forwardRef(function(e,r){return l.createElement(ne,O({},e,{ref:r,sideCar:ia}))});it.classNames=ne.classNames;var la=function(e){if(typeof document>"u")return null;var r=Array.isArray(e)?e[0]:e;return r.ownerDocument.body},H=new WeakMap,Y=new WeakMap,Q={},me=0,lt=function(e){return e&&(e.host||lt(e.parentNode))},ca=function(e,r){return r.map(function(a){if(e.contains(a))return a;var n=lt(a);return n&&e.contains(n)?n:(console.error("aria-hidden",a,"in not contained inside",e,". Doing nothing"),null)}).filter(function(a){return!!a})},da=function(e,r,a,n){var s=ca(r,Array.isArray(e)?e:[e]);Q[a]||(Q[a]=new WeakMap);var o=Q[a],c=[],i=new Set,u=new Set(s),m=function(h){!h||i.has(h)||(i.add(h),m(h.parentNode))};s.forEach(m);var f=function(h){!h||u.has(h)||Array.prototype.forEach.call(h.children,function(y){if(i.has(y))f(y);else try{var x=y.getAttribute(n),v=x!==null&&x!=="false",d=(H.get(y)||0)+1,p=(o.get(y)||0)+1;H.set(y,d),o.set(y,p),c.push(y),d===1&&v&&Y.set(y,!0),p===1&&y.setAttribute(a,"true"),v||y.setAttribute(n,"true")}catch(g){console.error("aria-hidden: cannot operate on ",y,g)}})};return f(r),i.clear(),me++,function(){c.forEach(function(h){var y=H.get(h)-1,x=o.get(h)-1;H.set(h,y),o.set(h,x),y||(Y.has(h)||h.removeAttribute(n),Y.delete(h)),x||h.removeAttribute(a)}),me--,me||(H=new WeakMap,H=new WeakMap,Y=new WeakMap,Q={})}},ua=function(e,r,a){a===void 0&&(a="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),s=la(e);return s?(n.push.apply(n,Array.from(s.querySelectorAll("[aria-live]"))),da(n,s,a,"aria-hidden")):function(){return null}},ve="Dialog",[ct,Ba]=Lt(ve),[ma,M]=ct(ve),dt=e=>{const{__scopeDialog:r,children:a,open:n,defaultOpen:s,onOpenChange:o,modal:c=!0}=e,i=l.useRef(null),u=l.useRef(null),[m=!1,f]=Ht({prop:n,defaultProp:s,onChange:o});return t.jsx(ma,{scope:r,triggerRef:i,contentRef:u,contentId:oe(),titleId:oe(),descriptionId:oe(),open:m,onOpenChange:f,onOpenToggle:l.useCallback(()=>f(h=>!h),[f]),modal:c,children:a})};dt.displayName=ve;var ut="DialogTrigger",fa=l.forwardRef((e,r)=>{const{__scopeDialog:a,...n}=e,s=M(ut,a),o=re(r,s.triggerRef);return t.jsx(B.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":we(s.open),...n,ref:o,onClick:q(e.onClick,s.onOpenToggle)})});fa.displayName=ut;var ye="DialogPortal",[pa,mt]=ct(ye,{forceMount:void 0}),ft=e=>{const{__scopeDialog:r,forceMount:a,children:n,container:s}=e,o=M(ye,r);return t.jsx(pa,{scope:r,forceMount:a,children:l.Children.map(n,c=>t.jsx(ge,{present:a||o.open,children:t.jsx($t,{asChild:!0,container:s,children:c})}))})};ft.displayName=ye;var te="DialogOverlay",pt=l.forwardRef((e,r)=>{const a=mt(te,e.__scopeDialog),{forceMount:n=a.forceMount,...s}=e,o=M(te,e.__scopeDialog);return o.modal?t.jsx(ge,{present:n||o.open,children:t.jsx(ha,{...s,ref:r})}):null});pt.displayName=te;var ha=l.forwardRef((e,r)=>{const{__scopeDialog:a,...n}=e,s=M(te,a);return t.jsx(it,{as:zt,allowPinchZoom:!0,shards:[s.contentRef],children:t.jsx(B.div,{"data-state":we(s.open),...n,ref:r,style:{pointerEvents:"auto",...n.style}})})}),F="DialogContent",ht=l.forwardRef((e,r)=>{const a=mt(F,e.__scopeDialog),{forceMount:n=a.forceMount,...s}=e,o=M(F,e.__scopeDialog);return t.jsx(ge,{present:n||o.open,children:o.modal?t.jsx(ga,{...s,ref:r}):t.jsx(xa,{...s,ref:r})})});ht.displayName=F;var ga=l.forwardRef((e,r)=>{const a=M(F,e.__scopeDialog),n=l.useRef(null),s=re(r,a.contentRef,n);return l.useEffect(()=>{const o=n.current;if(o)return ua(o)},[]),t.jsx(gt,{...e,ref:s,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:q(e.onCloseAutoFocus,o=>{var c;o.preventDefault(),(c=a.triggerRef.current)==null||c.focus()}),onPointerDownOutside:q(e.onPointerDownOutside,o=>{const c=o.detail.originalEvent,i=c.button===0&&c.ctrlKey===!0;(c.button===2||i)&&o.preventDefault()}),onFocusOutside:q(e.onFocusOutside,o=>o.preventDefault())})}),xa=l.forwardRef((e,r)=>{const a=M(F,e.__scopeDialog),n=l.useRef(!1),s=l.useRef(!1);return t.jsx(gt,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:o=>{var c,i;(c=e.onCloseAutoFocus)==null||c.call(e,o),o.defaultPrevented||(n.current||(i=a.triggerRef.current)==null||i.focus(),o.preventDefault()),n.current=!1,s.current=!1},onInteractOutside:o=>{var u,m;(u=e.onInteractOutside)==null||u.call(e,o),o.defaultPrevented||(n.current=!0,o.detail.originalEvent.type==="pointerdown"&&(s.current=!0));const c=o.target;((m=a.triggerRef.current)==null?void 0:m.contains(c))&&o.preventDefault(),o.detail.originalEvent.type==="focusin"&&s.current&&o.preventDefault()}})}),gt=l.forwardRef((e,r)=>{const{__scopeDialog:a,trapFocus:n,onOpenAutoFocus:s,onCloseAutoFocus:o,...c}=e,i=M(F,a),u=l.useRef(null),m=re(r,u);return Cr(),t.jsxs(t.Fragment,{children:[t.jsx(Qe,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:s,onUnmountAutoFocus:o,children:t.jsx(Ft,{role:"dialog",id:i.contentId,"aria-describedby":i.descriptionId,"aria-labelledby":i.titleId,"data-state":we(i.open),...c,ref:m,onDismiss:()=>i.onOpenChange(!1)})}),t.jsxs(t.Fragment,{children:[t.jsx(va,{titleId:i.titleId}),t.jsx(ba,{contentRef:u,descriptionId:i.descriptionId})]})]})}),be="DialogTitle",xt=l.forwardRef((e,r)=>{const{__scopeDialog:a,...n}=e,s=M(be,a);return t.jsx(B.h2,{id:s.titleId,...n,ref:r})});xt.displayName=be;var vt="DialogDescription",yt=l.forwardRef((e,r)=>{const{__scopeDialog:a,...n}=e,s=M(vt,a);return t.jsx(B.p,{id:s.descriptionId,...n,ref:r})});yt.displayName=vt;var bt="DialogClose",wt=l.forwardRef((e,r)=>{const{__scopeDialog:a,...n}=e,s=M(bt,a);return t.jsx(B.button,{type:"button",...n,ref:r,onClick:q(e.onClick,()=>s.onOpenChange(!1))})});wt.displayName=bt;function we(e){return e?"open":"closed"}var jt="DialogTitleWarning",[Va,Nt]=_t(jt,{contentName:F,titleName:be,docsSlug:"dialog"}),va=({titleId:e})=>{const r=Nt(jt),a=`\`${r.contentName}\` requires a \`${r.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${r.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${r.docsSlug}`;return l.useEffect(()=>{e&&(document.getElementById(e)||console.error(a))},[a,e]),null},ya="DialogDescriptionWarning",ba=({contentRef:e,descriptionId:r})=>{const n=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Nt(ya).contentName}}.`;return l.useEffect(()=>{var o;const s=(o=e.current)==null?void 0:o.getAttribute("aria-describedby");r&&s&&(document.getElementById(r)||console.warn(n))},[n,e,r]),null},wa=dt,ja=ft,kt=pt,Ct=ht,Et=xt,St=yt,Na=wt;const ka=wa,Ca=ja,Dt=l.forwardRef(({className:e,...r},a)=>t.jsx(kt,{ref:a,className:T("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...r}));Dt.displayName=kt.displayName;const Tt=l.forwardRef(({className:e,children:r,...a},n)=>t.jsxs(Ca,{children:[t.jsx(Dt,{}),t.jsxs(Ct,{ref:n,className:T("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a,children:[r,t.jsxs(Na,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[t.jsx(Be,{className:"h-4 w-4"}),t.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Tt.displayName=Ct.displayName;const Mt=({className:e,...r})=>t.jsx("div",{className:T("flex flex-col space-y-1.5 text-center sm:text-left",e),...r});Mt.displayName="DialogHeader";const Rt=l.forwardRef(({className:e,...r},a)=>t.jsx(Et,{ref:a,className:T("text-lg font-semibold leading-none tracking-tight",e),...r}));Rt.displayName=Et.displayName;const Ea=l.forwardRef(({className:e,...r},a)=>t.jsx(St,{ref:a,className:T("text-sm text-muted-foreground",e),...r}));Ea.displayName=St.displayName;const K=({isOpen:e,onClose:r})=>{const{toast:a}=Wt(),n="https://t.me/ZhuaMaCode",s="@ZHUAMACODE",o=i=>{navigator.clipboard.writeText(i),a({title:"已复制到剪贴板",description:"Telegram联系方式已复制"})},c=()=>{window.open(n,"_blank")};return t.jsx(ka,{open:e,onOpenChange:r,children:t.jsxs(Tt,{className:"max-w-md mx-auto bg-card/95 backdrop-blur-lg border-primary/20",children:[t.jsx(Mt,{children:t.jsx(Rt,{className:"text-center text-2xl font-bold bg-gradient-primary bg-clip-text text-transparent",children:"联系我们"})}),t.jsxs("div",{className:"space-y-6 py-4",children:[t.jsx("div",{className:"flex justify-center",children:t.jsxs("div",{className:"relative",children:[t.jsx("img",{src:"/lovable-uploads/49b2daf0-5632-4ea8-9987-23f4f03c5173.png",alt:"Telegram QR Code",className:"w-48 h-48 rounded-2xl shadow-glow animate-glow"}),t.jsx("div",{className:"absolute inset-0 bg-gradient-primary opacity-10 rounded-2xl"})]})}),t.jsxs("div",{className:"text-center space-y-4",children:[t.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[t.jsx(De,{className:"w-5 h-5 text-primary animate-pulse"}),t.jsx("span",{className:"text-lg font-semibold text-foreground",children:"Telegram"})]}),t.jsxs("div",{className:"bg-muted/50 rounded-lg p-4 border border-primary/20",children:[t.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"用户名"}),t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("code",{className:"text-primary font-mono text-lg",children:s}),t.jsx(E,{variant:"ghost",size:"sm",onClick:()=>o(s),className:"text-primary hover:bg-primary/10",children:t.jsx(Ee,{className:"w-4 h-4"})})]})]}),t.jsxs("div",{className:"bg-muted/50 rounded-lg p-4 border border-primary/20",children:[t.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"直接链接"}),t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("code",{className:"text-primary font-mono text-sm break-all",children:n}),t.jsx(E,{variant:"ghost",size:"sm",onClick:()=>o(n),className:"text-primary hover:bg-primary/10 ml-2",children:t.jsx(Ee,{className:"w-4 h-4"})})]})]})]}),t.jsxs("div",{className:"bg-primary/10 border border-primary/30 rounded-lg p-4",children:[t.jsx("p",{className:"text-center text-primary font-medium",children:"💬 任何定制化需求请Telegram沟通"}),t.jsx("p",{className:"text-center text-sm text-muted-foreground mt-1",children:"我们会在第一时间回复您的消息"})]}),t.jsxs("div",{className:"flex flex-col space-y-3",children:[t.jsxs(E,{onClick:c,className:"bg-gradient-primary hover:shadow-glow transition-all duration-300 group",size:"lg",children:[t.jsx(De,{className:"w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform duration-300"}),"打开 Telegram",t.jsx(qe,{className:"w-4 h-4 ml-2"})]}),t.jsx(E,{variant:"outline",onClick:r,className:"border-primary/30 hover:bg-primary/5",children:"稍后联系"})]})]})]})})},Sa=()=>{const{t:e}=V(),{isMobile:r,isTablet:a,isDesktop:n}=ae(),[s,o]=l.useState(!1),c=`
    relative flex items-center justify-center overflow-hidden bg-background
    ${r?"min-h-[calc(100vh-60px)] hero-mobile":""}
    ${a?"min-h-[calc(100vh-64px)] hero-tablet":""}
    ${n?"min-h-screen hero-desktop":""}
  `.trim(),i=`
    font-bold mb-6 leading-tight
    ${r?"text-2xl sm:text-3xl":""}
    ${a?"text-4xl md:text-5xl":""}
    ${n?"text-4xl md:text-6xl lg:text-7xl":""}
  `.trim(),u=`
    mb-8 max-w-2xl mx-auto leading-relaxed
    ${r?"text-sm px-4":""}
    ${a?"text-base px-6":""}
    ${n?"text-lg px-0":""}
  `.trim();return t.jsxs("section",{id:"home",className:c,children:[t.jsxs("div",{className:"absolute inset-0",children:[t.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-background via-primary/5 to-accent/5"}),t.jsx("div",{className:"absolute inset-0 opacity-5",children:t.jsx("div",{className:"w-full h-full grid-pattern"})}),t.jsx("div",{className:"absolute top-20 left-20 w-2 h-2 bg-primary rounded-full animate-pulse"}),t.jsx("div",{className:"absolute top-40 right-32 w-1.5 h-1.5 bg-accent rounded-full animate-ping"}),t.jsx("div",{className:"absolute bottom-32 left-32 w-1 h-1 bg-primary rounded-full animate-pulse",style:{animationDelay:"1s"}}),t.jsx("div",{className:"absolute bottom-20 right-20 w-2.5 h-2.5 bg-accent/50 rounded-full animate-pulse",style:{animationDelay:"2s"}})]}),t.jsx("div",{className:`relative z-10 max-w-7xl mx-auto text-center container-responsive ${r?"px-4":a?"px-6":"px-8"}`,children:t.jsxs("div",{className:"animate-fade-in",children:[t.jsxs("div",{className:`inline-flex items-center bg-primary/10 border border-primary/20 rounded-full text-primary font-medium hover:bg-primary/20 transition-all duration-300 ${r?"px-3 py-1.5 text-xs mb-6":a?"px-4 py-2 text-sm mb-7":"px-4 py-2 text-sm mb-8"}`,children:[t.jsx(U,{className:`mr-2 ${r?"w-3 h-3":"w-4 h-4"}`}),e("hero.badge")]}),t.jsxs("h1",{className:i,children:[t.jsx("span",{className:`block text-foreground ${r?"mb-1":"mb-2"}`,children:e("hero.title")}),t.jsx("span",{className:"block bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent",children:e("hero.subtitle")})]}),t.jsxs("p",{className:`text-muted-foreground max-w-3xl mx-auto leading-relaxed ${u}`,children:[e("hero.description1"),!r&&t.jsx("br",{}),r&&" ",e("hero.description2")]}),t.jsxs("div",{className:`flex gap-4 justify-center items-center ${r?"flex-col":"flex-col sm:flex-row"}`,children:[t.jsxs(E,{size:r?"default":"lg",onClick:()=>o(!0),className:`bg-gradient-primary hover:shadow-lg hover:scale-105 transition-all duration-300 group ${r?"w-full max-w-xs text-base px-6 py-2.5":a?"text-lg px-7 py-3":"text-lg px-8 py-3"}`,children:[t.jsx(Ke,{className:`mr-2 group-hover:translate-x-1 transition-transform duration-300 ${r?"h-4 w-4":"h-5 w-5"}`}),e("hero.cta"),t.jsx(Ve,{className:`ml-2 group-hover:translate-x-1 transition-transform duration-300 ${r?"h-4 w-4":"h-5 w-5"}`})]}),t.jsxs(E,{variant:"outline",size:"lg",onClick:()=>o(!0),className:"border-2 border-primary/50 hover:border-primary hover:bg-primary/10 hover:scale-105 text-lg px-8 py-3 transition-all duration-300",children:[t.jsx(U,{className:"mr-2 h-5 w-5"}),e("hero.contact")]})]})]})}),t.jsx(K,{isOpen:s,onClose:()=>o(!1)})]})},Da=({children:e,className:r=""})=>{const{isMobile:a,isTablet:n,isTouchDevice:s}=ae();xr(),l.useEffect(()=>{if(Ye.setViewportHeight(),a){const c=m=>{m.touches.length>1&&m.preventDefault()};let i=0;const u=m=>{const f=new Date().getTime();f-i<=300&&m.preventDefault(),i=f};return document.addEventListener("touchstart",c,{passive:!1}),document.addEventListener("touchend",u,{passive:!1}),()=>{document.removeEventListener("touchstart",c),document.removeEventListener("touchend",u)}}return()=>{}},[a]);const o=`
    ${r}
    ${a?"mobile-optimized":""}
    ${n?"tablet-optimized":""}
    ${s?"touch-optimized":""}
  `.trim();return t.jsx("div",{className:o,children:e})},Ue=()=>{const{i18n:e}=V(),r=()=>{const a=e.language==="zh"?"en":"zh";e.changeLanguage(a)};return t.jsxs("button",{onClick:r,className:"relative flex items-center space-x-2 px-4 py-2 rounded-lg bg-background/50 border border-primary/20 hover:border-primary/50 backdrop-blur-sm transition-all duration-300 group tech-glow","aria-label":"Switch Language",children:[t.jsx(xe,{className:"h-4 w-4 text-primary group-hover:rotate-12 transition-transform duration-300"}),t.jsx("span",{className:"text-sm font-medium text-foreground group-hover:text-primary transition-colors duration-300",children:e.language==="zh"?"EN":"中文"}),t.jsx("div",{className:"absolute inset-0 bg-gradient-primary/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]})},Ta=()=>{const{t:e}=V(),{isMobile:r,isTablet:a,isTouchDevice:n}=ae(),[s,o]=l.useState(!1),[c,i]=l.useState(!1),u=[{name:e("nav.home"),href:"#home"},{name:e("nav.services"),href:"#services"},{name:e("nav.tools"),href:"#tools"}],m=(v,d)=>{(v.key==="Enter"||v.key===" ")&&(v.preventDefault(),d())},f=v=>{v.key==="Escape"&&s&&o(!1)},h=`
    fixed top-0 w-full z-50 bg-background/90 backdrop-blur-md border-b border-primary/20 tech-glow
    ${r?"navbar-mobile":a?"navbar-tablet":"navbar-desktop"}
  `.trim(),y=`
    max-w-7xl mx-auto
    ${r?"px-4":a?"px-6":"px-8"}
  `.trim(),x=`
    flex justify-between items-center
    ${r?"h-14":a?"h-16":"h-18"}
  `.trim();return t.jsxs(t.Fragment,{children:[t.jsxs("nav",{className:h,children:[t.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-accent/5"}),t.jsxs("div",{className:y,children:[t.jsxs("div",{className:x,children:[t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsxs("picture",{children:[t.jsx("source",{srcSet:"/logo-64.webp",media:"(max-width: 768px)"}),t.jsx("source",{srcSet:"/logo-128.webp",media:"(max-width: 1024px)"}),t.jsx("img",{src:"/logo-128.webp",alt:"Drama Code Logo - 专业定制化技术服务",className:"h-10 w-10 object-contain hover:scale-110 transition-transform duration-300",loading:"eager",width:"40",height:"40",decoding:"async"})]}),t.jsxs("div",{className:"flex flex-col",children:[t.jsx("span",{className:"text-xl font-bold bg-gradient-primary bg-clip-text text-transparent hover:scale-105 transition-transform duration-300 cursor-default",role:"heading","aria-level":1,children:"Drama Code"}),t.jsx("span",{className:"text-xs text-muted-foreground","aria-label":"中文名称",children:"抓马代码"})]})]}),t.jsxs("div",{className:"hidden md:flex items-center space-x-8",children:[u.map(v=>t.jsxs("a",{href:v.href,className:"text-foreground hover:text-primary transition-colors duration-300 relative group",children:[v.name,t.jsx("span",{className:"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-primary transition-all duration-300 group-hover:w-full"})]},v.name)),t.jsx(Ue,{}),t.jsxs(E,{variant:"default",onClick:()=>i(!0),className:"bg-gradient-primary hover:shadow-glow hover:scale-105 transition-all duration-300 group relative overflow-hidden",children:[t.jsx("div",{className:"absolute inset-0 bg-white/20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"}),t.jsx(X,{className:"w-4 h-4 mr-2 group-hover:rotate-12 transition-transform duration-300"}),e("hero.cta")]})]}),t.jsxs("div",{className:"md:hidden flex items-center space-x-2",children:[t.jsx(Ue,{}),t.jsx(E,{variant:"ghost",size:r?"default":"sm",onClick:()=>o(!s),onKeyDown:v=>m(v,()=>o(!s)),className:`text-foreground hover:text-primary ${n?"min-h-[44px] min-w-[44px]":""}`,"aria-label":s?"关闭导航菜单":"打开导航菜单","aria-expanded":s,"aria-controls":"mobile-menu",children:s?t.jsx(Be,{className:r?"h-5 w-5":"h-6 w-6"}):t.jsx(Gt,{className:r?"h-5 w-5":"h-6 w-6"})})]})]}),s&&t.jsx("div",{className:"md:hidden animate-fade-in",id:"mobile-menu",role:"navigation","aria-label":"移动端导航菜单",onKeyDown:f,children:t.jsxs("div",{className:"px-2 pt-2 pb-3 space-y-1 bg-card border border-border rounded-lg mt-2",children:[u.map(v=>t.jsx("a",{href:v.href,className:"block px-3 py-2 text-foreground hover:text-primary hover:bg-muted/50 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",onClick:()=>o(!1),onKeyDown:d=>m(d,()=>o(!1)),tabIndex:0,role:"menuitem",children:v.name},v.name)),t.jsx("div",{className:"px-3 pt-2",children:t.jsx(E,{className:"w-full bg-gradient-primary hover:shadow-glow transition-all duration-300",onClick:()=>{i(!0),o(!1)},children:e("hero.cta")})})]})})]})]}),t.jsx(K,{isOpen:c,onClose:()=>i(!1)})]})},Ma=({title:e="Drama Code - 专业定制化技术服务",description:r="Drama Code提供专业的网站开发、移动应用开发、系统集成等定制化技术服务。我们致力于为客户提供高质量、创新的技术解决方案。",keywords:a="网站开发,移动应用开发,系统集成,技术服务,定制开发,前端开发,后端开发,全栈开发",image:n="/og-image.jpg",url:s="https://dramacode.com",type:o="website",siteName:c="Drama Code",locale:i="zh_CN",alternateLocales:u=["en_US"],author:m="Drama Code Team",publishedTime:f,modifiedTime:h,section:y,tags:x,noindex:v=!1,nofollow:d=!1,canonical:p})=>{const g=e.includes("Drama Code")?e:`${e} | Drama Code`,b=s||(typeof window<"u"?window.location.href:""),w=n.startsWith("http")?n:`${s}${n}`,j=p||b,k={"@context":"https://schema.org","@type":"Organization",name:c,url:s,logo:`${s}/logo-512.png`,description:r,contactPoint:{"@type":"ContactPoint",contactType:"customer service",email:"<EMAIL>"},sameAs:["https://github.com/dramacode","https://twitter.com/dramacode"],address:{"@type":"PostalAddress",addressCountry:"CN"},foundingDate:"2024",numberOfEmployees:"1-10",knowsAbout:["Web Development","Mobile App Development","System Integration","Frontend Development","Backend Development","Full Stack Development"]},S=o==="article"?{"@context":"https://schema.org","@type":"Article",headline:e,description:r,image:w,author:{"@type":"Person",name:m},publisher:{"@type":"Organization",name:c,logo:{"@type":"ImageObject",url:`${s}/logo-512.png`}},datePublished:f,dateModified:h||f,mainEntityOfPage:{"@type":"WebPage","@id":b}}:null,R=[v?"noindex":"index",d?"nofollow":"follow","max-snippet:-1","max-image-preview:large","max-video-preview:-1"].join(", ");return t.jsxs(Ut,{children:[t.jsx("title",{children:g}),t.jsx("meta",{name:"description",content:r}),t.jsx("meta",{name:"keywords",content:a}),t.jsx("meta",{name:"author",content:m}),t.jsx("meta",{name:"robots",content:R}),t.jsx("link",{rel:"canonical",href:j}),t.jsx("meta",{property:"og:type",content:o}),t.jsx("meta",{property:"og:title",content:g}),t.jsx("meta",{property:"og:description",content:r}),t.jsx("meta",{property:"og:image",content:w}),t.jsx("meta",{property:"og:url",content:b}),t.jsx("meta",{property:"og:site_name",content:c}),t.jsx("meta",{property:"og:locale",content:i}),u==null?void 0:u.map(N=>t.jsx("meta",{property:"og:locale:alternate",content:N},N)),o==="article"&&t.jsxs(t.Fragment,{children:[m&&t.jsx("meta",{property:"article:author",content:m}),f&&t.jsx("meta",{property:"article:published_time",content:f}),h&&t.jsx("meta",{property:"article:modified_time",content:h}),y&&t.jsx("meta",{property:"article:section",content:y}),x==null?void 0:x.map(N=>t.jsx("meta",{property:"article:tag",content:N},N))]}),t.jsx("meta",{name:"twitter:card",content:"summary_large_image"}),t.jsx("meta",{name:"twitter:title",content:g}),t.jsx("meta",{name:"twitter:description",content:r}),t.jsx("meta",{name:"twitter:image",content:w}),t.jsx("meta",{name:"twitter:site",content:"@dramacode"}),t.jsx("meta",{name:"twitter:creator",content:"@dramacode"}),t.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0"}),t.jsx("meta",{name:"format-detection",content:"telephone=no"}),t.jsx("meta",{name:"mobile-web-app-capable",content:"yes"}),t.jsx("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),t.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),t.jsx("meta",{name:"apple-mobile-web-app-title",content:c}),t.jsx("meta",{name:"theme-color",content:"#6366f1"}),t.jsx("meta",{name:"msapplication-TileColor",content:"#6366f1"}),t.jsx("link",{rel:"icon",type:"image/x-icon",href:"/favicon.ico"}),t.jsx("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),t.jsx("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),t.jsx("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),t.jsx("link",{rel:"manifest",href:"/site.webmanifest"}),t.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),t.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),t.jsx("link",{rel:"dns-prefetch",href:"https://www.google-analytics.com"}),t.jsx("link",{rel:"alternate",hrefLang:"zh-CN",href:`${s}/zh`}),t.jsx("link",{rel:"alternate",hrefLang:"en",href:`${s}/en`}),t.jsx("link",{rel:"alternate",hrefLang:"x-default",href:s}),t.jsx("script",{type:"application/ld+json",children:JSON.stringify(k)}),S&&t.jsx("script",{type:"application/ld+json",children:JSON.stringify(S)}),t.jsx("meta",{httpEquiv:"X-Content-Type-Options",content:"nosniff"}),t.jsx("meta",{httpEquiv:"X-Frame-Options",content:"DENY"}),t.jsx("meta",{httpEquiv:"X-XSS-Protection",content:"1; mode=block"}),t.jsx("meta",{httpEquiv:"Referrer-Policy",content:"strict-origin-when-cross-origin"}),t.jsx("link",{rel:"preload",href:"/fonts/inter-var.woff2",as:"font",type:"font/woff2",crossOrigin:"anonymous"})]})},Ra=[{icon:Zt,title:"需求咨询",description:"深入了解您的业务需求和技术要求"},{icon:Xe,title:"方案设计",description:"制定详细的技术方案和项目计划"},{icon:U,title:"技术开发",description:"按照设计方案进行项目开发"},{icon:tr,title:"测试验收",description:"全面的功能测试和性能优化"},{icon:Ke,title:"项目部署",description:"项目部署和上线发布"},{icon:Xt,title:"持续支持",description:"持续的技术支持和维护服务"}],Oa=()=>t.jsx("section",{className:"py-16 bg-muted/30",children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"text-center mb-12",children:[t.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"服务流程"}),t.jsx("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"专业的服务流程，确保项目从需求分析到上线维护的每个环节都精益求精"})]}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto",children:Ra.map((e,r)=>{const a=e.icon;return t.jsxs("div",{className:"bg-card/50 backdrop-blur-sm border border-border hover:border-accent/50 rounded-2xl p-6 transition-all duration-300 hover:shadow-lg hover:scale-105",children:[t.jsxs("div",{className:"flex items-center justify-between mb-4",children:[t.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-accent/20 to-primary/20 rounded-xl flex items-center justify-center",children:t.jsx(a,{className:"h-6 w-6 text-accent"})}),t.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-accent to-primary rounded-full flex items-center justify-center text-white font-bold text-sm",children:r+1})]}),t.jsx("h3",{className:"text-xl font-bold text-foreground mb-3",children:e.title}),t.jsx("p",{className:"text-muted-foreground leading-relaxed",children:e.description})]},r)})})]})}),je=l.forwardRef(({className:e,...r},a)=>t.jsx("div",{ref:a,className:T("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));je.displayName="Card";const Aa=l.forwardRef(({className:e,...r},a)=>t.jsx("div",{ref:a,className:T("flex flex-col space-y-1.5 p-6",e),...r}));Aa.displayName="CardHeader";const Pa=l.forwardRef(({className:e,...r},a)=>t.jsx("h3",{ref:a,className:T("text-2xl font-semibold leading-none tracking-tight",e),...r}));Pa.displayName="CardTitle";const Ia=l.forwardRef(({className:e,...r},a)=>t.jsx("p",{ref:a,className:T("text-sm text-muted-foreground",e),...r}));Ia.displayName="CardDescription";const Ne=l.forwardRef(({className:e,...r},a)=>t.jsx("div",{ref:a,className:T("p-6 pt-0",e),...r}));Ne.displayName="CardContent";const La=l.forwardRef(({className:e,...r},a)=>t.jsx("div",{ref:a,className:T("flex items-center p-6 pt-0",e),...r}));La.displayName="CardFooter";const za=({title:e,description:r,features:a,serviceType:n})=>{const[s,o]=l.useState(!1),i=(()=>{switch(n){case"web":return{icon:xe,gradient:"from-blue-500/20 via-purple-500/20 to-cyan-500/20",iconColor:"text-blue-400",pattern:"web-pattern"};case"script":return{icon:U,gradient:"from-green-500/20 via-purple-500/20 to-emerald-500/20",iconColor:"text-green-400",pattern:"code-pattern"};case"miniprogram":return{icon:Jt,gradient:"from-orange-500/20 via-purple-500/20 to-yellow-500/20",iconColor:"text-orange-400",pattern:"hex-pattern"};case"mobile":return{icon:Yt,gradient:"from-red-500/20 via-purple-500/20 to-pink-500/20",iconColor:"text-red-400",pattern:"circle-pattern"};default:return{icon:X,gradient:"from-purple-500/20 to-cyan-500/20",iconColor:"text-purple-400",pattern:"default-pattern"}}})(),u=i.icon;return t.jsxs(t.Fragment,{children:[t.jsx(je,{className:"group relative overflow-hidden bg-card/50 backdrop-blur-sm border-border hover:border-primary/50 transition-all duration-500 hover:shadow-glow hover:scale-105",children:t.jsxs(Ne,{className:"p-0",children:[t.jsxs("div",{className:`relative h-48 overflow-hidden bg-gradient-to-br ${i.gradient}`,children:[t.jsx("div",{className:"absolute inset-0 opacity-10",children:t.jsx("div",{className:`w-full h-full ${i.pattern}`})}),t.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:t.jsx(u,{className:`w-20 h-20 ${i.iconColor} group-hover:scale-110 transition-transform duration-500`})}),t.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-background/80 to-transparent"}),t.jsx("div",{className:"absolute inset-0 bg-gradient-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),t.jsxs("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700",children:[t.jsx("div",{className:"absolute top-4 left-4 w-2 h-2 bg-primary rounded-full animate-pulse"}),t.jsx("div",{className:"absolute top-8 right-8 w-1 h-1 bg-accent rounded-full animate-ping"}),t.jsx("div",{className:"absolute bottom-6 left-8 w-1.5 h-1.5 bg-primary rounded-full animate-bounce"})]})]}),t.jsxs("div",{className:"p-6",children:[t.jsxs("h3",{className:"text-xl font-bold text-foreground mb-3 group-hover:text-primary transition-colors duration-300 flex items-center",children:[e,t.jsx(X,{className:"ml-2 w-5 h-5 opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-all duration-300"})]}),t.jsx("p",{className:"text-muted-foreground mb-4 leading-relaxed group-hover:text-foreground/80 transition-colors duration-300",children:r}),t.jsx("ul",{className:"space-y-2 mb-6",children:a.map((m,f)=>t.jsxs("li",{className:"flex items-center text-sm text-muted-foreground group-hover:text-foreground/70 transition-colors duration-300",children:[t.jsx("div",{className:"w-1.5 h-1.5 bg-primary rounded-full mr-3 flex-shrink-0 group-hover:animate-pulse"}),m]},f))}),t.jsxs(E,{onClick:()=>o(!0),variant:"outline",className:"w-full group/btn hover:bg-gradient-primary hover:text-primary-foreground border-primary/30 hover:border-primary transition-all duration-500 relative overflow-hidden",children:[t.jsx("div",{className:"absolute inset-0 bg-gradient-primary transform scale-x-0 group-hover/btn:scale-x-100 transition-transform duration-500 origin-left"}),t.jsxs("span",{className:"relative z-10 flex items-center justify-center",children:["立即咨询",t.jsx(Ve,{className:"ml-2 h-4 w-4 transition-transform duration-300 group-hover/btn:translate-x-1 group-hover/btn:scale-110"})]})]})]})]})}),t.jsx(K,{isOpen:s,onClose:()=>o(!1)})]})},Fa=()=>{const{t:e}=V(),[r,a]=l.useState(!1),n=o=>{const c=e(o,{returnObjects:!0});return Array.isArray(c)&&c.every(i=>typeof i=="string")?c:[]},s=[{title:e("services.web.title"),description:e("services.web.description"),features:n("services.web.features"),serviceType:"web"},{title:e("services.script.title"),description:e("services.script.description"),features:n("services.script.features"),serviceType:"script"},{title:e("services.miniprogram.title"),description:e("services.miniprogram.description"),features:n("services.miniprogram.features"),serviceType:"miniprogram"},{title:e("services.mobile.title"),description:e("services.mobile.description"),features:n("services.mobile.features"),serviceType:"mobile"}];return t.jsxs("section",{id:"services",className:"relative py-20 bg-background overflow-hidden",children:[t.jsxs("div",{className:"absolute inset-0",children:[t.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background"}),t.jsx("div",{className:"absolute inset-0 opacity-10",children:t.jsx("div",{className:"w-full h-full default-pattern"})}),t.jsx("div",{className:"absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary/30 to-transparent"}),t.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-accent/30 to-transparent"})]}),t.jsxs("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[t.jsxs("div",{className:"text-center mb-16 animate-fade-in",children:[t.jsxs("div",{className:"inline-flex items-center px-6 py-3 bg-primary/10 border-2 border-primary/20 rounded-full text-primary text-sm font-medium mb-6 hover:bg-primary/20 transition-all duration-300 group",children:[t.jsx(xe,{className:"w-4 h-4 mr-2 group-hover:rotate-12 transition-transform duration-300"}),e("services.badge"),t.jsx(Kt,{className:"w-4 h-4 ml-2 text-red-400 animate-pulse"})]}),t.jsxs("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:[e("services.title")," ",t.jsx("span",{className:"bg-gradient-primary bg-clip-text text-transparent hover:animate-glow transition-all duration-300",children:e("services.titleHighlight")})]}),t.jsxs("p",{className:"text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed",children:[t.jsx("span",{className:"inline-block hover:text-primary transition-colors duration-300",children:e("services.description1")}),t.jsx("br",{}),t.jsx("span",{className:"inline-block hover:text-accent transition-colors duration-300",children:e("services.description2")})]})]}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8",children:s.map((o,c)=>t.jsx("div",{className:"animate-fade-in",style:{animationDelay:`${c*.2}s`},children:t.jsx(za,{...o})},o.title))})]}),t.jsx(K,{isOpen:r,onClose:()=>a(!1)})]})},_a=Bt("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function $a({className:e,variant:r,...a}){return t.jsx("div",{className:T(_a({variant:r}),e),...a})}const Ha=()=>{const{t:e}=V(),[r,a]=l.useState("all"),[n,s]=l.useState(!1),o=[{id:"all",name:e("tools.categories.all"),icon:t.jsx(Te,{className:"w-4 h-4"})},{id:"development",name:e("tools.categories.development"),icon:t.jsx(U,{className:"w-4 h-4"})},{id:"data",name:e("tools.categories.data"),icon:t.jsx(Se,{className:"w-4 h-4"})},{id:"automation",name:e("tools.categories.automation"),icon:t.jsx(Ce,{className:"w-4 h-4"})},{id:"utilities",name:e("tools.categories.utilities"),icon:t.jsx(X,{className:"w-4 h-4"})}],c=[{id:"api-tester",name:"API接口测试器",description:"强大的API接口测试工具，支持多种请求方式，自动生成测试报告",category:"development",icon:t.jsx(U,{className:"w-6 h-6"}),features:["RESTful API测试","自动化测试脚本","性能监控","报告生成"],tags:["API","测试","自动化"],popularity:95},{id:"data-processor",name:"数据处理工具",description:"高效的数据清洗、转换和分析工具，支持多种数据格式",category:"data",icon:t.jsx(Se,{className:"w-6 h-6"}),features:["数据清洗","格式转换","批量处理","可视化分析"],tags:["数据","分析","清洗"],popularity:88},{id:"automation-suite",name:"自动化工具套件",description:"全面的自动化解决方案，包含任务调度、监控和报警功能",category:"automation",icon:t.jsx(Ce,{className:"w-6 h-6"}),features:["任务调度","系统监控","自动报警","日志分析"],tags:["自动化","监控","调度"],popularity:92},{id:"security-scanner",name:"安全扫描器",description:"专业的安全漏洞扫描工具，帮助识别和修复安全问题",category:"utilities",icon:t.jsx(Qt,{className:"w-6 h-6"}),features:["漏洞扫描","安全评估","合规检查","修复建议"],tags:["安全","扫描","漏洞"],popularity:90},{id:"performance-monitor",name:"性能监控工具",description:"实时监控系统性能，提供详细的性能分析和优化建议",category:"utilities",icon:t.jsx(Vt,{className:"w-6 h-6"}),features:["实时监控","性能分析","资源优化","告警通知"],tags:["性能","监控","优化"],popularity:87},{id:"report-generator",name:"报告生成器",description:"智能报告生成工具，支持多种模板和自定义格式",category:"utilities",icon:t.jsx(Xe,{className:"w-6 h-6"}),features:["模板定制","数据可视化","自动生成","多格式导出"],tags:["报告","模板","导出"],popularity:85}],i=r==="all"?c:c.filter(u=>u.category===r);return t.jsxs("section",{id:"tools",className:"relative py-20 bg-background overflow-hidden",children:[t.jsxs("div",{className:"absolute inset-0",children:[t.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background"}),t.jsx("div",{className:"absolute inset-0 opacity-10",children:t.jsx("div",{className:"w-full h-full code-pattern"})}),t.jsx("div",{className:"absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary/30 to-transparent"}),t.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-accent/30 to-transparent"})]}),t.jsxs("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[t.jsxs("div",{className:"text-center mb-16 animate-fade-in",children:[t.jsxs("div",{className:"inline-flex items-center px-6 py-3 bg-accent/10 border-2 border-accent/20 rounded-full text-accent text-sm font-medium mb-6 hover:bg-accent/20 transition-all duration-300 group",children:[t.jsx(Te,{className:"w-4 h-4 mr-2 group-hover:rotate-12 transition-transform duration-300"}),e("tools.badge"),t.jsx(X,{className:"w-4 h-4 ml-2 text-yellow-400 animate-pulse"})]}),t.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:e("tools.title")}),t.jsx("p",{className:"text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed",children:e("tools.subtitle")})]}),t.jsx("div",{className:"flex flex-wrap justify-center gap-4 mb-12",children:o.map(u=>t.jsxs(E,{variant:r===u.id?"default":"outline",onClick:()=>a(u.id),className:"flex items-center gap-2 px-6 py-3 rounded-full transition-all duration-300 hover:scale-105",children:[u.icon,u.name]},u.id))}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:i.map((u,m)=>t.jsx("div",{className:"animate-fade-in",style:{animationDelay:`${m*.1}s`},children:t.jsx(je,{className:"h-full hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group border-2 hover:border-primary/20",children:t.jsxs(Ne,{className:"p-6",children:[t.jsx("div",{className:"flex items-start justify-between mb-4",children:t.jsxs("div",{className:"flex items-center gap-3",children:[t.jsx("div",{className:"p-3 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors duration-300",children:u.icon}),t.jsxs("div",{children:[t.jsx("h3",{className:"font-semibold text-lg text-foreground group-hover:text-primary transition-colors duration-300",children:u.name}),t.jsx("div",{className:"flex items-center gap-2 mt-1",children:t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(er,{className:"w-4 h-4 text-yellow-400 fill-current"}),t.jsxs("span",{className:"text-sm text-muted-foreground",children:[u.popularity,"%"]})]})})]})]})}),t.jsx("p",{className:"text-muted-foreground mb-4 leading-relaxed",children:u.description}),t.jsxs("div",{className:"space-y-3 mb-4",children:[t.jsx("h4",{className:"font-medium text-sm text-foreground",children:"主要功能："}),t.jsx("ul",{className:"space-y-1",children:u.features.slice(0,3).map((f,h)=>t.jsxs("li",{className:"text-sm text-muted-foreground flex items-center gap-2",children:[t.jsx("div",{className:"w-1.5 h-1.5 bg-primary rounded-full"}),f]},h))})]}),t.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:u.tags.map(f=>t.jsx($a,{variant:"secondary",className:"text-xs",children:f},f))}),t.jsxs("div",{className:"flex gap-2",children:[t.jsxs(E,{size:"sm",className:"flex-1",onClick:()=>s(!0),children:[t.jsx(qt,{className:"w-4 h-4 mr-2"}),"获取工具"]}),t.jsx(E,{size:"sm",variant:"outline",children:t.jsx(qe,{className:"w-4 h-4"})})]})]})})},u.id))})]}),t.jsx(K,{isOpen:n,onClose:()=>s(!1)})]})},Wa=()=>t.jsx("a",{href:"#main-content",className:"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md z-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",children:"跳转到主内容"}),qa=()=>{const{t:e}=V();return t.jsxs(Da,{className:"min-h-screen bg-background",children:[t.jsx(Ma,{title:"Drama Code - 专业定制化技术服务",description:"Drama Code提供专业的网站开发、移动应用开发、系统集成等定制化技术服务。我们致力于为客户提供高质量、创新的技术解决方案。",keywords:"网站开发,移动应用开发,系统集成,技术服务,定制开发,前端开发,后端开发,全栈开发,React,TypeScript,Node.js",type:"website"}),t.jsx(Wa,{}),t.jsx(Ta,{}),t.jsxs("main",{id:"main-content",role:"main","aria-label":"主要内容",children:[t.jsx(Sa,{}),t.jsx(Fa,{}),t.jsx(Oa,{}),t.jsx(Ha,{})]}),t.jsxs("footer",{className:"relative bg-background border-t border-primary/20 py-12 overflow-hidden",children:[t.jsxs("div",{className:"absolute inset-0",children:[t.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-primary/5 to-transparent"}),t.jsx("div",{className:"absolute inset-0 opacity-10",children:t.jsx("div",{className:"w-full h-full default-pattern"})}),t.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary/50 to-transparent"})]}),t.jsx("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:t.jsxs("div",{className:"text-center",children:[t.jsxs("div",{className:"mb-6",children:[t.jsx("h3",{className:"text-2xl font-bold bg-gradient-primary bg-clip-text text-transparent",children:"Drama Code"}),t.jsx("div",{className:"w-20 h-px bg-gradient-primary mx-auto mt-2"})]}),t.jsx("p",{className:"text-muted-foreground mb-2 animate-fade-in",children:e("footer.copyright")}),t.jsx("p",{className:"text-sm text-muted-foreground/80 animate-fade-in",style:{animationDelay:"0.2s"},children:e("footer.description")}),t.jsxs("div",{className:"flex justify-center items-center mt-6 space-x-4",children:[t.jsx("div",{className:"w-2 h-2 bg-primary rounded-full animate-pulse"}),t.jsx("div",{className:"w-1 h-1 bg-accent rounded-full animate-ping"}),t.jsx("div",{className:"w-2 h-2 bg-primary rounded-full animate-pulse",style:{animationDelay:"1s"}})]})]})})]})]})};export{qa as default};
