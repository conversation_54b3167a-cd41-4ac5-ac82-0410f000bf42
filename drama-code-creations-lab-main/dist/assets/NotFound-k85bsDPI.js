import{c as o,p as n,r as c,j as e,B as r,L as t,q as l}from"./index-Cw-G8SXJ.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const i=o("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),m=()=>{const s=n();return c.useEffect(()=>{console.error("404 Error: User attempted to access non-existent route:",s.pathname);const a=window;typeof window<"u"&&a.gtag&&a.gtag("event","page_not_found",{page_path:s.pathname,page_title:"404 - Page Not Found"})},[s.pathname]),e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:e.jsxs("div",{className:"max-w-md w-full text-center space-y-8",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"text-8xl font-bold bg-gradient-primary bg-clip-text text-transparent animate-pulse",children:"404"}),e.jsx("div",{className:"w-24 h-1 bg-gradient-primary mx-auto rounded-full"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h1",{className:"text-2xl font-bold text-foreground",children:"页面未找到"}),e.jsxs("p",{className:"text-muted-foreground leading-relaxed",children:["抱歉，您访问的页面不存在或已被移动。",e.jsx("br",{}),"请检查URL是否正确，或返回首页继续浏览。"]}),e.jsxs("div",{className:"bg-muted/50 p-3 rounded-lg border text-sm",children:[e.jsx("span",{className:"text-muted-foreground",children:"尝试访问："}),e.jsx("code",{className:"text-primary font-mono ml-2",children:s.pathname})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[e.jsx(r,{asChild:!0,className:"flex items-center gap-2 bg-gradient-primary hover:shadow-glow",children:e.jsxs(t,{to:"/",children:[e.jsx(l,{className:"w-4 h-4"}),"返回首页"]})}),e.jsxs(r,{variant:"outline",onClick:()=>window.history.back(),className:"flex items-center gap-2",children:[e.jsx(i,{className:"w-4 h-4"}),"返回上页"]})]}),e.jsxs("div",{className:"pt-4 border-t border-border",children:[e.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:"或者您可以："}),e.jsxs("div",{className:"flex flex-col gap-2 text-sm",children:[e.jsx(t,{to:"/#services",className:"text-primary hover:text-primary/80 transition-colors",children:"• 查看我们的服务"}),e.jsx(t,{to:"/#tools",className:"text-primary hover:text-primary/80 transition-colors",children:"• 浏览技术工具"}),e.jsx("a",{href:"mailto:<EMAIL>",className:"text-primary hover:text-primary/80 transition-colors",children:"• 联系我们获取帮助"})]})]})]})})};export{m as default};
