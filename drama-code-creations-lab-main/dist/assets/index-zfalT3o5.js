var qy=Object.defineProperty;var wd=e=>{throw TypeError(e)};var Xy=(e,t,n)=>t in e?qy(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Ba=(e,t,n)=>Xy(e,typeof t!="symbol"?t+"":t,n),Va=(e,t,n)=>t.has(e)||wd("Cannot "+n);var T=(e,t,n)=>(Va(e,t,"read from private field"),n?n.call(e):t.get(e)),Z=(e,t,n)=>t.has(e)?wd("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),Y=(e,t,n,r)=>(Va(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),Oe=(e,t,n)=>(Va(e,t,"access private method"),n);var _i=(e,t,n,r)=>({set _(o){Y(e,t,o,n)},get _(){return T(e,t,r)}});function Zy(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function rh(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var oh={exports:{}},la={},ih={exports:{}},q={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bi=Symbol.for("react.element"),Jy=Symbol.for("react.portal"),e0=Symbol.for("react.fragment"),t0=Symbol.for("react.strict_mode"),n0=Symbol.for("react.profiler"),r0=Symbol.for("react.provider"),o0=Symbol.for("react.context"),i0=Symbol.for("react.forward_ref"),s0=Symbol.for("react.suspense"),a0=Symbol.for("react.memo"),l0=Symbol.for("react.lazy"),Sd=Symbol.iterator;function u0(e){return e===null||typeof e!="object"?null:(e=Sd&&e[Sd]||e["@@iterator"],typeof e=="function"?e:null)}var sh={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ah=Object.assign,lh={};function po(e,t,n){this.props=e,this.context=t,this.refs=lh,this.updater=n||sh}po.prototype.isReactComponent={};po.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};po.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function uh(){}uh.prototype=po.prototype;function Gu(e,t,n){this.props=e,this.context=t,this.refs=lh,this.updater=n||sh}var Yu=Gu.prototype=new uh;Yu.constructor=Gu;ah(Yu,po.prototype);Yu.isPureReactComponent=!0;var bd=Array.isArray,ch=Object.prototype.hasOwnProperty,qu={current:null},dh={key:!0,ref:!0,__self:!0,__source:!0};function fh(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)ch.call(t,r)&&!dh.hasOwnProperty(r)&&(o[r]=t[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];o.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:bi,type:e,key:i,ref:s,props:o,_owner:qu.current}}function c0(e,t){return{$$typeof:bi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Xu(e){return typeof e=="object"&&e!==null&&e.$$typeof===bi}function d0(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Cd=/\/+/g;function Ha(e,t){return typeof e=="object"&&e!==null&&e.key!=null?d0(""+e.key):t.toString(36)}function cs(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case bi:case Jy:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+Ha(s,0):r,bd(o)?(n="",e!=null&&(n=e.replace(Cd,"$&/")+"/"),cs(o,t,n,"",function(u){return u})):o!=null&&(Xu(o)&&(o=c0(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(Cd,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",bd(e))for(var a=0;a<e.length;a++){i=e[a];var l=r+Ha(i,a);s+=cs(i,t,n,l,o)}else if(l=u0(e),typeof l=="function")for(e=l.call(e),a=0;!(i=e.next()).done;)i=i.value,l=r+Ha(i,a++),s+=cs(i,t,n,l,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Ii(e,t,n){if(e==null)return e;var r=[],o=0;return cs(e,r,"","",function(i){return t.call(n,i,o++)}),r}function f0(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ve={current:null},ds={transition:null},p0={ReactCurrentDispatcher:Ve,ReactCurrentBatchConfig:ds,ReactCurrentOwner:qu};function ph(){throw Error("act(...) is not supported in production builds of React.")}q.Children={map:Ii,forEach:function(e,t,n){Ii(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Ii(e,function(){t++}),t},toArray:function(e){return Ii(e,function(t){return t})||[]},only:function(e){if(!Xu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};q.Component=po;q.Fragment=e0;q.Profiler=n0;q.PureComponent=Gu;q.StrictMode=t0;q.Suspense=s0;q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=p0;q.act=ph;q.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=ah({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=qu.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)ch.call(t,l)&&!dh.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:bi,type:e.type,key:o,ref:i,props:r,_owner:s}};q.createContext=function(e){return e={$$typeof:o0,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:r0,_context:e},e.Consumer=e};q.createElement=fh;q.createFactory=function(e){var t=fh.bind(null,e);return t.type=e,t};q.createRef=function(){return{current:null}};q.forwardRef=function(e){return{$$typeof:i0,render:e}};q.isValidElement=Xu;q.lazy=function(e){return{$$typeof:l0,_payload:{_status:-1,_result:e},_init:f0}};q.memo=function(e,t){return{$$typeof:a0,type:e,compare:t===void 0?null:t}};q.startTransition=function(e){var t=ds.transition;ds.transition={};try{e()}finally{ds.transition=t}};q.unstable_act=ph;q.useCallback=function(e,t){return Ve.current.useCallback(e,t)};q.useContext=function(e){return Ve.current.useContext(e)};q.useDebugValue=function(){};q.useDeferredValue=function(e){return Ve.current.useDeferredValue(e)};q.useEffect=function(e,t){return Ve.current.useEffect(e,t)};q.useId=function(){return Ve.current.useId()};q.useImperativeHandle=function(e,t,n){return Ve.current.useImperativeHandle(e,t,n)};q.useInsertionEffect=function(e,t){return Ve.current.useInsertionEffect(e,t)};q.useLayoutEffect=function(e,t){return Ve.current.useLayoutEffect(e,t)};q.useMemo=function(e,t){return Ve.current.useMemo(e,t)};q.useReducer=function(e,t,n){return Ve.current.useReducer(e,t,n)};q.useRef=function(e){return Ve.current.useRef(e)};q.useState=function(e){return Ve.current.useState(e)};q.useSyncExternalStore=function(e,t,n){return Ve.current.useSyncExternalStore(e,t,n)};q.useTransition=function(){return Ve.current.useTransition()};q.version="18.3.1";ih.exports=q;var y=ih.exports;const M=rh(y),hh=Zy({__proto__:null,default:M},[y]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var h0=y,m0=Symbol.for("react.element"),g0=Symbol.for("react.fragment"),v0=Object.prototype.hasOwnProperty,y0=h0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,x0={key:!0,ref:!0,__self:!0,__source:!0};function mh(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)v0.call(t,r)&&!x0.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:m0,type:e,key:i,ref:s,props:o,_owner:y0.current}}la.Fragment=g0;la.jsx=mh;la.jsxs=mh;oh.exports=la;var p=oh.exports,gh={exports:{}},ot={},vh={exports:{}},yh={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(E,L){var F=E.length;E.push(L);e:for(;0<F;){var D=F-1>>>1,U=E[D];if(0<o(U,L))E[D]=L,E[F]=U,F=D;else break e}}function n(E){return E.length===0?null:E[0]}function r(E){if(E.length===0)return null;var L=E[0],F=E.pop();if(F!==L){E[0]=F;e:for(var D=0,U=E.length,W=U>>>1;D<W;){var ne=2*(D+1)-1,je=E[ne],J=ne+1,ht=E[J];if(0>o(je,F))J<U&&0>o(ht,je)?(E[D]=ht,E[J]=F,D=J):(E[D]=je,E[ne]=F,D=ne);else if(J<U&&0>o(ht,F))E[D]=ht,E[J]=F,D=J;else break e}}return L}function o(E,L){var F=E.sortIndex-L.sortIndex;return F!==0?F:E.id-L.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var l=[],u=[],d=1,f=null,c=3,v=!1,x=!1,h=!1,S=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function w(E){for(var L=n(u);L!==null;){if(L.callback===null)r(u);else if(L.startTime<=E)r(u),L.sortIndex=L.expirationTime,t(l,L);else break;L=n(u)}}function b(E){if(h=!1,w(E),!x)if(n(l)!==null)x=!0,z(C);else{var L=n(u);L!==null&&V(b,L.startTime-E)}}function C(E,L){x=!1,h&&(h=!1,g(P),P=-1),v=!0;var F=c;try{for(w(L),f=n(l);f!==null&&(!(f.expirationTime>L)||E&&!$());){var D=f.callback;if(typeof D=="function"){f.callback=null,c=f.priorityLevel;var U=D(f.expirationTime<=L);L=e.unstable_now(),typeof U=="function"?f.callback=U:f===n(l)&&r(l),w(L)}else r(l);f=n(l)}if(f!==null)var W=!0;else{var ne=n(u);ne!==null&&V(b,ne.startTime-L),W=!1}return W}finally{f=null,c=F,v=!1}}var k=!1,N=null,P=-1,O=5,R=-1;function $(){return!(e.unstable_now()-R<O)}function I(){if(N!==null){var E=e.unstable_now();R=E;var L=!0;try{L=N(!0,E)}finally{L?B():(k=!1,N=null)}}else k=!1}var B;if(typeof m=="function")B=function(){m(I)};else if(typeof MessageChannel<"u"){var A=new MessageChannel,G=A.port2;A.port1.onmessage=I,B=function(){G.postMessage(null)}}else B=function(){S(I,0)};function z(E){N=E,k||(k=!0,B())}function V(E,L){P=S(function(){E(e.unstable_now())},L)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(E){E.callback=null},e.unstable_continueExecution=function(){x||v||(x=!0,z(C))},e.unstable_forceFrameRate=function(E){0>E||125<E?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<E?Math.floor(1e3/E):5},e.unstable_getCurrentPriorityLevel=function(){return c},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(E){switch(c){case 1:case 2:case 3:var L=3;break;default:L=c}var F=c;c=L;try{return E()}finally{c=F}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(E,L){switch(E){case 1:case 2:case 3:case 4:case 5:break;default:E=3}var F=c;c=E;try{return L()}finally{c=F}},e.unstable_scheduleCallback=function(E,L,F){var D=e.unstable_now();switch(typeof F=="object"&&F!==null?(F=F.delay,F=typeof F=="number"&&0<F?D+F:D):F=D,E){case 1:var U=-1;break;case 2:U=250;break;case 5:U=**********;break;case 4:U=1e4;break;default:U=5e3}return U=F+U,E={id:d++,callback:L,priorityLevel:E,startTime:F,expirationTime:U,sortIndex:-1},F>D?(E.sortIndex=F,t(u,E),n(l)===null&&E===n(u)&&(h?(g(P),P=-1):h=!0,V(b,F-D))):(E.sortIndex=U,t(l,E),x||v||(x=!0,z(C))),E},e.unstable_shouldYield=$,e.unstable_wrapCallback=function(E){var L=c;return function(){var F=c;c=L;try{return E.apply(this,arguments)}finally{c=F}}}})(yh);vh.exports=yh;var w0=vh.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var S0=y,rt=w0;function j(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var xh=new Set,Go={};function cr(e,t){to(e,t),to(e+"Capture",t)}function to(e,t){for(Go[e]=t,e=0;e<t.length;e++)xh.add(t[e])}var Gt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ml=Object.prototype.hasOwnProperty,b0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ed={},kd={};function C0(e){return Ml.call(kd,e)?!0:Ml.call(Ed,e)?!1:b0.test(e)?kd[e]=!0:(Ed[e]=!0,!1)}function E0(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function k0(e,t,n,r){if(t===null||typeof t>"u"||E0(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function He(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var Re={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Re[e]=new He(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Re[t]=new He(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Re[e]=new He(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Re[e]=new He(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Re[e]=new He(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Re[e]=new He(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Re[e]=new He(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Re[e]=new He(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Re[e]=new He(e,5,!1,e.toLowerCase(),null,!1,!1)});var Zu=/[\-:]([a-z])/g;function Ju(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Zu,Ju);Re[t]=new He(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Zu,Ju);Re[t]=new He(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Zu,Ju);Re[t]=new He(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Re[e]=new He(e,1,!1,e.toLowerCase(),null,!1,!1)});Re.xlinkHref=new He("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Re[e]=new He(e,1,!1,e.toLowerCase(),null,!0,!0)});function ec(e,t,n,r){var o=Re.hasOwnProperty(t)?Re[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(k0(t,n,o,r)&&(n=null),r||o===null?C0(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var en=S0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Fi=Symbol.for("react.element"),kr=Symbol.for("react.portal"),Nr=Symbol.for("react.fragment"),tc=Symbol.for("react.strict_mode"),Dl=Symbol.for("react.profiler"),wh=Symbol.for("react.provider"),Sh=Symbol.for("react.context"),nc=Symbol.for("react.forward_ref"),_l=Symbol.for("react.suspense"),Il=Symbol.for("react.suspense_list"),rc=Symbol.for("react.memo"),fn=Symbol.for("react.lazy"),bh=Symbol.for("react.offscreen"),Nd=Symbol.iterator;function bo(e){return e===null||typeof e!="object"?null:(e=Nd&&e[Nd]||e["@@iterator"],typeof e=="function"?e:null)}var pe=Object.assign,Wa;function Ao(e){if(Wa===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Wa=t&&t[1]||""}return`
`+Wa+e}var Ka=!1;function Qa(e,t){if(!e||Ka)return"";Ka=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,a=i.length-1;1<=s&&0<=a&&o[s]!==i[a];)a--;for(;1<=s&&0<=a;s--,a--)if(o[s]!==i[a]){if(s!==1||a!==1)do if(s--,a--,0>a||o[s]!==i[a]){var l=`
`+o[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=s&&0<=a);break}}}finally{Ka=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Ao(e):""}function N0(e){switch(e.tag){case 5:return Ao(e.type);case 16:return Ao("Lazy");case 13:return Ao("Suspense");case 19:return Ao("SuspenseList");case 0:case 2:case 15:return e=Qa(e.type,!1),e;case 11:return e=Qa(e.type.render,!1),e;case 1:return e=Qa(e.type,!0),e;default:return""}}function Fl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Nr:return"Fragment";case kr:return"Portal";case Dl:return"Profiler";case tc:return"StrictMode";case _l:return"Suspense";case Il:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Sh:return(e.displayName||"Context")+".Consumer";case wh:return(e._context.displayName||"Context")+".Provider";case nc:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case rc:return t=e.displayName||null,t!==null?t:Fl(e.type)||"Memo";case fn:t=e._payload,e=e._init;try{return Fl(e(t))}catch{}}return null}function P0(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Fl(t);case 8:return t===tc?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function An(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ch(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function T0(e){var t=Ch(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function $i(e){e._valueTracker||(e._valueTracker=T0(e))}function Eh(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Ch(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Ps(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function $l(e,t){var n=t.checked;return pe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Pd(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=An(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function kh(e,t){t=t.checked,t!=null&&ec(e,"checked",t,!1)}function zl(e,t){kh(e,t);var n=An(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ul(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ul(e,t.type,An(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Td(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ul(e,t,n){(t!=="number"||Ps(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Mo=Array.isArray;function Ir(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+An(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Bl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(j(91));return pe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Rd(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(j(92));if(Mo(n)){if(1<n.length)throw Error(j(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:An(n)}}function Nh(e,t){var n=An(t.value),r=An(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function jd(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Ph(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Vl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Ph(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var zi,Th=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(zi=zi||document.createElement("div"),zi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=zi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Yo(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Io={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},R0=["Webkit","ms","Moz","O"];Object.keys(Io).forEach(function(e){R0.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Io[t]=Io[e]})});function Rh(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Io.hasOwnProperty(e)&&Io[e]?(""+t).trim():t+"px"}function jh(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Rh(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var j0=pe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Hl(e,t){if(t){if(j0[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(j(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(j(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(j(61))}if(t.style!=null&&typeof t.style!="object")throw Error(j(62))}}function Wl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Kl=null;function oc(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ql=null,Fr=null,$r=null;function Od(e){if(e=ki(e)){if(typeof Ql!="function")throw Error(j(280));var t=e.stateNode;t&&(t=pa(t),Ql(e.stateNode,e.type,t))}}function Oh(e){Fr?$r?$r.push(e):$r=[e]:Fr=e}function Lh(){if(Fr){var e=Fr,t=$r;if($r=Fr=null,Od(e),t)for(e=0;e<t.length;e++)Od(t[e])}}function Ah(e,t){return e(t)}function Mh(){}var Ga=!1;function Dh(e,t,n){if(Ga)return e(t,n);Ga=!0;try{return Ah(e,t,n)}finally{Ga=!1,(Fr!==null||$r!==null)&&(Mh(),Lh())}}function qo(e,t){var n=e.stateNode;if(n===null)return null;var r=pa(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(j(231,t,typeof n));return n}var Gl=!1;if(Gt)try{var Co={};Object.defineProperty(Co,"passive",{get:function(){Gl=!0}}),window.addEventListener("test",Co,Co),window.removeEventListener("test",Co,Co)}catch{Gl=!1}function O0(e,t,n,r,o,i,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var Fo=!1,Ts=null,Rs=!1,Yl=null,L0={onError:function(e){Fo=!0,Ts=e}};function A0(e,t,n,r,o,i,s,a,l){Fo=!1,Ts=null,O0.apply(L0,arguments)}function M0(e,t,n,r,o,i,s,a,l){if(A0.apply(this,arguments),Fo){if(Fo){var u=Ts;Fo=!1,Ts=null}else throw Error(j(198));Rs||(Rs=!0,Yl=u)}}function dr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function _h(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ld(e){if(dr(e)!==e)throw Error(j(188))}function D0(e){var t=e.alternate;if(!t){if(t=dr(e),t===null)throw Error(j(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Ld(o),e;if(i===r)return Ld(o),t;i=i.sibling}throw Error(j(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s){for(a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s)throw Error(j(189))}}if(n.alternate!==r)throw Error(j(190))}if(n.tag!==3)throw Error(j(188));return n.stateNode.current===n?e:t}function Ih(e){return e=D0(e),e!==null?Fh(e):null}function Fh(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Fh(e);if(t!==null)return t;e=e.sibling}return null}var $h=rt.unstable_scheduleCallback,Ad=rt.unstable_cancelCallback,_0=rt.unstable_shouldYield,I0=rt.unstable_requestPaint,ge=rt.unstable_now,F0=rt.unstable_getCurrentPriorityLevel,ic=rt.unstable_ImmediatePriority,zh=rt.unstable_UserBlockingPriority,js=rt.unstable_NormalPriority,$0=rt.unstable_LowPriority,Uh=rt.unstable_IdlePriority,ua=null,It=null;function z0(e){if(It&&typeof It.onCommitFiberRoot=="function")try{It.onCommitFiberRoot(ua,e,void 0,(e.current.flags&128)===128)}catch{}}var St=Math.clz32?Math.clz32:V0,U0=Math.log,B0=Math.LN2;function V0(e){return e>>>=0,e===0?32:31-(U0(e)/B0|0)|0}var Ui=64,Bi=4194304;function Do(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Os(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~o;a!==0?r=Do(a):(i&=s,i!==0&&(r=Do(i)))}else s=n&~o,s!==0?r=Do(s):i!==0&&(r=Do(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-St(t),o=1<<n,r|=e[n],t&=~o;return r}function H0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function W0(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-St(i),a=1<<s,l=o[s];l===-1?(!(a&n)||a&r)&&(o[s]=H0(a,t)):l<=t&&(e.expiredLanes|=a),i&=~a}}function ql(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Bh(){var e=Ui;return Ui<<=1,!(Ui&4194240)&&(Ui=64),e}function Ya(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ci(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-St(t),e[t]=n}function K0(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-St(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function sc(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-St(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var ee=0;function Vh(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Hh,ac,Wh,Kh,Qh,Xl=!1,Vi=[],En=null,kn=null,Nn=null,Xo=new Map,Zo=new Map,hn=[],Q0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Md(e,t){switch(e){case"focusin":case"focusout":En=null;break;case"dragenter":case"dragleave":kn=null;break;case"mouseover":case"mouseout":Nn=null;break;case"pointerover":case"pointerout":Xo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Zo.delete(t.pointerId)}}function Eo(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=ki(t),t!==null&&ac(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function G0(e,t,n,r,o){switch(t){case"focusin":return En=Eo(En,e,t,n,r,o),!0;case"dragenter":return kn=Eo(kn,e,t,n,r,o),!0;case"mouseover":return Nn=Eo(Nn,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return Xo.set(i,Eo(Xo.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Zo.set(i,Eo(Zo.get(i)||null,e,t,n,r,o)),!0}return!1}function Gh(e){var t=Qn(e.target);if(t!==null){var n=dr(t);if(n!==null){if(t=n.tag,t===13){if(t=_h(n),t!==null){e.blockedOn=t,Qh(e.priority,function(){Wh(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function fs(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Zl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Kl=r,n.target.dispatchEvent(r),Kl=null}else return t=ki(n),t!==null&&ac(t),e.blockedOn=n,!1;t.shift()}return!0}function Dd(e,t,n){fs(e)&&n.delete(t)}function Y0(){Xl=!1,En!==null&&fs(En)&&(En=null),kn!==null&&fs(kn)&&(kn=null),Nn!==null&&fs(Nn)&&(Nn=null),Xo.forEach(Dd),Zo.forEach(Dd)}function ko(e,t){e.blockedOn===t&&(e.blockedOn=null,Xl||(Xl=!0,rt.unstable_scheduleCallback(rt.unstable_NormalPriority,Y0)))}function Jo(e){function t(o){return ko(o,e)}if(0<Vi.length){ko(Vi[0],e);for(var n=1;n<Vi.length;n++){var r=Vi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(En!==null&&ko(En,e),kn!==null&&ko(kn,e),Nn!==null&&ko(Nn,e),Xo.forEach(t),Zo.forEach(t),n=0;n<hn.length;n++)r=hn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<hn.length&&(n=hn[0],n.blockedOn===null);)Gh(n),n.blockedOn===null&&hn.shift()}var zr=en.ReactCurrentBatchConfig,Ls=!0;function q0(e,t,n,r){var o=ee,i=zr.transition;zr.transition=null;try{ee=1,lc(e,t,n,r)}finally{ee=o,zr.transition=i}}function X0(e,t,n,r){var o=ee,i=zr.transition;zr.transition=null;try{ee=4,lc(e,t,n,r)}finally{ee=o,zr.transition=i}}function lc(e,t,n,r){if(Ls){var o=Zl(e,t,n,r);if(o===null)il(e,t,r,As,n),Md(e,r);else if(G0(o,e,t,n,r))r.stopPropagation();else if(Md(e,r),t&4&&-1<Q0.indexOf(e)){for(;o!==null;){var i=ki(o);if(i!==null&&Hh(i),i=Zl(e,t,n,r),i===null&&il(e,t,r,As,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else il(e,t,r,null,n)}}var As=null;function Zl(e,t,n,r){if(As=null,e=oc(r),e=Qn(e),e!==null)if(t=dr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=_h(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return As=e,null}function Yh(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(F0()){case ic:return 1;case zh:return 4;case js:case $0:return 16;case Uh:return 536870912;default:return 16}default:return 16}}var Sn=null,uc=null,ps=null;function qh(){if(ps)return ps;var e,t=uc,n=t.length,r,o="value"in Sn?Sn.value:Sn.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return ps=o.slice(e,1<r?1-r:void 0)}function hs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Hi(){return!0}function _d(){return!1}function it(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Hi:_d,this.isPropagationStopped=_d,this}return pe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Hi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Hi)},persist:function(){},isPersistent:Hi}),t}var ho={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cc=it(ho),Ei=pe({},ho,{view:0,detail:0}),Z0=it(Ei),qa,Xa,No,ca=pe({},Ei,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:dc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==No&&(No&&e.type==="mousemove"?(qa=e.screenX-No.screenX,Xa=e.screenY-No.screenY):Xa=qa=0,No=e),qa)},movementY:function(e){return"movementY"in e?e.movementY:Xa}}),Id=it(ca),J0=pe({},ca,{dataTransfer:0}),ex=it(J0),tx=pe({},Ei,{relatedTarget:0}),Za=it(tx),nx=pe({},ho,{animationName:0,elapsedTime:0,pseudoElement:0}),rx=it(nx),ox=pe({},ho,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ix=it(ox),sx=pe({},ho,{data:0}),Fd=it(sx),ax={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},lx={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ux={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function cx(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=ux[e])?!!t[e]:!1}function dc(){return cx}var dx=pe({},Ei,{key:function(e){if(e.key){var t=ax[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=hs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?lx[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:dc,charCode:function(e){return e.type==="keypress"?hs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?hs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),fx=it(dx),px=pe({},ca,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),$d=it(px),hx=pe({},Ei,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:dc}),mx=it(hx),gx=pe({},ho,{propertyName:0,elapsedTime:0,pseudoElement:0}),vx=it(gx),yx=pe({},ca,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),xx=it(yx),wx=[9,13,27,32],fc=Gt&&"CompositionEvent"in window,$o=null;Gt&&"documentMode"in document&&($o=document.documentMode);var Sx=Gt&&"TextEvent"in window&&!$o,Xh=Gt&&(!fc||$o&&8<$o&&11>=$o),zd=" ",Ud=!1;function Zh(e,t){switch(e){case"keyup":return wx.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Jh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Pr=!1;function bx(e,t){switch(e){case"compositionend":return Jh(t);case"keypress":return t.which!==32?null:(Ud=!0,zd);case"textInput":return e=t.data,e===zd&&Ud?null:e;default:return null}}function Cx(e,t){if(Pr)return e==="compositionend"||!fc&&Zh(e,t)?(e=qh(),ps=uc=Sn=null,Pr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Xh&&t.locale!=="ko"?null:t.data;default:return null}}var Ex={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Bd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Ex[e.type]:t==="textarea"}function em(e,t,n,r){Oh(r),t=Ms(t,"onChange"),0<t.length&&(n=new cc("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var zo=null,ei=null;function kx(e){dm(e,0)}function da(e){var t=jr(e);if(Eh(t))return e}function Nx(e,t){if(e==="change")return t}var tm=!1;if(Gt){var Ja;if(Gt){var el="oninput"in document;if(!el){var Vd=document.createElement("div");Vd.setAttribute("oninput","return;"),el=typeof Vd.oninput=="function"}Ja=el}else Ja=!1;tm=Ja&&(!document.documentMode||9<document.documentMode)}function Hd(){zo&&(zo.detachEvent("onpropertychange",nm),ei=zo=null)}function nm(e){if(e.propertyName==="value"&&da(ei)){var t=[];em(t,ei,e,oc(e)),Dh(kx,t)}}function Px(e,t,n){e==="focusin"?(Hd(),zo=t,ei=n,zo.attachEvent("onpropertychange",nm)):e==="focusout"&&Hd()}function Tx(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return da(ei)}function Rx(e,t){if(e==="click")return da(t)}function jx(e,t){if(e==="input"||e==="change")return da(t)}function Ox(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ct=typeof Object.is=="function"?Object.is:Ox;function ti(e,t){if(Ct(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Ml.call(t,o)||!Ct(e[o],t[o]))return!1}return!0}function Wd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Kd(e,t){var n=Wd(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Wd(n)}}function rm(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?rm(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function om(){for(var e=window,t=Ps();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ps(e.document)}return t}function pc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Lx(e){var t=om(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&rm(n.ownerDocument.documentElement,n)){if(r!==null&&pc(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Kd(n,i);var s=Kd(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Ax=Gt&&"documentMode"in document&&11>=document.documentMode,Tr=null,Jl=null,Uo=null,eu=!1;function Qd(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;eu||Tr==null||Tr!==Ps(r)||(r=Tr,"selectionStart"in r&&pc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Uo&&ti(Uo,r)||(Uo=r,r=Ms(Jl,"onSelect"),0<r.length&&(t=new cc("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Tr)))}function Wi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Rr={animationend:Wi("Animation","AnimationEnd"),animationiteration:Wi("Animation","AnimationIteration"),animationstart:Wi("Animation","AnimationStart"),transitionend:Wi("Transition","TransitionEnd")},tl={},im={};Gt&&(im=document.createElement("div").style,"AnimationEvent"in window||(delete Rr.animationend.animation,delete Rr.animationiteration.animation,delete Rr.animationstart.animation),"TransitionEvent"in window||delete Rr.transitionend.transition);function fa(e){if(tl[e])return tl[e];if(!Rr[e])return e;var t=Rr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in im)return tl[e]=t[n];return e}var sm=fa("animationend"),am=fa("animationiteration"),lm=fa("animationstart"),um=fa("transitionend"),cm=new Map,Gd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function zn(e,t){cm.set(e,t),cr(t,[e])}for(var nl=0;nl<Gd.length;nl++){var rl=Gd[nl],Mx=rl.toLowerCase(),Dx=rl[0].toUpperCase()+rl.slice(1);zn(Mx,"on"+Dx)}zn(sm,"onAnimationEnd");zn(am,"onAnimationIteration");zn(lm,"onAnimationStart");zn("dblclick","onDoubleClick");zn("focusin","onFocus");zn("focusout","onBlur");zn(um,"onTransitionEnd");to("onMouseEnter",["mouseout","mouseover"]);to("onMouseLeave",["mouseout","mouseover"]);to("onPointerEnter",["pointerout","pointerover"]);to("onPointerLeave",["pointerout","pointerover"]);cr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));cr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));cr("onBeforeInput",["compositionend","keypress","textInput","paste"]);cr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));cr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));cr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var _o="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),_x=new Set("cancel close invalid load scroll toggle".split(" ").concat(_o));function Yd(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,M0(r,t,void 0,e),e.currentTarget=null}function dm(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==i&&o.isPropagationStopped())break e;Yd(o,a,u),i=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,u=a.currentTarget,a=a.listener,l!==i&&o.isPropagationStopped())break e;Yd(o,a,u),i=l}}}if(Rs)throw e=Yl,Rs=!1,Yl=null,e}function ae(e,t){var n=t[iu];n===void 0&&(n=t[iu]=new Set);var r=e+"__bubble";n.has(r)||(fm(t,e,2,!1),n.add(r))}function ol(e,t,n){var r=0;t&&(r|=4),fm(n,e,r,t)}var Ki="_reactListening"+Math.random().toString(36).slice(2);function ni(e){if(!e[Ki]){e[Ki]=!0,xh.forEach(function(n){n!=="selectionchange"&&(_x.has(n)||ol(n,!1,e),ol(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ki]||(t[Ki]=!0,ol("selectionchange",!1,t))}}function fm(e,t,n,r){switch(Yh(t)){case 1:var o=q0;break;case 4:o=X0;break;default:o=lc}n=o.bind(null,t,n,e),o=void 0,!Gl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function il(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===o||l.nodeType===8&&l.parentNode===o))return;s=s.return}for(;a!==null;){if(s=Qn(a),s===null)return;if(l=s.tag,l===5||l===6){r=i=s;continue e}a=a.parentNode}}r=r.return}Dh(function(){var u=i,d=oc(n),f=[];e:{var c=cm.get(e);if(c!==void 0){var v=cc,x=e;switch(e){case"keypress":if(hs(n)===0)break e;case"keydown":case"keyup":v=fx;break;case"focusin":x="focus",v=Za;break;case"focusout":x="blur",v=Za;break;case"beforeblur":case"afterblur":v=Za;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=Id;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=ex;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=mx;break;case sm:case am:case lm:v=rx;break;case um:v=vx;break;case"scroll":v=Z0;break;case"wheel":v=xx;break;case"copy":case"cut":case"paste":v=ix;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=$d}var h=(t&4)!==0,S=!h&&e==="scroll",g=h?c!==null?c+"Capture":null:c;h=[];for(var m=u,w;m!==null;){w=m;var b=w.stateNode;if(w.tag===5&&b!==null&&(w=b,g!==null&&(b=qo(m,g),b!=null&&h.push(ri(m,b,w)))),S)break;m=m.return}0<h.length&&(c=new v(c,x,null,n,d),f.push({event:c,listeners:h}))}}if(!(t&7)){e:{if(c=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",c&&n!==Kl&&(x=n.relatedTarget||n.fromElement)&&(Qn(x)||x[Yt]))break e;if((v||c)&&(c=d.window===d?d:(c=d.ownerDocument)?c.defaultView||c.parentWindow:window,v?(x=n.relatedTarget||n.toElement,v=u,x=x?Qn(x):null,x!==null&&(S=dr(x),x!==S||x.tag!==5&&x.tag!==6)&&(x=null)):(v=null,x=u),v!==x)){if(h=Id,b="onMouseLeave",g="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(h=$d,b="onPointerLeave",g="onPointerEnter",m="pointer"),S=v==null?c:jr(v),w=x==null?c:jr(x),c=new h(b,m+"leave",v,n,d),c.target=S,c.relatedTarget=w,b=null,Qn(d)===u&&(h=new h(g,m+"enter",x,n,d),h.target=w,h.relatedTarget=S,b=h),S=b,v&&x)t:{for(h=v,g=x,m=0,w=h;w;w=yr(w))m++;for(w=0,b=g;b;b=yr(b))w++;for(;0<m-w;)h=yr(h),m--;for(;0<w-m;)g=yr(g),w--;for(;m--;){if(h===g||g!==null&&h===g.alternate)break t;h=yr(h),g=yr(g)}h=null}else h=null;v!==null&&qd(f,c,v,h,!1),x!==null&&S!==null&&qd(f,S,x,h,!0)}}e:{if(c=u?jr(u):window,v=c.nodeName&&c.nodeName.toLowerCase(),v==="select"||v==="input"&&c.type==="file")var C=Nx;else if(Bd(c))if(tm)C=jx;else{C=Tx;var k=Px}else(v=c.nodeName)&&v.toLowerCase()==="input"&&(c.type==="checkbox"||c.type==="radio")&&(C=Rx);if(C&&(C=C(e,u))){em(f,C,n,d);break e}k&&k(e,c,u),e==="focusout"&&(k=c._wrapperState)&&k.controlled&&c.type==="number"&&Ul(c,"number",c.value)}switch(k=u?jr(u):window,e){case"focusin":(Bd(k)||k.contentEditable==="true")&&(Tr=k,Jl=u,Uo=null);break;case"focusout":Uo=Jl=Tr=null;break;case"mousedown":eu=!0;break;case"contextmenu":case"mouseup":case"dragend":eu=!1,Qd(f,n,d);break;case"selectionchange":if(Ax)break;case"keydown":case"keyup":Qd(f,n,d)}var N;if(fc)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else Pr?Zh(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(Xh&&n.locale!=="ko"&&(Pr||P!=="onCompositionStart"?P==="onCompositionEnd"&&Pr&&(N=qh()):(Sn=d,uc="value"in Sn?Sn.value:Sn.textContent,Pr=!0)),k=Ms(u,P),0<k.length&&(P=new Fd(P,e,null,n,d),f.push({event:P,listeners:k}),N?P.data=N:(N=Jh(n),N!==null&&(P.data=N)))),(N=Sx?bx(e,n):Cx(e,n))&&(u=Ms(u,"onBeforeInput"),0<u.length&&(d=new Fd("onBeforeInput","beforeinput",null,n,d),f.push({event:d,listeners:u}),d.data=N))}dm(f,t)})}function ri(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ms(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=qo(e,n),i!=null&&r.unshift(ri(e,i,o)),i=qo(e,t),i!=null&&r.push(ri(e,i,o))),e=e.return}return r}function yr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function qd(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,o?(l=qo(n,i),l!=null&&s.unshift(ri(n,l,a))):o||(l=qo(n,i),l!=null&&s.push(ri(n,l,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var Ix=/\r\n?/g,Fx=/\u0000|\uFFFD/g;function Xd(e){return(typeof e=="string"?e:""+e).replace(Ix,`
`).replace(Fx,"")}function Qi(e,t,n){if(t=Xd(t),Xd(e)!==t&&n)throw Error(j(425))}function Ds(){}var tu=null,nu=null;function ru(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ou=typeof setTimeout=="function"?setTimeout:void 0,$x=typeof clearTimeout=="function"?clearTimeout:void 0,Zd=typeof Promise=="function"?Promise:void 0,zx=typeof queueMicrotask=="function"?queueMicrotask:typeof Zd<"u"?function(e){return Zd.resolve(null).then(e).catch(Ux)}:ou;function Ux(e){setTimeout(function(){throw e})}function sl(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Jo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Jo(t)}function Pn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Jd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var mo=Math.random().toString(36).slice(2),Mt="__reactFiber$"+mo,oi="__reactProps$"+mo,Yt="__reactContainer$"+mo,iu="__reactEvents$"+mo,Bx="__reactListeners$"+mo,Vx="__reactHandles$"+mo;function Qn(e){var t=e[Mt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Yt]||n[Mt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Jd(e);e!==null;){if(n=e[Mt])return n;e=Jd(e)}return t}e=n,n=e.parentNode}return null}function ki(e){return e=e[Mt]||e[Yt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function jr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(j(33))}function pa(e){return e[oi]||null}var su=[],Or=-1;function Un(e){return{current:e}}function le(e){0>Or||(e.current=su[Or],su[Or]=null,Or--)}function oe(e,t){Or++,su[Or]=e.current,e.current=t}var Mn={},_e=Un(Mn),Qe=Un(!1),rr=Mn;function no(e,t){var n=e.type.contextTypes;if(!n)return Mn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Ge(e){return e=e.childContextTypes,e!=null}function _s(){le(Qe),le(_e)}function ef(e,t,n){if(_e.current!==Mn)throw Error(j(168));oe(_e,t),oe(Qe,n)}function pm(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(j(108,P0(e)||"Unknown",o));return pe({},n,r)}function Is(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Mn,rr=_e.current,oe(_e,e),oe(Qe,Qe.current),!0}function tf(e,t,n){var r=e.stateNode;if(!r)throw Error(j(169));n?(e=pm(e,t,rr),r.__reactInternalMemoizedMergedChildContext=e,le(Qe),le(_e),oe(_e,e)):le(Qe),oe(Qe,n)}var Ht=null,ha=!1,al=!1;function hm(e){Ht===null?Ht=[e]:Ht.push(e)}function Hx(e){ha=!0,hm(e)}function Bn(){if(!al&&Ht!==null){al=!0;var e=0,t=ee;try{var n=Ht;for(ee=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Ht=null,ha=!1}catch(o){throw Ht!==null&&(Ht=Ht.slice(e+1)),$h(ic,Bn),o}finally{ee=t,al=!1}}return null}var Lr=[],Ar=0,Fs=null,$s=0,at=[],lt=0,or=null,Wt=1,Kt="";function Wn(e,t){Lr[Ar++]=$s,Lr[Ar++]=Fs,Fs=e,$s=t}function mm(e,t,n){at[lt++]=Wt,at[lt++]=Kt,at[lt++]=or,or=e;var r=Wt;e=Kt;var o=32-St(r)-1;r&=~(1<<o),n+=1;var i=32-St(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,Wt=1<<32-St(t)+o|n<<o|r,Kt=i+e}else Wt=1<<i|n<<o|r,Kt=e}function hc(e){e.return!==null&&(Wn(e,1),mm(e,1,0))}function mc(e){for(;e===Fs;)Fs=Lr[--Ar],Lr[Ar]=null,$s=Lr[--Ar],Lr[Ar]=null;for(;e===or;)or=at[--lt],at[lt]=null,Kt=at[--lt],at[lt]=null,Wt=at[--lt],at[lt]=null}var tt=null,et=null,ue=!1,wt=null;function gm(e,t){var n=ut(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function nf(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,tt=e,et=Pn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,tt=e,et=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=or!==null?{id:Wt,overflow:Kt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ut(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,tt=e,et=null,!0):!1;default:return!1}}function au(e){return(e.mode&1)!==0&&(e.flags&128)===0}function lu(e){if(ue){var t=et;if(t){var n=t;if(!nf(e,t)){if(au(e))throw Error(j(418));t=Pn(n.nextSibling);var r=tt;t&&nf(e,t)?gm(r,n):(e.flags=e.flags&-4097|2,ue=!1,tt=e)}}else{if(au(e))throw Error(j(418));e.flags=e.flags&-4097|2,ue=!1,tt=e}}}function rf(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;tt=e}function Gi(e){if(e!==tt)return!1;if(!ue)return rf(e),ue=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ru(e.type,e.memoizedProps)),t&&(t=et)){if(au(e))throw vm(),Error(j(418));for(;t;)gm(e,t),t=Pn(t.nextSibling)}if(rf(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(j(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){et=Pn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}et=null}}else et=tt?Pn(e.stateNode.nextSibling):null;return!0}function vm(){for(var e=et;e;)e=Pn(e.nextSibling)}function ro(){et=tt=null,ue=!1}function gc(e){wt===null?wt=[e]:wt.push(e)}var Wx=en.ReactCurrentBatchConfig;function Po(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(j(309));var r=n.stateNode}if(!r)throw Error(j(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var a=o.refs;s===null?delete a[i]:a[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(j(284));if(!n._owner)throw Error(j(290,e))}return e}function Yi(e,t){throw e=Object.prototype.toString.call(t),Error(j(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function of(e){var t=e._init;return t(e._payload)}function ym(e){function t(g,m){if(e){var w=g.deletions;w===null?(g.deletions=[m],g.flags|=16):w.push(m)}}function n(g,m){if(!e)return null;for(;m!==null;)t(g,m),m=m.sibling;return null}function r(g,m){for(g=new Map;m!==null;)m.key!==null?g.set(m.key,m):g.set(m.index,m),m=m.sibling;return g}function o(g,m){return g=On(g,m),g.index=0,g.sibling=null,g}function i(g,m,w){return g.index=w,e?(w=g.alternate,w!==null?(w=w.index,w<m?(g.flags|=2,m):w):(g.flags|=2,m)):(g.flags|=1048576,m)}function s(g){return e&&g.alternate===null&&(g.flags|=2),g}function a(g,m,w,b){return m===null||m.tag!==6?(m=hl(w,g.mode,b),m.return=g,m):(m=o(m,w),m.return=g,m)}function l(g,m,w,b){var C=w.type;return C===Nr?d(g,m,w.props.children,b,w.key):m!==null&&(m.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===fn&&of(C)===m.type)?(b=o(m,w.props),b.ref=Po(g,m,w),b.return=g,b):(b=Ss(w.type,w.key,w.props,null,g.mode,b),b.ref=Po(g,m,w),b.return=g,b)}function u(g,m,w,b){return m===null||m.tag!==4||m.stateNode.containerInfo!==w.containerInfo||m.stateNode.implementation!==w.implementation?(m=ml(w,g.mode,b),m.return=g,m):(m=o(m,w.children||[]),m.return=g,m)}function d(g,m,w,b,C){return m===null||m.tag!==7?(m=tr(w,g.mode,b,C),m.return=g,m):(m=o(m,w),m.return=g,m)}function f(g,m,w){if(typeof m=="string"&&m!==""||typeof m=="number")return m=hl(""+m,g.mode,w),m.return=g,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case Fi:return w=Ss(m.type,m.key,m.props,null,g.mode,w),w.ref=Po(g,null,m),w.return=g,w;case kr:return m=ml(m,g.mode,w),m.return=g,m;case fn:var b=m._init;return f(g,b(m._payload),w)}if(Mo(m)||bo(m))return m=tr(m,g.mode,w,null),m.return=g,m;Yi(g,m)}return null}function c(g,m,w,b){var C=m!==null?m.key:null;if(typeof w=="string"&&w!==""||typeof w=="number")return C!==null?null:a(g,m,""+w,b);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case Fi:return w.key===C?l(g,m,w,b):null;case kr:return w.key===C?u(g,m,w,b):null;case fn:return C=w._init,c(g,m,C(w._payload),b)}if(Mo(w)||bo(w))return C!==null?null:d(g,m,w,b,null);Yi(g,w)}return null}function v(g,m,w,b,C){if(typeof b=="string"&&b!==""||typeof b=="number")return g=g.get(w)||null,a(m,g,""+b,C);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case Fi:return g=g.get(b.key===null?w:b.key)||null,l(m,g,b,C);case kr:return g=g.get(b.key===null?w:b.key)||null,u(m,g,b,C);case fn:var k=b._init;return v(g,m,w,k(b._payload),C)}if(Mo(b)||bo(b))return g=g.get(w)||null,d(m,g,b,C,null);Yi(m,b)}return null}function x(g,m,w,b){for(var C=null,k=null,N=m,P=m=0,O=null;N!==null&&P<w.length;P++){N.index>P?(O=N,N=null):O=N.sibling;var R=c(g,N,w[P],b);if(R===null){N===null&&(N=O);break}e&&N&&R.alternate===null&&t(g,N),m=i(R,m,P),k===null?C=R:k.sibling=R,k=R,N=O}if(P===w.length)return n(g,N),ue&&Wn(g,P),C;if(N===null){for(;P<w.length;P++)N=f(g,w[P],b),N!==null&&(m=i(N,m,P),k===null?C=N:k.sibling=N,k=N);return ue&&Wn(g,P),C}for(N=r(g,N);P<w.length;P++)O=v(N,g,P,w[P],b),O!==null&&(e&&O.alternate!==null&&N.delete(O.key===null?P:O.key),m=i(O,m,P),k===null?C=O:k.sibling=O,k=O);return e&&N.forEach(function($){return t(g,$)}),ue&&Wn(g,P),C}function h(g,m,w,b){var C=bo(w);if(typeof C!="function")throw Error(j(150));if(w=C.call(w),w==null)throw Error(j(151));for(var k=C=null,N=m,P=m=0,O=null,R=w.next();N!==null&&!R.done;P++,R=w.next()){N.index>P?(O=N,N=null):O=N.sibling;var $=c(g,N,R.value,b);if($===null){N===null&&(N=O);break}e&&N&&$.alternate===null&&t(g,N),m=i($,m,P),k===null?C=$:k.sibling=$,k=$,N=O}if(R.done)return n(g,N),ue&&Wn(g,P),C;if(N===null){for(;!R.done;P++,R=w.next())R=f(g,R.value,b),R!==null&&(m=i(R,m,P),k===null?C=R:k.sibling=R,k=R);return ue&&Wn(g,P),C}for(N=r(g,N);!R.done;P++,R=w.next())R=v(N,g,P,R.value,b),R!==null&&(e&&R.alternate!==null&&N.delete(R.key===null?P:R.key),m=i(R,m,P),k===null?C=R:k.sibling=R,k=R);return e&&N.forEach(function(I){return t(g,I)}),ue&&Wn(g,P),C}function S(g,m,w,b){if(typeof w=="object"&&w!==null&&w.type===Nr&&w.key===null&&(w=w.props.children),typeof w=="object"&&w!==null){switch(w.$$typeof){case Fi:e:{for(var C=w.key,k=m;k!==null;){if(k.key===C){if(C=w.type,C===Nr){if(k.tag===7){n(g,k.sibling),m=o(k,w.props.children),m.return=g,g=m;break e}}else if(k.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===fn&&of(C)===k.type){n(g,k.sibling),m=o(k,w.props),m.ref=Po(g,k,w),m.return=g,g=m;break e}n(g,k);break}else t(g,k);k=k.sibling}w.type===Nr?(m=tr(w.props.children,g.mode,b,w.key),m.return=g,g=m):(b=Ss(w.type,w.key,w.props,null,g.mode,b),b.ref=Po(g,m,w),b.return=g,g=b)}return s(g);case kr:e:{for(k=w.key;m!==null;){if(m.key===k)if(m.tag===4&&m.stateNode.containerInfo===w.containerInfo&&m.stateNode.implementation===w.implementation){n(g,m.sibling),m=o(m,w.children||[]),m.return=g,g=m;break e}else{n(g,m);break}else t(g,m);m=m.sibling}m=ml(w,g.mode,b),m.return=g,g=m}return s(g);case fn:return k=w._init,S(g,m,k(w._payload),b)}if(Mo(w))return x(g,m,w,b);if(bo(w))return h(g,m,w,b);Yi(g,w)}return typeof w=="string"&&w!==""||typeof w=="number"?(w=""+w,m!==null&&m.tag===6?(n(g,m.sibling),m=o(m,w),m.return=g,g=m):(n(g,m),m=hl(w,g.mode,b),m.return=g,g=m),s(g)):n(g,m)}return S}var oo=ym(!0),xm=ym(!1),zs=Un(null),Us=null,Mr=null,vc=null;function yc(){vc=Mr=Us=null}function xc(e){var t=zs.current;le(zs),e._currentValue=t}function uu(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ur(e,t){Us=e,vc=Mr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ke=!0),e.firstContext=null)}function dt(e){var t=e._currentValue;if(vc!==e)if(e={context:e,memoizedValue:t,next:null},Mr===null){if(Us===null)throw Error(j(308));Mr=e,Us.dependencies={lanes:0,firstContext:e}}else Mr=Mr.next=e;return t}var Gn=null;function wc(e){Gn===null?Gn=[e]:Gn.push(e)}function wm(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,wc(t)):(n.next=o.next,o.next=n),t.interleaved=n,qt(e,r)}function qt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var pn=!1;function Sc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Sm(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Qt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Tn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,X&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,qt(e,n)}return o=r.interleaved,o===null?(t.next=t,wc(r)):(t.next=o.next,o.next=t),r.interleaved=t,qt(e,n)}function ms(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,sc(e,n)}}function sf(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Bs(e,t,n,r){var o=e.updateQueue;pn=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var l=a,u=l.next;l.next=null,s===null?i=u:s.next=u,s=l;var d=e.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==s&&(a===null?d.firstBaseUpdate=u:a.next=u,d.lastBaseUpdate=l))}if(i!==null){var f=o.baseState;s=0,d=u=l=null,a=i;do{var c=a.lane,v=a.eventTime;if((r&c)===c){d!==null&&(d=d.next={eventTime:v,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var x=e,h=a;switch(c=t,v=n,h.tag){case 1:if(x=h.payload,typeof x=="function"){f=x.call(v,f,c);break e}f=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=h.payload,c=typeof x=="function"?x.call(v,f,c):x,c==null)break e;f=pe({},f,c);break e;case 2:pn=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,c=o.effects,c===null?o.effects=[a]:c.push(a))}else v={eventTime:v,lane:c,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(u=d=v,l=f):d=d.next=v,s|=c;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;c=a,a=c.next,c.next=null,o.lastBaseUpdate=c,o.shared.pending=null}}while(!0);if(d===null&&(l=f),o.baseState=l,o.firstBaseUpdate=u,o.lastBaseUpdate=d,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);sr|=s,e.lanes=s,e.memoizedState=f}}function af(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(j(191,o));o.call(r)}}}var Ni={},Ft=Un(Ni),ii=Un(Ni),si=Un(Ni);function Yn(e){if(e===Ni)throw Error(j(174));return e}function bc(e,t){switch(oe(si,t),oe(ii,e),oe(Ft,Ni),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Vl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Vl(t,e)}le(Ft),oe(Ft,t)}function io(){le(Ft),le(ii),le(si)}function bm(e){Yn(si.current);var t=Yn(Ft.current),n=Vl(t,e.type);t!==n&&(oe(ii,e),oe(Ft,n))}function Cc(e){ii.current===e&&(le(Ft),le(ii))}var de=Un(0);function Vs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ll=[];function Ec(){for(var e=0;e<ll.length;e++)ll[e]._workInProgressVersionPrimary=null;ll.length=0}var gs=en.ReactCurrentDispatcher,ul=en.ReactCurrentBatchConfig,ir=0,fe=null,Se=null,Ce=null,Hs=!1,Bo=!1,ai=0,Kx=0;function Le(){throw Error(j(321))}function kc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ct(e[n],t[n]))return!1;return!0}function Nc(e,t,n,r,o,i){if(ir=i,fe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,gs.current=e===null||e.memoizedState===null?qx:Xx,e=n(r,o),Bo){i=0;do{if(Bo=!1,ai=0,25<=i)throw Error(j(301));i+=1,Ce=Se=null,t.updateQueue=null,gs.current=Zx,e=n(r,o)}while(Bo)}if(gs.current=Ws,t=Se!==null&&Se.next!==null,ir=0,Ce=Se=fe=null,Hs=!1,t)throw Error(j(300));return e}function Pc(){var e=ai!==0;return ai=0,e}function jt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ce===null?fe.memoizedState=Ce=e:Ce=Ce.next=e,Ce}function ft(){if(Se===null){var e=fe.alternate;e=e!==null?e.memoizedState:null}else e=Se.next;var t=Ce===null?fe.memoizedState:Ce.next;if(t!==null)Ce=t,Se=e;else{if(e===null)throw Error(j(310));Se=e,e={memoizedState:Se.memoizedState,baseState:Se.baseState,baseQueue:Se.baseQueue,queue:Se.queue,next:null},Ce===null?fe.memoizedState=Ce=e:Ce=Ce.next=e}return Ce}function li(e,t){return typeof t=="function"?t(e):t}function cl(e){var t=ft(),n=t.queue;if(n===null)throw Error(j(311));n.lastRenderedReducer=e;var r=Se,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var a=s=null,l=null,u=i;do{var d=u.lane;if((ir&d)===d)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=f,s=r):l=l.next=f,fe.lanes|=d,sr|=d}u=u.next}while(u!==null&&u!==i);l===null?s=r:l.next=a,Ct(r,t.memoizedState)||(Ke=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,fe.lanes|=i,sr|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function dl(e){var t=ft(),n=t.queue;if(n===null)throw Error(j(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);Ct(i,t.memoizedState)||(Ke=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Cm(){}function Em(e,t){var n=fe,r=ft(),o=t(),i=!Ct(r.memoizedState,o);if(i&&(r.memoizedState=o,Ke=!0),r=r.queue,Tc(Pm.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||Ce!==null&&Ce.memoizedState.tag&1){if(n.flags|=2048,ui(9,Nm.bind(null,n,r,o,t),void 0,null),ke===null)throw Error(j(349));ir&30||km(n,t,o)}return o}function km(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=fe.updateQueue,t===null?(t={lastEffect:null,stores:null},fe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Nm(e,t,n,r){t.value=n,t.getSnapshot=r,Tm(t)&&Rm(e)}function Pm(e,t,n){return n(function(){Tm(t)&&Rm(e)})}function Tm(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ct(e,n)}catch{return!0}}function Rm(e){var t=qt(e,1);t!==null&&bt(t,e,1,-1)}function lf(e){var t=jt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:li,lastRenderedState:e},t.queue=e,e=e.dispatch=Yx.bind(null,fe,e),[t.memoizedState,e]}function ui(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=fe.updateQueue,t===null?(t={lastEffect:null,stores:null},fe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function jm(){return ft().memoizedState}function vs(e,t,n,r){var o=jt();fe.flags|=e,o.memoizedState=ui(1|t,n,void 0,r===void 0?null:r)}function ma(e,t,n,r){var o=ft();r=r===void 0?null:r;var i=void 0;if(Se!==null){var s=Se.memoizedState;if(i=s.destroy,r!==null&&kc(r,s.deps)){o.memoizedState=ui(t,n,i,r);return}}fe.flags|=e,o.memoizedState=ui(1|t,n,i,r)}function uf(e,t){return vs(8390656,8,e,t)}function Tc(e,t){return ma(2048,8,e,t)}function Om(e,t){return ma(4,2,e,t)}function Lm(e,t){return ma(4,4,e,t)}function Am(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Mm(e,t,n){return n=n!=null?n.concat([e]):null,ma(4,4,Am.bind(null,t,e),n)}function Rc(){}function Dm(e,t){var n=ft();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&kc(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function _m(e,t){var n=ft();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&kc(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Im(e,t,n){return ir&21?(Ct(n,t)||(n=Bh(),fe.lanes|=n,sr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ke=!0),e.memoizedState=n)}function Qx(e,t){var n=ee;ee=n!==0&&4>n?n:4,e(!0);var r=ul.transition;ul.transition={};try{e(!1),t()}finally{ee=n,ul.transition=r}}function Fm(){return ft().memoizedState}function Gx(e,t,n){var r=jn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},$m(e))zm(t,n);else if(n=wm(e,t,n,r),n!==null){var o=Ue();bt(n,e,r,o),Um(n,t,r)}}function Yx(e,t,n){var r=jn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if($m(e))zm(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,a=i(s,n);if(o.hasEagerState=!0,o.eagerState=a,Ct(a,s)){var l=t.interleaved;l===null?(o.next=o,wc(t)):(o.next=l.next,l.next=o),t.interleaved=o;return}}catch{}finally{}n=wm(e,t,o,r),n!==null&&(o=Ue(),bt(n,e,r,o),Um(n,t,r))}}function $m(e){var t=e.alternate;return e===fe||t!==null&&t===fe}function zm(e,t){Bo=Hs=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Um(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,sc(e,n)}}var Ws={readContext:dt,useCallback:Le,useContext:Le,useEffect:Le,useImperativeHandle:Le,useInsertionEffect:Le,useLayoutEffect:Le,useMemo:Le,useReducer:Le,useRef:Le,useState:Le,useDebugValue:Le,useDeferredValue:Le,useTransition:Le,useMutableSource:Le,useSyncExternalStore:Le,useId:Le,unstable_isNewReconciler:!1},qx={readContext:dt,useCallback:function(e,t){return jt().memoizedState=[e,t===void 0?null:t],e},useContext:dt,useEffect:uf,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,vs(4194308,4,Am.bind(null,t,e),n)},useLayoutEffect:function(e,t){return vs(4194308,4,e,t)},useInsertionEffect:function(e,t){return vs(4,2,e,t)},useMemo:function(e,t){var n=jt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=jt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Gx.bind(null,fe,e),[r.memoizedState,e]},useRef:function(e){var t=jt();return e={current:e},t.memoizedState=e},useState:lf,useDebugValue:Rc,useDeferredValue:function(e){return jt().memoizedState=e},useTransition:function(){var e=lf(!1),t=e[0];return e=Qx.bind(null,e[1]),jt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=fe,o=jt();if(ue){if(n===void 0)throw Error(j(407));n=n()}else{if(n=t(),ke===null)throw Error(j(349));ir&30||km(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,uf(Pm.bind(null,r,i,e),[e]),r.flags|=2048,ui(9,Nm.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=jt(),t=ke.identifierPrefix;if(ue){var n=Kt,r=Wt;n=(r&~(1<<32-St(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ai++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Kx++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Xx={readContext:dt,useCallback:Dm,useContext:dt,useEffect:Tc,useImperativeHandle:Mm,useInsertionEffect:Om,useLayoutEffect:Lm,useMemo:_m,useReducer:cl,useRef:jm,useState:function(){return cl(li)},useDebugValue:Rc,useDeferredValue:function(e){var t=ft();return Im(t,Se.memoizedState,e)},useTransition:function(){var e=cl(li)[0],t=ft().memoizedState;return[e,t]},useMutableSource:Cm,useSyncExternalStore:Em,useId:Fm,unstable_isNewReconciler:!1},Zx={readContext:dt,useCallback:Dm,useContext:dt,useEffect:Tc,useImperativeHandle:Mm,useInsertionEffect:Om,useLayoutEffect:Lm,useMemo:_m,useReducer:dl,useRef:jm,useState:function(){return dl(li)},useDebugValue:Rc,useDeferredValue:function(e){var t=ft();return Se===null?t.memoizedState=e:Im(t,Se.memoizedState,e)},useTransition:function(){var e=dl(li)[0],t=ft().memoizedState;return[e,t]},useMutableSource:Cm,useSyncExternalStore:Em,useId:Fm,unstable_isNewReconciler:!1};function gt(e,t){if(e&&e.defaultProps){t=pe({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function cu(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:pe({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ga={isMounted:function(e){return(e=e._reactInternals)?dr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ue(),o=jn(e),i=Qt(r,o);i.payload=t,n!=null&&(i.callback=n),t=Tn(e,i,o),t!==null&&(bt(t,e,o,r),ms(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ue(),o=jn(e),i=Qt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Tn(e,i,o),t!==null&&(bt(t,e,o,r),ms(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ue(),r=jn(e),o=Qt(n,r);o.tag=2,t!=null&&(o.callback=t),t=Tn(e,o,r),t!==null&&(bt(t,e,r,n),ms(t,e,r))}};function cf(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!ti(n,r)||!ti(o,i):!0}function Bm(e,t,n){var r=!1,o=Mn,i=t.contextType;return typeof i=="object"&&i!==null?i=dt(i):(o=Ge(t)?rr:_e.current,r=t.contextTypes,i=(r=r!=null)?no(e,o):Mn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ga,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function df(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ga.enqueueReplaceState(t,t.state,null)}function du(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Sc(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=dt(i):(i=Ge(t)?rr:_e.current,o.context=no(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(cu(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&ga.enqueueReplaceState(o,o.state,null),Bs(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function so(e,t){try{var n="",r=t;do n+=N0(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function fl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function fu(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Jx=typeof WeakMap=="function"?WeakMap:Map;function Vm(e,t,n){n=Qt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Qs||(Qs=!0,bu=r),fu(e,t)},n}function Hm(e,t,n){n=Qt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){fu(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){fu(e,t),typeof r!="function"&&(Rn===null?Rn=new Set([this]):Rn.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function ff(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Jx;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=p1.bind(null,e,t,n),t.then(e,e))}function pf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function hf(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Qt(-1,1),t.tag=2,Tn(n,t,1))),n.lanes|=1),e)}var e1=en.ReactCurrentOwner,Ke=!1;function $e(e,t,n,r){t.child=e===null?xm(t,null,n,r):oo(t,e.child,n,r)}function mf(e,t,n,r,o){n=n.render;var i=t.ref;return Ur(t,o),r=Nc(e,t,n,r,i,o),n=Pc(),e!==null&&!Ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Xt(e,t,o)):(ue&&n&&hc(t),t.flags|=1,$e(e,t,r,o),t.child)}function gf(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Ic(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Wm(e,t,i,r,o)):(e=Ss(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:ti,n(s,r)&&e.ref===t.ref)return Xt(e,t,o)}return t.flags|=1,e=On(i,r),e.ref=t.ref,e.return=t,t.child=e}function Wm(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(ti(i,r)&&e.ref===t.ref)if(Ke=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Ke=!0);else return t.lanes=e.lanes,Xt(e,t,o)}return pu(e,t,n,r,o)}function Km(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},oe(_r,Ze),Ze|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,oe(_r,Ze),Ze|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,oe(_r,Ze),Ze|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,oe(_r,Ze),Ze|=r;return $e(e,t,o,n),t.child}function Qm(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function pu(e,t,n,r,o){var i=Ge(n)?rr:_e.current;return i=no(t,i),Ur(t,o),n=Nc(e,t,n,r,i,o),r=Pc(),e!==null&&!Ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Xt(e,t,o)):(ue&&r&&hc(t),t.flags|=1,$e(e,t,n,o),t.child)}function vf(e,t,n,r,o){if(Ge(n)){var i=!0;Is(t)}else i=!1;if(Ur(t,o),t.stateNode===null)ys(e,t),Bm(t,n,r),du(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=dt(u):(u=Ge(n)?rr:_e.current,u=no(t,u));var d=n.getDerivedStateFromProps,f=typeof d=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==u)&&df(t,s,r,u),pn=!1;var c=t.memoizedState;s.state=c,Bs(t,r,s,o),l=t.memoizedState,a!==r||c!==l||Qe.current||pn?(typeof d=="function"&&(cu(t,n,d,r),l=t.memoizedState),(a=pn||cf(t,n,a,r,c,l,u))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Sm(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:gt(t.type,a),s.props=u,f=t.pendingProps,c=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=dt(l):(l=Ge(n)?rr:_e.current,l=no(t,l));var v=n.getDerivedStateFromProps;(d=typeof v=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==f||c!==l)&&df(t,s,r,l),pn=!1,c=t.memoizedState,s.state=c,Bs(t,r,s,o);var x=t.memoizedState;a!==f||c!==x||Qe.current||pn?(typeof v=="function"&&(cu(t,n,v,r),x=t.memoizedState),(u=pn||cf(t,n,u,r,c,x,l)||!1)?(d||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,x,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,x,l)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&c===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&c===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=x),s.props=r,s.state=x,s.context=l,r=u):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&c===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&c===e.memoizedState||(t.flags|=1024),r=!1)}return hu(e,t,n,r,i,o)}function hu(e,t,n,r,o,i){Qm(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&tf(t,n,!1),Xt(e,t,i);r=t.stateNode,e1.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=oo(t,e.child,null,i),t.child=oo(t,null,a,i)):$e(e,t,a,i),t.memoizedState=r.state,o&&tf(t,n,!0),t.child}function Gm(e){var t=e.stateNode;t.pendingContext?ef(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ef(e,t.context,!1),bc(e,t.containerInfo)}function yf(e,t,n,r,o){return ro(),gc(o),t.flags|=256,$e(e,t,n,r),t.child}var mu={dehydrated:null,treeContext:null,retryLane:0};function gu(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ym(e,t,n){var r=t.pendingProps,o=de.current,i=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(o&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),oe(de,o&1),e===null)return lu(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=xa(s,r,0,null),e=tr(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=gu(n),t.memoizedState=mu,e):jc(t,s));if(o=e.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return t1(e,t,s,r,a,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,a=o.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=On(o,l),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?i=On(a,i):(i=tr(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?gu(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=mu,r}return i=e.child,e=i.sibling,r=On(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function jc(e,t){return t=xa({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function qi(e,t,n,r){return r!==null&&gc(r),oo(t,e.child,null,n),e=jc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function t1(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=fl(Error(j(422))),qi(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=xa({mode:"visible",children:r.children},o,0,null),i=tr(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&oo(t,e.child,null,s),t.child.memoizedState=gu(s),t.memoizedState=mu,i);if(!(t.mode&1))return qi(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(j(419)),r=fl(i,r,void 0),qi(e,t,s,r)}if(a=(s&e.childLanes)!==0,Ke||a){if(r=ke,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,qt(e,o),bt(r,e,o,-1))}return _c(),r=fl(Error(j(421))),qi(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=h1.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,et=Pn(o.nextSibling),tt=t,ue=!0,wt=null,e!==null&&(at[lt++]=Wt,at[lt++]=Kt,at[lt++]=or,Wt=e.id,Kt=e.overflow,or=t),t=jc(t,r.children),t.flags|=4096,t)}function xf(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),uu(e.return,t,n)}function pl(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function qm(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if($e(e,t,r.children,n),r=de.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&xf(e,n,t);else if(e.tag===19)xf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(oe(de,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Vs(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),pl(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Vs(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}pl(t,!0,n,null,i);break;case"together":pl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ys(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Xt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),sr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(j(153));if(t.child!==null){for(e=t.child,n=On(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=On(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function n1(e,t,n){switch(t.tag){case 3:Gm(t),ro();break;case 5:bm(t);break;case 1:Ge(t.type)&&Is(t);break;case 4:bc(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;oe(zs,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(oe(de,de.current&1),t.flags|=128,null):n&t.child.childLanes?Ym(e,t,n):(oe(de,de.current&1),e=Xt(e,t,n),e!==null?e.sibling:null);oe(de,de.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return qm(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),oe(de,de.current),r)break;return null;case 22:case 23:return t.lanes=0,Km(e,t,n)}return Xt(e,t,n)}var Xm,vu,Zm,Jm;Xm=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};vu=function(){};Zm=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Yn(Ft.current);var i=null;switch(n){case"input":o=$l(e,o),r=$l(e,r),i=[];break;case"select":o=pe({},o,{value:void 0}),r=pe({},r,{value:void 0}),i=[];break;case"textarea":o=Bl(e,o),r=Bl(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ds)}Hl(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var a=o[u];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Go.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(a=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Go.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&ae("scroll",e),i||a===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};Jm=function(e,t,n,r){n!==r&&(t.flags|=4)};function To(e,t){if(!ue)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ae(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function r1(e,t,n){var r=t.pendingProps;switch(mc(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ae(t),null;case 1:return Ge(t.type)&&_s(),Ae(t),null;case 3:return r=t.stateNode,io(),le(Qe),le(_e),Ec(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Gi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,wt!==null&&(ku(wt),wt=null))),vu(e,t),Ae(t),null;case 5:Cc(t);var o=Yn(si.current);if(n=t.type,e!==null&&t.stateNode!=null)Zm(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(j(166));return Ae(t),null}if(e=Yn(Ft.current),Gi(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Mt]=t,r[oi]=i,e=(t.mode&1)!==0,n){case"dialog":ae("cancel",r),ae("close",r);break;case"iframe":case"object":case"embed":ae("load",r);break;case"video":case"audio":for(o=0;o<_o.length;o++)ae(_o[o],r);break;case"source":ae("error",r);break;case"img":case"image":case"link":ae("error",r),ae("load",r);break;case"details":ae("toggle",r);break;case"input":Pd(r,i),ae("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},ae("invalid",r);break;case"textarea":Rd(r,i),ae("invalid",r)}Hl(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var a=i[s];s==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Qi(r.textContent,a,e),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Qi(r.textContent,a,e),o=["children",""+a]):Go.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&ae("scroll",r)}switch(n){case"input":$i(r),Td(r,i,!0);break;case"textarea":$i(r),jd(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Ds)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Ph(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[Mt]=t,e[oi]=r,Xm(e,t,!1,!1),t.stateNode=e;e:{switch(s=Wl(n,r),n){case"dialog":ae("cancel",e),ae("close",e),o=r;break;case"iframe":case"object":case"embed":ae("load",e),o=r;break;case"video":case"audio":for(o=0;o<_o.length;o++)ae(_o[o],e);o=r;break;case"source":ae("error",e),o=r;break;case"img":case"image":case"link":ae("error",e),ae("load",e),o=r;break;case"details":ae("toggle",e),o=r;break;case"input":Pd(e,r),o=$l(e,r),ae("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=pe({},r,{value:void 0}),ae("invalid",e);break;case"textarea":Rd(e,r),o=Bl(e,r),ae("invalid",e);break;default:o=r}Hl(n,o),a=o;for(i in a)if(a.hasOwnProperty(i)){var l=a[i];i==="style"?jh(e,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Th(e,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Yo(e,l):typeof l=="number"&&Yo(e,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Go.hasOwnProperty(i)?l!=null&&i==="onScroll"&&ae("scroll",e):l!=null&&ec(e,i,l,s))}switch(n){case"input":$i(e),Td(e,r,!1);break;case"textarea":$i(e),jd(e);break;case"option":r.value!=null&&e.setAttribute("value",""+An(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Ir(e,!!r.multiple,i,!1):r.defaultValue!=null&&Ir(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Ds)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ae(t),null;case 6:if(e&&t.stateNode!=null)Jm(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(j(166));if(n=Yn(si.current),Yn(Ft.current),Gi(t)){if(r=t.stateNode,n=t.memoizedProps,r[Mt]=t,(i=r.nodeValue!==n)&&(e=tt,e!==null))switch(e.tag){case 3:Qi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Qi(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Mt]=t,t.stateNode=r}return Ae(t),null;case 13:if(le(de),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ue&&et!==null&&t.mode&1&&!(t.flags&128))vm(),ro(),t.flags|=98560,i=!1;else if(i=Gi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(j(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(j(317));i[Mt]=t}else ro(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Ae(t),i=!1}else wt!==null&&(ku(wt),wt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||de.current&1?be===0&&(be=3):_c())),t.updateQueue!==null&&(t.flags|=4),Ae(t),null);case 4:return io(),vu(e,t),e===null&&ni(t.stateNode.containerInfo),Ae(t),null;case 10:return xc(t.type._context),Ae(t),null;case 17:return Ge(t.type)&&_s(),Ae(t),null;case 19:if(le(de),i=t.memoizedState,i===null)return Ae(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)To(i,!1);else{if(be!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Vs(e),s!==null){for(t.flags|=128,To(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return oe(de,de.current&1|2),t.child}e=e.sibling}i.tail!==null&&ge()>ao&&(t.flags|=128,r=!0,To(i,!1),t.lanes=4194304)}else{if(!r)if(e=Vs(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),To(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!ue)return Ae(t),null}else 2*ge()-i.renderingStartTime>ao&&n!==1073741824&&(t.flags|=128,r=!0,To(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ge(),t.sibling=null,n=de.current,oe(de,r?n&1|2:n&1),t):(Ae(t),null);case 22:case 23:return Dc(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ze&1073741824&&(Ae(t),t.subtreeFlags&6&&(t.flags|=8192)):Ae(t),null;case 24:return null;case 25:return null}throw Error(j(156,t.tag))}function o1(e,t){switch(mc(t),t.tag){case 1:return Ge(t.type)&&_s(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return io(),le(Qe),le(_e),Ec(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Cc(t),null;case 13:if(le(de),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(j(340));ro()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return le(de),null;case 4:return io(),null;case 10:return xc(t.type._context),null;case 22:case 23:return Dc(),null;case 24:return null;default:return null}}var Xi=!1,De=!1,i1=typeof WeakSet=="function"?WeakSet:Set,_=null;function Dr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){me(e,t,r)}else n.current=null}function yu(e,t,n){try{n()}catch(r){me(e,t,r)}}var wf=!1;function s1(e,t){if(tu=Ls,e=om(),pc(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,a=-1,l=-1,u=0,d=0,f=e,c=null;t:for(;;){for(var v;f!==n||o!==0&&f.nodeType!==3||(a=s+o),f!==i||r!==0&&f.nodeType!==3||(l=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(v=f.firstChild)!==null;)c=f,f=v;for(;;){if(f===e)break t;if(c===n&&++u===o&&(a=s),c===i&&++d===r&&(l=s),(v=f.nextSibling)!==null)break;f=c,c=f.parentNode}f=v}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(nu={focusedElem:e,selectionRange:n},Ls=!1,_=t;_!==null;)if(t=_,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,_=e;else for(;_!==null;){t=_;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var h=x.memoizedProps,S=x.memoizedState,g=t.stateNode,m=g.getSnapshotBeforeUpdate(t.elementType===t.type?h:gt(t.type,h),S);g.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var w=t.stateNode.containerInfo;w.nodeType===1?w.textContent="":w.nodeType===9&&w.documentElement&&w.removeChild(w.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(j(163))}}catch(b){me(t,t.return,b)}if(e=t.sibling,e!==null){e.return=t.return,_=e;break}_=t.return}return x=wf,wf=!1,x}function Vo(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&yu(t,n,i)}o=o.next}while(o!==r)}}function va(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function xu(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function eg(e){var t=e.alternate;t!==null&&(e.alternate=null,eg(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Mt],delete t[oi],delete t[iu],delete t[Bx],delete t[Vx])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function tg(e){return e.tag===5||e.tag===3||e.tag===4}function Sf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||tg(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function wu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ds));else if(r!==4&&(e=e.child,e!==null))for(wu(e,t,n),e=e.sibling;e!==null;)wu(e,t,n),e=e.sibling}function Su(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Su(e,t,n),e=e.sibling;e!==null;)Su(e,t,n),e=e.sibling}var Pe=null,xt=!1;function an(e,t,n){for(n=n.child;n!==null;)ng(e,t,n),n=n.sibling}function ng(e,t,n){if(It&&typeof It.onCommitFiberUnmount=="function")try{It.onCommitFiberUnmount(ua,n)}catch{}switch(n.tag){case 5:De||Dr(n,t);case 6:var r=Pe,o=xt;Pe=null,an(e,t,n),Pe=r,xt=o,Pe!==null&&(xt?(e=Pe,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Pe.removeChild(n.stateNode));break;case 18:Pe!==null&&(xt?(e=Pe,n=n.stateNode,e.nodeType===8?sl(e.parentNode,n):e.nodeType===1&&sl(e,n),Jo(e)):sl(Pe,n.stateNode));break;case 4:r=Pe,o=xt,Pe=n.stateNode.containerInfo,xt=!0,an(e,t,n),Pe=r,xt=o;break;case 0:case 11:case 14:case 15:if(!De&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&yu(n,t,s),o=o.next}while(o!==r)}an(e,t,n);break;case 1:if(!De&&(Dr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){me(n,t,a)}an(e,t,n);break;case 21:an(e,t,n);break;case 22:n.mode&1?(De=(r=De)||n.memoizedState!==null,an(e,t,n),De=r):an(e,t,n);break;default:an(e,t,n)}}function bf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new i1),t.forEach(function(r){var o=m1.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function mt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:Pe=a.stateNode,xt=!1;break e;case 3:Pe=a.stateNode.containerInfo,xt=!0;break e;case 4:Pe=a.stateNode.containerInfo,xt=!0;break e}a=a.return}if(Pe===null)throw Error(j(160));ng(i,s,o),Pe=null,xt=!1;var l=o.alternate;l!==null&&(l.return=null),o.return=null}catch(u){me(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)rg(t,e),t=t.sibling}function rg(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(mt(t,e),Rt(e),r&4){try{Vo(3,e,e.return),va(3,e)}catch(h){me(e,e.return,h)}try{Vo(5,e,e.return)}catch(h){me(e,e.return,h)}}break;case 1:mt(t,e),Rt(e),r&512&&n!==null&&Dr(n,n.return);break;case 5:if(mt(t,e),Rt(e),r&512&&n!==null&&Dr(n,n.return),e.flags&32){var o=e.stateNode;try{Yo(o,"")}catch(h){me(e,e.return,h)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&kh(o,i),Wl(a,s);var u=Wl(a,i);for(s=0;s<l.length;s+=2){var d=l[s],f=l[s+1];d==="style"?jh(o,f):d==="dangerouslySetInnerHTML"?Th(o,f):d==="children"?Yo(o,f):ec(o,d,f,u)}switch(a){case"input":zl(o,i);break;case"textarea":Nh(o,i);break;case"select":var c=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var v=i.value;v!=null?Ir(o,!!i.multiple,v,!1):c!==!!i.multiple&&(i.defaultValue!=null?Ir(o,!!i.multiple,i.defaultValue,!0):Ir(o,!!i.multiple,i.multiple?[]:"",!1))}o[oi]=i}catch(h){me(e,e.return,h)}}break;case 6:if(mt(t,e),Rt(e),r&4){if(e.stateNode===null)throw Error(j(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(h){me(e,e.return,h)}}break;case 3:if(mt(t,e),Rt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Jo(t.containerInfo)}catch(h){me(e,e.return,h)}break;case 4:mt(t,e),Rt(e);break;case 13:mt(t,e),Rt(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Ac=ge())),r&4&&bf(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(De=(u=De)||d,mt(t,e),De=u):mt(t,e),Rt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(_=e,d=e.child;d!==null;){for(f=_=d;_!==null;){switch(c=_,v=c.child,c.tag){case 0:case 11:case 14:case 15:Vo(4,c,c.return);break;case 1:Dr(c,c.return);var x=c.stateNode;if(typeof x.componentWillUnmount=="function"){r=c,n=c.return;try{t=r,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(h){me(r,n,h)}}break;case 5:Dr(c,c.return);break;case 22:if(c.memoizedState!==null){Ef(f);continue}}v!==null?(v.return=c,_=v):Ef(f)}d=d.sibling}e:for(d=null,f=e;;){if(f.tag===5){if(d===null){d=f;try{o=f.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=f.stateNode,l=f.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=Rh("display",s))}catch(h){me(e,e.return,h)}}}else if(f.tag===6){if(d===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(h){me(e,e.return,h)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:mt(t,e),Rt(e),r&4&&bf(e);break;case 21:break;default:mt(t,e),Rt(e)}}function Rt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(tg(n)){var r=n;break e}n=n.return}throw Error(j(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Yo(o,""),r.flags&=-33);var i=Sf(e);Su(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,a=Sf(e);wu(e,a,s);break;default:throw Error(j(161))}}catch(l){me(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function a1(e,t,n){_=e,og(e)}function og(e,t,n){for(var r=(e.mode&1)!==0;_!==null;){var o=_,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||Xi;if(!s){var a=o.alternate,l=a!==null&&a.memoizedState!==null||De;a=Xi;var u=De;if(Xi=s,(De=l)&&!u)for(_=o;_!==null;)s=_,l=s.child,s.tag===22&&s.memoizedState!==null?kf(o):l!==null?(l.return=s,_=l):kf(o);for(;i!==null;)_=i,og(i),i=i.sibling;_=o,Xi=a,De=u}Cf(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,_=i):Cf(e)}}function Cf(e){for(;_!==null;){var t=_;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:De||va(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!De)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:gt(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&af(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}af(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var f=d.dehydrated;f!==null&&Jo(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(j(163))}De||t.flags&512&&xu(t)}catch(c){me(t,t.return,c)}}if(t===e){_=null;break}if(n=t.sibling,n!==null){n.return=t.return,_=n;break}_=t.return}}function Ef(e){for(;_!==null;){var t=_;if(t===e){_=null;break}var n=t.sibling;if(n!==null){n.return=t.return,_=n;break}_=t.return}}function kf(e){for(;_!==null;){var t=_;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{va(4,t)}catch(l){me(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(l){me(t,o,l)}}var i=t.return;try{xu(t)}catch(l){me(t,i,l)}break;case 5:var s=t.return;try{xu(t)}catch(l){me(t,s,l)}}}catch(l){me(t,t.return,l)}if(t===e){_=null;break}var a=t.sibling;if(a!==null){a.return=t.return,_=a;break}_=t.return}}var l1=Math.ceil,Ks=en.ReactCurrentDispatcher,Oc=en.ReactCurrentOwner,ct=en.ReactCurrentBatchConfig,X=0,ke=null,ve=null,Te=0,Ze=0,_r=Un(0),be=0,ci=null,sr=0,ya=0,Lc=0,Ho=null,We=null,Ac=0,ao=1/0,Vt=null,Qs=!1,bu=null,Rn=null,Zi=!1,bn=null,Gs=0,Wo=0,Cu=null,xs=-1,ws=0;function Ue(){return X&6?ge():xs!==-1?xs:xs=ge()}function jn(e){return e.mode&1?X&2&&Te!==0?Te&-Te:Wx.transition!==null?(ws===0&&(ws=Bh()),ws):(e=ee,e!==0||(e=window.event,e=e===void 0?16:Yh(e.type)),e):1}function bt(e,t,n,r){if(50<Wo)throw Wo=0,Cu=null,Error(j(185));Ci(e,n,r),(!(X&2)||e!==ke)&&(e===ke&&(!(X&2)&&(ya|=n),be===4&&mn(e,Te)),Ye(e,r),n===1&&X===0&&!(t.mode&1)&&(ao=ge()+500,ha&&Bn()))}function Ye(e,t){var n=e.callbackNode;W0(e,t);var r=Os(e,e===ke?Te:0);if(r===0)n!==null&&Ad(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ad(n),t===1)e.tag===0?Hx(Nf.bind(null,e)):hm(Nf.bind(null,e)),zx(function(){!(X&6)&&Bn()}),n=null;else{switch(Vh(r)){case 1:n=ic;break;case 4:n=zh;break;case 16:n=js;break;case 536870912:n=Uh;break;default:n=js}n=fg(n,ig.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ig(e,t){if(xs=-1,ws=0,X&6)throw Error(j(327));var n=e.callbackNode;if(Br()&&e.callbackNode!==n)return null;var r=Os(e,e===ke?Te:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ys(e,r);else{t=r;var o=X;X|=2;var i=ag();(ke!==e||Te!==t)&&(Vt=null,ao=ge()+500,er(e,t));do try{d1();break}catch(a){sg(e,a)}while(!0);yc(),Ks.current=i,X=o,ve!==null?t=0:(ke=null,Te=0,t=be)}if(t!==0){if(t===2&&(o=ql(e),o!==0&&(r=o,t=Eu(e,o))),t===1)throw n=ci,er(e,0),mn(e,r),Ye(e,ge()),n;if(t===6)mn(e,r);else{if(o=e.current.alternate,!(r&30)&&!u1(o)&&(t=Ys(e,r),t===2&&(i=ql(e),i!==0&&(r=i,t=Eu(e,i))),t===1))throw n=ci,er(e,0),mn(e,r),Ye(e,ge()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(j(345));case 2:Kn(e,We,Vt);break;case 3:if(mn(e,r),(r&130023424)===r&&(t=Ac+500-ge(),10<t)){if(Os(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Ue(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=ou(Kn.bind(null,e,We,Vt),t);break}Kn(e,We,Vt);break;case 4:if(mn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-St(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=ge()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*l1(r/1960))-r,10<r){e.timeoutHandle=ou(Kn.bind(null,e,We,Vt),r);break}Kn(e,We,Vt);break;case 5:Kn(e,We,Vt);break;default:throw Error(j(329))}}}return Ye(e,ge()),e.callbackNode===n?ig.bind(null,e):null}function Eu(e,t){var n=Ho;return e.current.memoizedState.isDehydrated&&(er(e,t).flags|=256),e=Ys(e,t),e!==2&&(t=We,We=n,t!==null&&ku(t)),e}function ku(e){We===null?We=e:We.push.apply(We,e)}function u1(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!Ct(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function mn(e,t){for(t&=~Lc,t&=~ya,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-St(t),r=1<<n;e[n]=-1,t&=~r}}function Nf(e){if(X&6)throw Error(j(327));Br();var t=Os(e,0);if(!(t&1))return Ye(e,ge()),null;var n=Ys(e,t);if(e.tag!==0&&n===2){var r=ql(e);r!==0&&(t=r,n=Eu(e,r))}if(n===1)throw n=ci,er(e,0),mn(e,t),Ye(e,ge()),n;if(n===6)throw Error(j(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Kn(e,We,Vt),Ye(e,ge()),null}function Mc(e,t){var n=X;X|=1;try{return e(t)}finally{X=n,X===0&&(ao=ge()+500,ha&&Bn())}}function ar(e){bn!==null&&bn.tag===0&&!(X&6)&&Br();var t=X;X|=1;var n=ct.transition,r=ee;try{if(ct.transition=null,ee=1,e)return e()}finally{ee=r,ct.transition=n,X=t,!(X&6)&&Bn()}}function Dc(){Ze=_r.current,le(_r)}function er(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,$x(n)),ve!==null)for(n=ve.return;n!==null;){var r=n;switch(mc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&_s();break;case 3:io(),le(Qe),le(_e),Ec();break;case 5:Cc(r);break;case 4:io();break;case 13:le(de);break;case 19:le(de);break;case 10:xc(r.type._context);break;case 22:case 23:Dc()}n=n.return}if(ke=e,ve=e=On(e.current,null),Te=Ze=t,be=0,ci=null,Lc=ya=sr=0,We=Ho=null,Gn!==null){for(t=0;t<Gn.length;t++)if(n=Gn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}Gn=null}return e}function sg(e,t){do{var n=ve;try{if(yc(),gs.current=Ws,Hs){for(var r=fe.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Hs=!1}if(ir=0,Ce=Se=fe=null,Bo=!1,ai=0,Oc.current=null,n===null||n.return===null){be=1,ci=t,ve=null;break}e:{var i=e,s=n.return,a=n,l=t;if(t=Te,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,d=a,f=d.tag;if(!(d.mode&1)&&(f===0||f===11||f===15)){var c=d.alternate;c?(d.updateQueue=c.updateQueue,d.memoizedState=c.memoizedState,d.lanes=c.lanes):(d.updateQueue=null,d.memoizedState=null)}var v=pf(s);if(v!==null){v.flags&=-257,hf(v,s,a,i,t),v.mode&1&&ff(i,u,t),t=v,l=u;var x=t.updateQueue;if(x===null){var h=new Set;h.add(l),t.updateQueue=h}else x.add(l);break e}else{if(!(t&1)){ff(i,u,t),_c();break e}l=Error(j(426))}}else if(ue&&a.mode&1){var S=pf(s);if(S!==null){!(S.flags&65536)&&(S.flags|=256),hf(S,s,a,i,t),gc(so(l,a));break e}}i=l=so(l,a),be!==4&&(be=2),Ho===null?Ho=[i]:Ho.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var g=Vm(i,l,t);sf(i,g);break e;case 1:a=l;var m=i.type,w=i.stateNode;if(!(i.flags&128)&&(typeof m.getDerivedStateFromError=="function"||w!==null&&typeof w.componentDidCatch=="function"&&(Rn===null||!Rn.has(w)))){i.flags|=65536,t&=-t,i.lanes|=t;var b=Hm(i,a,t);sf(i,b);break e}}i=i.return}while(i!==null)}ug(n)}catch(C){t=C,ve===n&&n!==null&&(ve=n=n.return);continue}break}while(!0)}function ag(){var e=Ks.current;return Ks.current=Ws,e===null?Ws:e}function _c(){(be===0||be===3||be===2)&&(be=4),ke===null||!(sr&268435455)&&!(ya&268435455)||mn(ke,Te)}function Ys(e,t){var n=X;X|=2;var r=ag();(ke!==e||Te!==t)&&(Vt=null,er(e,t));do try{c1();break}catch(o){sg(e,o)}while(!0);if(yc(),X=n,Ks.current=r,ve!==null)throw Error(j(261));return ke=null,Te=0,be}function c1(){for(;ve!==null;)lg(ve)}function d1(){for(;ve!==null&&!_0();)lg(ve)}function lg(e){var t=dg(e.alternate,e,Ze);e.memoizedProps=e.pendingProps,t===null?ug(e):ve=t,Oc.current=null}function ug(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=o1(n,t),n!==null){n.flags&=32767,ve=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{be=6,ve=null;return}}else if(n=r1(n,t,Ze),n!==null){ve=n;return}if(t=t.sibling,t!==null){ve=t;return}ve=t=e}while(t!==null);be===0&&(be=5)}function Kn(e,t,n){var r=ee,o=ct.transition;try{ct.transition=null,ee=1,f1(e,t,n,r)}finally{ct.transition=o,ee=r}return null}function f1(e,t,n,r){do Br();while(bn!==null);if(X&6)throw Error(j(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(j(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(K0(e,i),e===ke&&(ve=ke=null,Te=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Zi||(Zi=!0,fg(js,function(){return Br(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=ct.transition,ct.transition=null;var s=ee;ee=1;var a=X;X|=4,Oc.current=null,s1(e,n),rg(n,e),Lx(nu),Ls=!!tu,nu=tu=null,e.current=n,a1(n),I0(),X=a,ee=s,ct.transition=i}else e.current=n;if(Zi&&(Zi=!1,bn=e,Gs=o),i=e.pendingLanes,i===0&&(Rn=null),z0(n.stateNode),Ye(e,ge()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Qs)throw Qs=!1,e=bu,bu=null,e;return Gs&1&&e.tag!==0&&Br(),i=e.pendingLanes,i&1?e===Cu?Wo++:(Wo=0,Cu=e):Wo=0,Bn(),null}function Br(){if(bn!==null){var e=Vh(Gs),t=ct.transition,n=ee;try{if(ct.transition=null,ee=16>e?16:e,bn===null)var r=!1;else{if(e=bn,bn=null,Gs=0,X&6)throw Error(j(331));var o=X;for(X|=4,_=e.current;_!==null;){var i=_,s=i.child;if(_.flags&16){var a=i.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(_=u;_!==null;){var d=_;switch(d.tag){case 0:case 11:case 15:Vo(8,d,i)}var f=d.child;if(f!==null)f.return=d,_=f;else for(;_!==null;){d=_;var c=d.sibling,v=d.return;if(eg(d),d===u){_=null;break}if(c!==null){c.return=v,_=c;break}_=v}}}var x=i.alternate;if(x!==null){var h=x.child;if(h!==null){x.child=null;do{var S=h.sibling;h.sibling=null,h=S}while(h!==null)}}_=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,_=s;else e:for(;_!==null;){if(i=_,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Vo(9,i,i.return)}var g=i.sibling;if(g!==null){g.return=i.return,_=g;break e}_=i.return}}var m=e.current;for(_=m;_!==null;){s=_;var w=s.child;if(s.subtreeFlags&2064&&w!==null)w.return=s,_=w;else e:for(s=m;_!==null;){if(a=_,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:va(9,a)}}catch(C){me(a,a.return,C)}if(a===s){_=null;break e}var b=a.sibling;if(b!==null){b.return=a.return,_=b;break e}_=a.return}}if(X=o,Bn(),It&&typeof It.onPostCommitFiberRoot=="function")try{It.onPostCommitFiberRoot(ua,e)}catch{}r=!0}return r}finally{ee=n,ct.transition=t}}return!1}function Pf(e,t,n){t=so(n,t),t=Vm(e,t,1),e=Tn(e,t,1),t=Ue(),e!==null&&(Ci(e,1,t),Ye(e,t))}function me(e,t,n){if(e.tag===3)Pf(e,e,n);else for(;t!==null;){if(t.tag===3){Pf(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Rn===null||!Rn.has(r))){e=so(n,e),e=Hm(t,e,1),t=Tn(t,e,1),e=Ue(),t!==null&&(Ci(t,1,e),Ye(t,e));break}}t=t.return}}function p1(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ue(),e.pingedLanes|=e.suspendedLanes&n,ke===e&&(Te&n)===n&&(be===4||be===3&&(Te&130023424)===Te&&500>ge()-Ac?er(e,0):Lc|=n),Ye(e,t)}function cg(e,t){t===0&&(e.mode&1?(t=Bi,Bi<<=1,!(Bi&130023424)&&(Bi=4194304)):t=1);var n=Ue();e=qt(e,t),e!==null&&(Ci(e,t,n),Ye(e,n))}function h1(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),cg(e,n)}function m1(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(j(314))}r!==null&&r.delete(t),cg(e,n)}var dg;dg=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Qe.current)Ke=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ke=!1,n1(e,t,n);Ke=!!(e.flags&131072)}else Ke=!1,ue&&t.flags&1048576&&mm(t,$s,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ys(e,t),e=t.pendingProps;var o=no(t,_e.current);Ur(t,n),o=Nc(null,t,r,e,o,n);var i=Pc();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ge(r)?(i=!0,Is(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Sc(t),o.updater=ga,t.stateNode=o,o._reactInternals=t,du(t,r,e,n),t=hu(null,t,r,!0,i,n)):(t.tag=0,ue&&i&&hc(t),$e(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ys(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=v1(r),e=gt(r,e),o){case 0:t=pu(null,t,r,e,n);break e;case 1:t=vf(null,t,r,e,n);break e;case 11:t=mf(null,t,r,e,n);break e;case 14:t=gf(null,t,r,gt(r.type,e),n);break e}throw Error(j(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:gt(r,o),pu(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:gt(r,o),vf(e,t,r,o,n);case 3:e:{if(Gm(t),e===null)throw Error(j(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Sm(e,t),Bs(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=so(Error(j(423)),t),t=yf(e,t,r,n,o);break e}else if(r!==o){o=so(Error(j(424)),t),t=yf(e,t,r,n,o);break e}else for(et=Pn(t.stateNode.containerInfo.firstChild),tt=t,ue=!0,wt=null,n=xm(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(ro(),r===o){t=Xt(e,t,n);break e}$e(e,t,r,n)}t=t.child}return t;case 5:return bm(t),e===null&&lu(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,ru(r,o)?s=null:i!==null&&ru(r,i)&&(t.flags|=32),Qm(e,t),$e(e,t,s,n),t.child;case 6:return e===null&&lu(t),null;case 13:return Ym(e,t,n);case 4:return bc(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=oo(t,null,r,n):$e(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:gt(r,o),mf(e,t,r,o,n);case 7:return $e(e,t,t.pendingProps,n),t.child;case 8:return $e(e,t,t.pendingProps.children,n),t.child;case 12:return $e(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,oe(zs,r._currentValue),r._currentValue=s,i!==null)if(Ct(i.value,s)){if(i.children===o.children&&!Qe.current){t=Xt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){s=i.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=Qt(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?l.next=l:(l.next=d.next,d.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),uu(i.return,n,t),a.lanes|=n;break}l=l.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(j(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),uu(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}$e(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Ur(t,n),o=dt(o),r=r(o),t.flags|=1,$e(e,t,r,n),t.child;case 14:return r=t.type,o=gt(r,t.pendingProps),o=gt(r.type,o),gf(e,t,r,o,n);case 15:return Wm(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:gt(r,o),ys(e,t),t.tag=1,Ge(r)?(e=!0,Is(t)):e=!1,Ur(t,n),Bm(t,r,o),du(t,r,o,n),hu(null,t,r,!0,e,n);case 19:return qm(e,t,n);case 22:return Km(e,t,n)}throw Error(j(156,t.tag))};function fg(e,t){return $h(e,t)}function g1(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ut(e,t,n,r){return new g1(e,t,n,r)}function Ic(e){return e=e.prototype,!(!e||!e.isReactComponent)}function v1(e){if(typeof e=="function")return Ic(e)?1:0;if(e!=null){if(e=e.$$typeof,e===nc)return 11;if(e===rc)return 14}return 2}function On(e,t){var n=e.alternate;return n===null?(n=ut(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ss(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")Ic(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Nr:return tr(n.children,o,i,t);case tc:s=8,o|=8;break;case Dl:return e=ut(12,n,t,o|2),e.elementType=Dl,e.lanes=i,e;case _l:return e=ut(13,n,t,o),e.elementType=_l,e.lanes=i,e;case Il:return e=ut(19,n,t,o),e.elementType=Il,e.lanes=i,e;case bh:return xa(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case wh:s=10;break e;case Sh:s=9;break e;case nc:s=11;break e;case rc:s=14;break e;case fn:s=16,r=null;break e}throw Error(j(130,e==null?e:typeof e,""))}return t=ut(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function tr(e,t,n,r){return e=ut(7,e,r,t),e.lanes=n,e}function xa(e,t,n,r){return e=ut(22,e,r,t),e.elementType=bh,e.lanes=n,e.stateNode={isHidden:!1},e}function hl(e,t,n){return e=ut(6,e,null,t),e.lanes=n,e}function ml(e,t,n){return t=ut(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function y1(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ya(0),this.expirationTimes=Ya(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ya(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Fc(e,t,n,r,o,i,s,a,l){return e=new y1(e,t,n,a,l),t===1?(t=1,i===!0&&(t|=8)):t=0,i=ut(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Sc(i),e}function x1(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:kr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function pg(e){if(!e)return Mn;e=e._reactInternals;e:{if(dr(e)!==e||e.tag!==1)throw Error(j(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ge(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(j(171))}if(e.tag===1){var n=e.type;if(Ge(n))return pm(e,n,t)}return t}function hg(e,t,n,r,o,i,s,a,l){return e=Fc(n,r,!0,e,o,i,s,a,l),e.context=pg(null),n=e.current,r=Ue(),o=jn(n),i=Qt(r,o),i.callback=t??null,Tn(n,i,o),e.current.lanes=o,Ci(e,o,r),Ye(e,r),e}function wa(e,t,n,r){var o=t.current,i=Ue(),s=jn(o);return n=pg(n),t.context===null?t.context=n:t.pendingContext=n,t=Qt(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Tn(o,t,s),e!==null&&(bt(e,o,s,i),ms(e,o,s)),s}function qs(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Tf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function $c(e,t){Tf(e,t),(e=e.alternate)&&Tf(e,t)}function w1(){return null}var mg=typeof reportError=="function"?reportError:function(e){console.error(e)};function zc(e){this._internalRoot=e}Sa.prototype.render=zc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(j(409));wa(e,t,null,null)};Sa.prototype.unmount=zc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;ar(function(){wa(null,e,null,null)}),t[Yt]=null}};function Sa(e){this._internalRoot=e}Sa.prototype.unstable_scheduleHydration=function(e){if(e){var t=Kh();e={blockedOn:null,target:e,priority:t};for(var n=0;n<hn.length&&t!==0&&t<hn[n].priority;n++);hn.splice(n,0,e),n===0&&Gh(e)}};function Uc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ba(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Rf(){}function S1(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=qs(s);i.call(u)}}var s=hg(t,r,e,0,null,!1,!1,"",Rf);return e._reactRootContainer=s,e[Yt]=s.current,ni(e.nodeType===8?e.parentNode:e),ar(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var u=qs(l);a.call(u)}}var l=Fc(e,0,!1,null,null,!1,!1,"",Rf);return e._reactRootContainer=l,e[Yt]=l.current,ni(e.nodeType===8?e.parentNode:e),ar(function(){wa(t,l,n,r)}),l}function Ca(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var a=o;o=function(){var l=qs(s);a.call(l)}}wa(t,s,e,o)}else s=S1(n,t,e,o,r);return qs(s)}Hh=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Do(t.pendingLanes);n!==0&&(sc(t,n|1),Ye(t,ge()),!(X&6)&&(ao=ge()+500,Bn()))}break;case 13:ar(function(){var r=qt(e,1);if(r!==null){var o=Ue();bt(r,e,1,o)}}),$c(e,1)}};ac=function(e){if(e.tag===13){var t=qt(e,134217728);if(t!==null){var n=Ue();bt(t,e,134217728,n)}$c(e,134217728)}};Wh=function(e){if(e.tag===13){var t=jn(e),n=qt(e,t);if(n!==null){var r=Ue();bt(n,e,t,r)}$c(e,t)}};Kh=function(){return ee};Qh=function(e,t){var n=ee;try{return ee=e,t()}finally{ee=n}};Ql=function(e,t,n){switch(t){case"input":if(zl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=pa(r);if(!o)throw Error(j(90));Eh(r),zl(r,o)}}}break;case"textarea":Nh(e,n);break;case"select":t=n.value,t!=null&&Ir(e,!!n.multiple,t,!1)}};Ah=Mc;Mh=ar;var b1={usingClientEntryPoint:!1,Events:[ki,jr,pa,Oh,Lh,Mc]},Ro={findFiberByHostInstance:Qn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},C1={bundleType:Ro.bundleType,version:Ro.version,rendererPackageName:Ro.rendererPackageName,rendererConfig:Ro.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:en.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Ih(e),e===null?null:e.stateNode},findFiberByHostInstance:Ro.findFiberByHostInstance||w1,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ji=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ji.isDisabled&&Ji.supportsFiber)try{ua=Ji.inject(C1),It=Ji}catch{}}ot.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=b1;ot.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Uc(t))throw Error(j(200));return x1(e,t,null,n)};ot.createRoot=function(e,t){if(!Uc(e))throw Error(j(299));var n=!1,r="",o=mg;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Fc(e,1,!1,null,null,n,!1,r,o),e[Yt]=t.current,ni(e.nodeType===8?e.parentNode:e),new zc(t)};ot.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(j(188)):(e=Object.keys(e).join(","),Error(j(268,e)));return e=Ih(t),e=e===null?null:e.stateNode,e};ot.flushSync=function(e){return ar(e)};ot.hydrate=function(e,t,n){if(!ba(t))throw Error(j(200));return Ca(null,e,t,!0,n)};ot.hydrateRoot=function(e,t,n){if(!Uc(e))throw Error(j(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=mg;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=hg(t,null,e,1,n??null,o,!1,i,s),e[Yt]=t.current,ni(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Sa(t)};ot.render=function(e,t,n){if(!ba(t))throw Error(j(200));return Ca(null,e,t,!1,n)};ot.unmountComponentAtNode=function(e){if(!ba(e))throw Error(j(40));return e._reactRootContainer?(ar(function(){Ca(null,null,e,!1,function(){e._reactRootContainer=null,e[Yt]=null})}),!0):!1};ot.unstable_batchedUpdates=Mc;ot.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ba(n))throw Error(j(200));if(e==null||e._reactInternals===void 0)throw Error(j(38));return Ca(e,t,n,!1,r)};ot.version="18.3.1-next-f1338f8080-20240426";function gg(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(gg)}catch(e){console.error(e)}}gg(),gh.exports=ot;var Pi=gh.exports;const vg=rh(Pi);var yg,jf=Pi;yg=jf.createRoot,jf.hydrateRoot;var Of=["light","dark"],E1="(prefers-color-scheme: dark)",k1=y.createContext(void 0),N1={setTheme:e=>{},themes:[]},P1=()=>{var e;return(e=y.useContext(k1))!=null?e:N1};y.memo(({forcedTheme:e,storageKey:t,attribute:n,enableSystem:r,enableColorScheme:o,defaultTheme:i,value:s,attrs:a,nonce:l})=>{let u=i==="system",d=n==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${a.map(x=>`'${x}'`).join(",")})`};`:`var d=document.documentElement,n='${n}',s='setAttribute';`,f=o?Of.includes(i)&&i?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${i}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",c=(x,h=!1,S=!0)=>{let g=s?s[x]:x,m=h?x+"|| ''":`'${g}'`,w="";return o&&S&&!h&&Of.includes(x)&&(w+=`d.style.colorScheme = '${x}';`),n==="class"?h||g?w+=`c.add(${m})`:w+="null":g&&(w+=`d[s](n,${m})`),w},v=e?`!function(){${d}${c(e)}}()`:r?`!function(){try{${d}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${u})){var t='${E1}',m=window.matchMedia(t);if(m.media!==t||m.matches){${c("dark")}}else{${c("light")}}}else if(e){${s?`var x=${JSON.stringify(s)};`:""}${c(s?"x[e]":"e",!0)}}${u?"":"else{"+c(i,!1,!1)+"}"}${f}}catch(e){}}()`:`!function(){try{${d}var e=localStorage.getItem('${t}');if(e){${s?`var x=${JSON.stringify(s)};`:""}${c(s?"x[e]":"e",!0)}}else{${c(i,!1,!1)};}${f}}catch(t){}}();`;return y.createElement("script",{nonce:l,dangerouslySetInnerHTML:{__html:v}})});var T1=e=>{switch(e){case"success":return O1;case"info":return A1;case"warning":return L1;case"error":return M1;default:return null}},R1=Array(12).fill(0),j1=({visible:e})=>M.createElement("div",{className:"sonner-loading-wrapper","data-visible":e},M.createElement("div",{className:"sonner-spinner"},R1.map((t,n)=>M.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),O1=M.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},M.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),L1=M.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},M.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),A1=M.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},M.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),M1=M.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},M.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),D1=()=>{let[e,t]=M.useState(document.hidden);return M.useEffect(()=>{let n=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",n),()=>window.removeEventListener("visibilitychange",n)},[]),e},Nu=1,_1=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...r}=e,o=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:Nu++,i=this.toasts.find(a=>a.id===o),s=e.dismissible===void 0?!0:e.dismissible;return i?this.toasts=this.toasts.map(a=>a.id===o?(this.publish({...a,...e,id:o,title:n}),{...a,...e,id:o,dismissible:s,title:n}):a):this.addToast({title:n,...r,dismissible:s,id:o}),o},this.dismiss=e=>(e||this.toasts.forEach(t=>{this.subscribers.forEach(n=>n({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let n;t.loading!==void 0&&(n=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let r=e instanceof Promise?e:e(),o=n!==void 0;return r.then(async i=>{if(F1(i)&&!i.ok){o=!1;let s=typeof t.error=="function"?await t.error(`HTTP error! status: ${i.status}`):t.error,a=typeof t.description=="function"?await t.description(`HTTP error! status: ${i.status}`):t.description;this.create({id:n,type:"error",message:s,description:a})}else if(t.success!==void 0){o=!1;let s=typeof t.success=="function"?await t.success(i):t.success,a=typeof t.description=="function"?await t.description(i):t.description;this.create({id:n,type:"success",message:s,description:a})}}).catch(async i=>{if(t.error!==void 0){o=!1;let s=typeof t.error=="function"?await t.error(i):t.error,a=typeof t.description=="function"?await t.description(i):t.description;this.create({id:n,type:"error",message:s,description:a})}}).finally(()=>{var i;o&&(this.dismiss(n),n=void 0),(i=t.finally)==null||i.call(t)}),n},this.custom=(e,t)=>{let n=(t==null?void 0:t.id)||Nu++;return this.create({jsx:e(n),id:n,...t}),n},this.subscribers=[],this.toasts=[]}},Xe=new _1,I1=(e,t)=>{let n=(t==null?void 0:t.id)||Nu++;return Xe.addToast({title:e,...t,id:n}),n},F1=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",$1=I1,z1=()=>Xe.toasts;Object.assign($1,{success:Xe.success,info:Xe.info,warning:Xe.warning,error:Xe.error,custom:Xe.custom,message:Xe.message,promise:Xe.promise,dismiss:Xe.dismiss,loading:Xe.loading},{getHistory:z1});function U1(e,{insertAt:t}={}){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t==="top"&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}U1(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function es(e){return e.label!==void 0}var B1=3,V1="32px",H1=4e3,W1=356,K1=14,Q1=20,G1=200;function Y1(...e){return e.filter(Boolean).join(" ")}var q1=e=>{var t,n,r,o,i,s,a,l,u,d;let{invert:f,toast:c,unstyled:v,interacting:x,setHeights:h,visibleToasts:S,heights:g,index:m,toasts:w,expanded:b,removeToast:C,defaultRichColors:k,closeButton:N,style:P,cancelButtonStyle:O,actionButtonStyle:R,className:$="",descriptionClassName:I="",duration:B,position:A,gap:G,loadingIcon:z,expandByDefault:V,classNames:E,icons:L,closeButtonAriaLabel:F="Close toast",pauseWhenPageIsHidden:D,cn:U}=e,[W,ne]=M.useState(!1),[je,J]=M.useState(!1),[ht,tn]=M.useState(!1),[nn,rn]=M.useState(!1),[Li,hr]=M.useState(0),[Hn,So]=M.useState(0),Ai=M.useRef(null),on=M.useRef(null),Fa=m===0,$a=m+1<=S,Ne=c.type,mr=c.dismissible!==!1,Vy=c.className||"",Hy=c.descriptionClassName||"",Mi=M.useMemo(()=>g.findIndex(K=>K.toastId===c.id)||0,[g,c.id]),Wy=M.useMemo(()=>{var K;return(K=c.closeButton)!=null?K:N},[c.closeButton,N]),md=M.useMemo(()=>c.duration||B||H1,[c.duration,B]),za=M.useRef(0),gr=M.useRef(0),gd=M.useRef(0),vr=M.useRef(null),[vd,Ky]=A.split("-"),yd=M.useMemo(()=>g.reduce((K,ie,re)=>re>=Mi?K:K+ie.height,0),[g,Mi]),xd=D1(),Qy=c.invert||f,Ua=Ne==="loading";gr.current=M.useMemo(()=>Mi*G+yd,[Mi,yd]),M.useEffect(()=>{ne(!0)},[]),M.useLayoutEffect(()=>{if(!W)return;let K=on.current,ie=K.style.height;K.style.height="auto";let re=K.getBoundingClientRect().height;K.style.height=ie,So(re),h(Pt=>Pt.find(Tt=>Tt.toastId===c.id)?Pt.map(Tt=>Tt.toastId===c.id?{...Tt,height:re}:Tt):[{toastId:c.id,height:re,position:c.position},...Pt])},[W,c.title,c.description,h,c.id]);let sn=M.useCallback(()=>{J(!0),hr(gr.current),h(K=>K.filter(ie=>ie.toastId!==c.id)),setTimeout(()=>{C(c)},G1)},[c,C,h,gr]);M.useEffect(()=>{if(c.promise&&Ne==="loading"||c.duration===1/0||c.type==="loading")return;let K,ie=md;return b||x||D&&xd?(()=>{if(gd.current<za.current){let re=new Date().getTime()-za.current;ie=ie-re}gd.current=new Date().getTime()})():ie!==1/0&&(za.current=new Date().getTime(),K=setTimeout(()=>{var re;(re=c.onAutoClose)==null||re.call(c,c),sn()},ie)),()=>clearTimeout(K)},[b,x,V,c,md,sn,c.promise,Ne,D,xd]),M.useEffect(()=>{let K=on.current;if(K){let ie=K.getBoundingClientRect().height;return So(ie),h(re=>[{toastId:c.id,height:ie,position:c.position},...re]),()=>h(re=>re.filter(Pt=>Pt.toastId!==c.id))}},[h,c.id]),M.useEffect(()=>{c.delete&&sn()},[sn,c.delete]);function Gy(){return L!=null&&L.loading?M.createElement("div",{className:"sonner-loader","data-visible":Ne==="loading"},L.loading):z?M.createElement("div",{className:"sonner-loader","data-visible":Ne==="loading"},z):M.createElement(j1,{visible:Ne==="loading"})}return M.createElement("li",{"aria-live":c.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:on,className:U($,Vy,E==null?void 0:E.toast,(t=c==null?void 0:c.classNames)==null?void 0:t.toast,E==null?void 0:E.default,E==null?void 0:E[Ne],(n=c==null?void 0:c.classNames)==null?void 0:n[Ne]),"data-sonner-toast":"","data-rich-colors":(r=c.richColors)!=null?r:k,"data-styled":!(c.jsx||c.unstyled||v),"data-mounted":W,"data-promise":!!c.promise,"data-removed":je,"data-visible":$a,"data-y-position":vd,"data-x-position":Ky,"data-index":m,"data-front":Fa,"data-swiping":ht,"data-dismissible":mr,"data-type":Ne,"data-invert":Qy,"data-swipe-out":nn,"data-expanded":!!(b||V&&W),style:{"--index":m,"--toasts-before":m,"--z-index":w.length-m,"--offset":`${je?Li:gr.current}px`,"--initial-height":V?"auto":`${Hn}px`,...P,...c.style},onPointerDown:K=>{Ua||!mr||(Ai.current=new Date,hr(gr.current),K.target.setPointerCapture(K.pointerId),K.target.tagName!=="BUTTON"&&(tn(!0),vr.current={x:K.clientX,y:K.clientY}))},onPointerUp:()=>{var K,ie,re,Pt;if(nn||!mr)return;vr.current=null;let Tt=Number(((K=on.current)==null?void 0:K.style.getPropertyValue("--swipe-amount").replace("px",""))||0),Di=new Date().getTime()-((ie=Ai.current)==null?void 0:ie.getTime()),Yy=Math.abs(Tt)/Di;if(Math.abs(Tt)>=Q1||Yy>.11){hr(gr.current),(re=c.onDismiss)==null||re.call(c,c),sn(),rn(!0);return}(Pt=on.current)==null||Pt.style.setProperty("--swipe-amount","0px"),tn(!1)},onPointerMove:K=>{var ie;if(!vr.current||!mr)return;let re=K.clientY-vr.current.y,Pt=K.clientX-vr.current.x,Tt=(vd==="top"?Math.min:Math.max)(0,re),Di=K.pointerType==="touch"?10:2;Math.abs(Tt)>Di?(ie=on.current)==null||ie.style.setProperty("--swipe-amount",`${re}px`):Math.abs(Pt)>Di&&(vr.current=null)}},Wy&&!c.jsx?M.createElement("button",{"aria-label":F,"data-disabled":Ua,"data-close-button":!0,onClick:Ua||!mr?()=>{}:()=>{var K;sn(),(K=c.onDismiss)==null||K.call(c,c)},className:U(E==null?void 0:E.closeButton,(o=c==null?void 0:c.classNames)==null?void 0:o.closeButton)},M.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},M.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),M.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,c.jsx||M.isValidElement(c.title)?c.jsx||c.title:M.createElement(M.Fragment,null,Ne||c.icon||c.promise?M.createElement("div",{"data-icon":"",className:U(E==null?void 0:E.icon,(i=c==null?void 0:c.classNames)==null?void 0:i.icon)},c.promise||c.type==="loading"&&!c.icon?c.icon||Gy():null,c.type!=="loading"?c.icon||(L==null?void 0:L[Ne])||T1(Ne):null):null,M.createElement("div",{"data-content":"",className:U(E==null?void 0:E.content,(s=c==null?void 0:c.classNames)==null?void 0:s.content)},M.createElement("div",{"data-title":"",className:U(E==null?void 0:E.title,(a=c==null?void 0:c.classNames)==null?void 0:a.title)},c.title),c.description?M.createElement("div",{"data-description":"",className:U(I,Hy,E==null?void 0:E.description,(l=c==null?void 0:c.classNames)==null?void 0:l.description)},c.description):null),M.isValidElement(c.cancel)?c.cancel:c.cancel&&es(c.cancel)?M.createElement("button",{"data-button":!0,"data-cancel":!0,style:c.cancelButtonStyle||O,onClick:K=>{var ie,re;es(c.cancel)&&mr&&((re=(ie=c.cancel).onClick)==null||re.call(ie,K),sn())},className:U(E==null?void 0:E.cancelButton,(u=c==null?void 0:c.classNames)==null?void 0:u.cancelButton)},c.cancel.label):null,M.isValidElement(c.action)?c.action:c.action&&es(c.action)?M.createElement("button",{"data-button":!0,"data-action":!0,style:c.actionButtonStyle||R,onClick:K=>{var ie,re;es(c.action)&&(K.defaultPrevented||((re=(ie=c.action).onClick)==null||re.call(ie,K),sn()))},className:U(E==null?void 0:E.actionButton,(d=c==null?void 0:c.classNames)==null?void 0:d.actionButton)},c.action.label):null))};function Lf(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}var X1=e=>{let{invert:t,position:n="bottom-right",hotkey:r=["altKey","KeyT"],expand:o,closeButton:i,className:s,offset:a,theme:l="light",richColors:u,duration:d,style:f,visibleToasts:c=B1,toastOptions:v,dir:x=Lf(),gap:h=K1,loadingIcon:S,icons:g,containerAriaLabel:m="Notifications",pauseWhenPageIsHidden:w,cn:b=Y1}=e,[C,k]=M.useState([]),N=M.useMemo(()=>Array.from(new Set([n].concat(C.filter(D=>D.position).map(D=>D.position)))),[C,n]),[P,O]=M.useState([]),[R,$]=M.useState(!1),[I,B]=M.useState(!1),[A,G]=M.useState(l!=="system"?l:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),z=M.useRef(null),V=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),E=M.useRef(null),L=M.useRef(!1),F=M.useCallback(D=>{var U;(U=C.find(W=>W.id===D.id))!=null&&U.delete||Xe.dismiss(D.id),k(W=>W.filter(({id:ne})=>ne!==D.id))},[C]);return M.useEffect(()=>Xe.subscribe(D=>{if(D.dismiss){k(U=>U.map(W=>W.id===D.id?{...W,delete:!0}:W));return}setTimeout(()=>{vg.flushSync(()=>{k(U=>{let W=U.findIndex(ne=>ne.id===D.id);return W!==-1?[...U.slice(0,W),{...U[W],...D},...U.slice(W+1)]:[D,...U]})})})}),[]),M.useEffect(()=>{if(l!=="system"){G(l);return}l==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?G("dark"):G("light")),typeof window<"u"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",({matches:D})=>{G(D?"dark":"light")})},[l]),M.useEffect(()=>{C.length<=1&&$(!1)},[C]),M.useEffect(()=>{let D=U=>{var W,ne;r.every(je=>U[je]||U.code===je)&&($(!0),(W=z.current)==null||W.focus()),U.code==="Escape"&&(document.activeElement===z.current||(ne=z.current)!=null&&ne.contains(document.activeElement))&&$(!1)};return document.addEventListener("keydown",D),()=>document.removeEventListener("keydown",D)},[r]),M.useEffect(()=>{if(z.current)return()=>{E.current&&(E.current.focus({preventScroll:!0}),E.current=null,L.current=!1)}},[z.current]),C.length?M.createElement("section",{"aria-label":`${m} ${V}`,tabIndex:-1},N.map((D,U)=>{var W;let[ne,je]=D.split("-");return M.createElement("ol",{key:D,dir:x==="auto"?Lf():x,tabIndex:-1,ref:z,className:s,"data-sonner-toaster":!0,"data-theme":A,"data-y-position":ne,"data-x-position":je,style:{"--front-toast-height":`${((W=P[0])==null?void 0:W.height)||0}px`,"--offset":typeof a=="number"?`${a}px`:a||V1,"--width":`${W1}px`,"--gap":`${h}px`,...f},onBlur:J=>{L.current&&!J.currentTarget.contains(J.relatedTarget)&&(L.current=!1,E.current&&(E.current.focus({preventScroll:!0}),E.current=null))},onFocus:J=>{J.target instanceof HTMLElement&&J.target.dataset.dismissible==="false"||L.current||(L.current=!0,E.current=J.relatedTarget)},onMouseEnter:()=>$(!0),onMouseMove:()=>$(!0),onMouseLeave:()=>{I||$(!1)},onPointerDown:J=>{J.target instanceof HTMLElement&&J.target.dataset.dismissible==="false"||B(!0)},onPointerUp:()=>B(!1)},C.filter(J=>!J.position&&U===0||J.position===D).map((J,ht)=>{var tn,nn;return M.createElement(q1,{key:J.id,icons:g,index:ht,toast:J,defaultRichColors:u,duration:(tn=v==null?void 0:v.duration)!=null?tn:d,className:v==null?void 0:v.className,descriptionClassName:v==null?void 0:v.descriptionClassName,invert:t,visibleToasts:c,closeButton:(nn=v==null?void 0:v.closeButton)!=null?nn:i,interacting:I,position:D,style:v==null?void 0:v.style,unstyled:v==null?void 0:v.unstyled,classNames:v==null?void 0:v.classNames,cancelButtonStyle:v==null?void 0:v.cancelButtonStyle,actionButtonStyle:v==null?void 0:v.actionButtonStyle,removeToast:F,toasts:C.filter(rn=>rn.position==J.position),heights:P.filter(rn=>rn.position==J.position),setHeights:O,expandByDefault:o,gap:h,loadingIcon:S,expanded:R,pauseWhenPageIsHidden:w,cn:b})}))})):null};const Z1=({...e})=>{const{theme:t="system"}=P1();return p.jsx(X1,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})},J1=1,ew=1e6;let gl=0;function tw(){return gl=(gl+1)%Number.MAX_SAFE_INTEGER,gl.toString()}const vl=new Map,Af=e=>{if(vl.has(e))return;const t=setTimeout(()=>{vl.delete(e),Ko({type:"REMOVE_TOAST",toastId:e})},ew);vl.set(e,t)},nw=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,J1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?Af(n):e.toasts.forEach(r=>{Af(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},bs=[];let Cs={toasts:[]};function Ko(e){Cs=nw(Cs,e),bs.forEach(t=>{t(Cs)})}function rw({...e}){const t=tw(),n=o=>Ko({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>Ko({type:"DISMISS_TOAST",toastId:t});return Ko({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function xg(){const[e,t]=y.useState(Cs);return y.useEffect(()=>(bs.push(t),()=>{const n=bs.indexOf(t);n>-1&&bs.splice(n,1)}),[e]),{...e,toast:rw,dismiss:n=>Ko({type:"DISMISS_TOAST",toastId:n})}}function ce(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function ow(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function wg(...e){return t=>e.forEach(n=>ow(n,t))}function Be(...e){return y.useCallback(wg(...e),e)}function iw(e,t=[]){let n=[];function r(i,s){const a=y.createContext(s),l=n.length;n=[...n,s];function u(f){const{scope:c,children:v,...x}=f,h=(c==null?void 0:c[e][l])||a,S=y.useMemo(()=>x,Object.values(x));return p.jsx(h.Provider,{value:S,children:v})}function d(f,c){const v=(c==null?void 0:c[e][l])||a,x=y.useContext(v);if(x)return x;if(s!==void 0)return s;throw new Error(`\`${f}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,d]}const o=()=>{const i=n.map(s=>y.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return y.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,sw(o,...t)]}function sw(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const f=l(i)[`__scope${u}`];return{...a,...f}},{});return y.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var lo=y.forwardRef((e,t)=>{const{children:n,...r}=e,o=y.Children.toArray(n),i=o.find(aw);if(i){const s=i.props.children,a=o.map(l=>l===i?y.Children.count(s)>1?y.Children.only(null):y.isValidElement(s)?s.props.children:null:l);return p.jsx(Pu,{...r,ref:t,children:y.isValidElement(s)?y.cloneElement(s,void 0,a):null})}return p.jsx(Pu,{...r,ref:t,children:n})});lo.displayName="Slot";var Pu=y.forwardRef((e,t)=>{const{children:n,...r}=e;if(y.isValidElement(n)){const o=uw(n);return y.cloneElement(n,{...lw(r,n.props),ref:t?wg(t,o):o})}return y.Children.count(n)>1?y.Children.only(null):null});Pu.displayName="SlotClone";var Sg=({children:e})=>p.jsx(p.Fragment,{children:e});function aw(e){return y.isValidElement(e)&&e.type===Sg}function lw(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{i(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function uw(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function cw(e){const t=e+"CollectionProvider",[n,r]=iw(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=v=>{const{scope:x,children:h}=v,S=M.useRef(null),g=M.useRef(new Map).current;return p.jsx(o,{scope:x,itemMap:g,collectionRef:S,children:h})};s.displayName=t;const a=e+"CollectionSlot",l=M.forwardRef((v,x)=>{const{scope:h,children:S}=v,g=i(a,h),m=Be(x,g.collectionRef);return p.jsx(lo,{ref:m,children:S})});l.displayName=a;const u=e+"CollectionItemSlot",d="data-radix-collection-item",f=M.forwardRef((v,x)=>{const{scope:h,children:S,...g}=v,m=M.useRef(null),w=Be(x,m),b=i(u,h);return M.useEffect(()=>(b.itemMap.set(m,{ref:m,...g}),()=>void b.itemMap.delete(m))),p.jsx(lo,{[d]:"",ref:w,children:S})});f.displayName=u;function c(v){const x=i(e+"CollectionConsumer",v);return M.useCallback(()=>{const S=x.collectionRef.current;if(!S)return[];const g=Array.from(S.querySelectorAll(`[${d}]`));return Array.from(x.itemMap.values()).sort((b,C)=>g.indexOf(b.ref.current)-g.indexOf(C.ref.current))},[x.collectionRef,x.itemMap])}return[{Provider:s,Slot:l,ItemSlot:f},c,r]}function dw(e,t){const n=y.createContext(t),r=i=>{const{children:s,...a}=i,l=y.useMemo(()=>a,Object.values(a));return p.jsx(n.Provider,{value:l,children:s})};r.displayName=e+"Provider";function o(i){const s=y.useContext(n);if(s)return s;if(t!==void 0)return t;throw new Error(`\`${i}\` must be used within \`${e}\``)}return[r,o]}function Bc(e,t=[]){let n=[];function r(i,s){const a=y.createContext(s),l=n.length;n=[...n,s];const u=f=>{var g;const{scope:c,children:v,...x}=f,h=((g=c==null?void 0:c[e])==null?void 0:g[l])||a,S=y.useMemo(()=>x,Object.values(x));return p.jsx(h.Provider,{value:S,children:v})};u.displayName=i+"Provider";function d(f,c){var h;const v=((h=c==null?void 0:c[e])==null?void 0:h[l])||a,x=y.useContext(v);if(x)return x;if(s!==void 0)return s;throw new Error(`\`${f}\` must be used within \`${i}\``)}return[u,d]}const o=()=>{const i=n.map(s=>y.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return y.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,fw(o,...t)]}function fw(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const f=l(i)[`__scope${u}`];return{...a,...f}},{});return y.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var pw=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],xe=pw.reduce((e,t)=>{const n=y.forwardRef((r,o)=>{const{asChild:i,...s}=r,a=i?lo:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),p.jsx(a,{...s,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function bg(e,t){e&&Pi.flushSync(()=>e.dispatchEvent(t))}function pt(e){const t=y.useRef(e);return y.useEffect(()=>{t.current=e}),y.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function hw(e,t=globalThis==null?void 0:globalThis.document){const n=pt(e);y.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var mw="DismissableLayer",Tu="dismissableLayer.update",gw="dismissableLayer.pointerDownOutside",vw="dismissableLayer.focusOutside",Mf,Cg=y.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Ea=y.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:a,...l}=e,u=y.useContext(Cg),[d,f]=y.useState(null),c=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,v]=y.useState({}),x=Be(t,N=>f(N)),h=Array.from(u.layers),[S]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),g=h.indexOf(S),m=d?h.indexOf(d):-1,w=u.layersWithOutsidePointerEventsDisabled.size>0,b=m>=g,C=xw(N=>{const P=N.target,O=[...u.branches].some(R=>R.contains(P));!b||O||(o==null||o(N),s==null||s(N),N.defaultPrevented||a==null||a())},c),k=ww(N=>{const P=N.target;[...u.branches].some(R=>R.contains(P))||(i==null||i(N),s==null||s(N),N.defaultPrevented||a==null||a())},c);return hw(N=>{m===u.layers.size-1&&(r==null||r(N),!N.defaultPrevented&&a&&(N.preventDefault(),a()))},c),y.useEffect(()=>{if(d)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Mf=c.body.style.pointerEvents,c.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),Df(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(c.body.style.pointerEvents=Mf)}},[d,c,n,u]),y.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),Df())},[d,u]),y.useEffect(()=>{const N=()=>v({});return document.addEventListener(Tu,N),()=>document.removeEventListener(Tu,N)},[]),p.jsx(xe.div,{...l,ref:x,style:{pointerEvents:w?b?"auto":"none":void 0,...e.style},onFocusCapture:ce(e.onFocusCapture,k.onFocusCapture),onBlurCapture:ce(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:ce(e.onPointerDownCapture,C.onPointerDownCapture)})});Ea.displayName=mw;var yw="DismissableLayerBranch",Eg=y.forwardRef((e,t)=>{const n=y.useContext(Cg),r=y.useRef(null),o=Be(t,r);return y.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),p.jsx(xe.div,{...e,ref:o})});Eg.displayName=yw;function xw(e,t=globalThis==null?void 0:globalThis.document){const n=pt(e),r=y.useRef(!1),o=y.useRef(()=>{});return y.useEffect(()=>{const i=a=>{if(a.target&&!r.current){let l=function(){kg(gw,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function ww(e,t=globalThis==null?void 0:globalThis.document){const n=pt(e),r=y.useRef(!1);return y.useEffect(()=>{const o=i=>{i.target&&!r.current&&kg(vw,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Df(){const e=new CustomEvent(Tu);document.dispatchEvent(e)}function kg(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?bg(o,i):o.dispatchEvent(i)}var Sw=Ea,bw=Eg,Dn=globalThis!=null&&globalThis.document?y.useLayoutEffect:()=>{},Cw="Portal",Vc=y.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,i]=y.useState(!1);Dn(()=>i(!0),[]);const s=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return s?vg.createPortal(p.jsx(xe.div,{...r,ref:t}),s):null});Vc.displayName=Cw;function Ew(e,t){return y.useReducer((n,r)=>t[n][r]??n,e)}var go=e=>{const{present:t,children:n}=e,r=kw(t),o=typeof n=="function"?n({present:r.isPresent}):y.Children.only(n),i=Be(r.ref,Nw(o));return typeof n=="function"||r.isPresent?y.cloneElement(o,{ref:i}):null};go.displayName="Presence";function kw(e){const[t,n]=y.useState(),r=y.useRef({}),o=y.useRef(e),i=y.useRef("none"),s=e?"mounted":"unmounted",[a,l]=Ew(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return y.useEffect(()=>{const u=ts(r.current);i.current=a==="mounted"?u:"none"},[a]),Dn(()=>{const u=r.current,d=o.current;if(d!==e){const c=i.current,v=ts(u);e?l("MOUNT"):v==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(d&&c!==v?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),Dn(()=>{if(t){let u;const d=t.ownerDocument.defaultView??window,f=v=>{const h=ts(r.current).includes(v.animationName);if(v.target===t&&h&&(l("ANIMATION_END"),!o.current)){const S=t.style.animationFillMode;t.style.animationFillMode="forwards",u=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=S)})}},c=v=>{v.target===t&&(i.current=ts(r.current))};return t.addEventListener("animationstart",c),t.addEventListener("animationcancel",f),t.addEventListener("animationend",f),()=>{d.clearTimeout(u),t.removeEventListener("animationstart",c),t.removeEventListener("animationcancel",f),t.removeEventListener("animationend",f)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:y.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function ts(e){return(e==null?void 0:e.animationName)||"none"}function Nw(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Ng({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=Pw({defaultProp:t,onChange:n}),i=e!==void 0,s=i?e:r,a=pt(n),l=y.useCallback(u=>{if(i){const f=typeof u=="function"?u(e):u;f!==e&&a(f)}else o(u)},[i,e,o,a]);return[s,l]}function Pw({defaultProp:e,onChange:t}){const n=y.useState(e),[r]=n,o=y.useRef(r),i=pt(t);return y.useEffect(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}var Tw="VisuallyHidden",ka=y.forwardRef((e,t)=>p.jsx(xe.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));ka.displayName=Tw;var Rw=ka,Hc="ToastProvider",[Wc,jw,Ow]=cw("Toast"),[Pg,MN]=Bc("Toast",[Ow]),[Lw,Na]=Pg(Hc),Tg=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:i=50,children:s}=e,[a,l]=y.useState(null),[u,d]=y.useState(0),f=y.useRef(!1),c=y.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${Hc}\`. Expected non-empty \`string\`.`),p.jsx(Wc.Provider,{scope:t,children:p.jsx(Lw,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:i,toastCount:u,viewport:a,onViewportChange:l,onToastAdd:y.useCallback(()=>d(v=>v+1),[]),onToastRemove:y.useCallback(()=>d(v=>v-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:c,children:s})})};Tg.displayName=Hc;var Rg="ToastViewport",Aw=["F8"],Ru="toast.viewportPause",ju="toast.viewportResume",jg=y.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=Aw,label:o="Notifications ({hotkey})",...i}=e,s=Na(Rg,n),a=jw(n),l=y.useRef(null),u=y.useRef(null),d=y.useRef(null),f=y.useRef(null),c=Be(t,f,s.onViewportChange),v=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=s.toastCount>0;y.useEffect(()=>{const S=g=>{var w;r.length!==0&&r.every(b=>g[b]||g.code===b)&&((w=f.current)==null||w.focus())};return document.addEventListener("keydown",S),()=>document.removeEventListener("keydown",S)},[r]),y.useEffect(()=>{const S=l.current,g=f.current;if(x&&S&&g){const m=()=>{if(!s.isClosePausedRef.current){const k=new CustomEvent(Ru);g.dispatchEvent(k),s.isClosePausedRef.current=!0}},w=()=>{if(s.isClosePausedRef.current){const k=new CustomEvent(ju);g.dispatchEvent(k),s.isClosePausedRef.current=!1}},b=k=>{!S.contains(k.relatedTarget)&&w()},C=()=>{S.contains(document.activeElement)||w()};return S.addEventListener("focusin",m),S.addEventListener("focusout",b),S.addEventListener("pointermove",m),S.addEventListener("pointerleave",C),window.addEventListener("blur",m),window.addEventListener("focus",w),()=>{S.removeEventListener("focusin",m),S.removeEventListener("focusout",b),S.removeEventListener("pointermove",m),S.removeEventListener("pointerleave",C),window.removeEventListener("blur",m),window.removeEventListener("focus",w)}}},[x,s.isClosePausedRef]);const h=y.useCallback(({tabbingDirection:S})=>{const m=a().map(w=>{const b=w.ref.current,C=[b,...Kw(b)];return S==="forwards"?C:C.reverse()});return(S==="forwards"?m.reverse():m).flat()},[a]);return y.useEffect(()=>{const S=f.current;if(S){const g=m=>{var C,k,N;const w=m.altKey||m.ctrlKey||m.metaKey;if(m.key==="Tab"&&!w){const P=document.activeElement,O=m.shiftKey;if(m.target===S&&O){(C=u.current)==null||C.focus();return}const I=h({tabbingDirection:O?"backwards":"forwards"}),B=I.findIndex(A=>A===P);yl(I.slice(B+1))?m.preventDefault():O?(k=u.current)==null||k.focus():(N=d.current)==null||N.focus()}};return S.addEventListener("keydown",g),()=>S.removeEventListener("keydown",g)}},[a,h]),p.jsxs(bw,{ref:l,role:"region","aria-label":o.replace("{hotkey}",v),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&p.jsx(Ou,{ref:u,onFocusFromOutsideViewport:()=>{const S=h({tabbingDirection:"forwards"});yl(S)}}),p.jsx(Wc.Slot,{scope:n,children:p.jsx(xe.ol,{tabIndex:-1,...i,ref:c})}),x&&p.jsx(Ou,{ref:d,onFocusFromOutsideViewport:()=>{const S=h({tabbingDirection:"backwards"});yl(S)}})]})});jg.displayName=Rg;var Og="ToastFocusProxy",Ou=y.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=Na(Og,n);return p.jsx(ka,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:s=>{var u;const a=s.relatedTarget;!((u=i.viewport)!=null&&u.contains(a))&&r()}})});Ou.displayName=Og;var Pa="Toast",Mw="toast.swipeStart",Dw="toast.swipeMove",_w="toast.swipeCancel",Iw="toast.swipeEnd",Lg=y.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:i,...s}=e,[a=!0,l]=Ng({prop:r,defaultProp:o,onChange:i});return p.jsx(go,{present:n||a,children:p.jsx(zw,{open:a,...s,ref:t,onClose:()=>l(!1),onPause:pt(e.onPause),onResume:pt(e.onResume),onSwipeStart:ce(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:ce(e.onSwipeMove,u=>{const{x:d,y:f}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${d}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${f}px`)}),onSwipeCancel:ce(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:ce(e.onSwipeEnd,u=>{const{x:d,y:f}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${d}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${f}px`),l(!1)})})})});Lg.displayName=Pa;var[Fw,$w]=Pg(Pa,{onClose(){}}),zw=y.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:i,onClose:s,onEscapeKeyDown:a,onPause:l,onResume:u,onSwipeStart:d,onSwipeMove:f,onSwipeCancel:c,onSwipeEnd:v,...x}=e,h=Na(Pa,n),[S,g]=y.useState(null),m=Be(t,A=>g(A)),w=y.useRef(null),b=y.useRef(null),C=o||h.duration,k=y.useRef(0),N=y.useRef(C),P=y.useRef(0),{onToastAdd:O,onToastRemove:R}=h,$=pt(()=>{var G;(S==null?void 0:S.contains(document.activeElement))&&((G=h.viewport)==null||G.focus()),s()}),I=y.useCallback(A=>{!A||A===1/0||(window.clearTimeout(P.current),k.current=new Date().getTime(),P.current=window.setTimeout($,A))},[$]);y.useEffect(()=>{const A=h.viewport;if(A){const G=()=>{I(N.current),u==null||u()},z=()=>{const V=new Date().getTime()-k.current;N.current=N.current-V,window.clearTimeout(P.current),l==null||l()};return A.addEventListener(Ru,z),A.addEventListener(ju,G),()=>{A.removeEventListener(Ru,z),A.removeEventListener(ju,G)}}},[h.viewport,C,l,u,I]),y.useEffect(()=>{i&&!h.isClosePausedRef.current&&I(C)},[i,C,h.isClosePausedRef,I]),y.useEffect(()=>(O(),()=>R()),[O,R]);const B=y.useMemo(()=>S?$g(S):null,[S]);return h.viewport?p.jsxs(p.Fragment,{children:[B&&p.jsx(Uw,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:B}),p.jsx(Fw,{scope:n,onClose:$,children:Pi.createPortal(p.jsx(Wc.ItemSlot,{scope:n,children:p.jsx(Sw,{asChild:!0,onEscapeKeyDown:ce(a,()=>{h.isFocusedToastEscapeKeyDownRef.current||$(),h.isFocusedToastEscapeKeyDownRef.current=!1}),children:p.jsx(xe.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":h.swipeDirection,...x,ref:m,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:ce(e.onKeyDown,A=>{A.key==="Escape"&&(a==null||a(A.nativeEvent),A.nativeEvent.defaultPrevented||(h.isFocusedToastEscapeKeyDownRef.current=!0,$()))}),onPointerDown:ce(e.onPointerDown,A=>{A.button===0&&(w.current={x:A.clientX,y:A.clientY})}),onPointerMove:ce(e.onPointerMove,A=>{if(!w.current)return;const G=A.clientX-w.current.x,z=A.clientY-w.current.y,V=!!b.current,E=["left","right"].includes(h.swipeDirection),L=["left","up"].includes(h.swipeDirection)?Math.min:Math.max,F=E?L(0,G):0,D=E?0:L(0,z),U=A.pointerType==="touch"?10:2,W={x:F,y:D},ne={originalEvent:A,delta:W};V?(b.current=W,ns(Dw,f,ne,{discrete:!1})):_f(W,h.swipeDirection,U)?(b.current=W,ns(Mw,d,ne,{discrete:!1}),A.target.setPointerCapture(A.pointerId)):(Math.abs(G)>U||Math.abs(z)>U)&&(w.current=null)}),onPointerUp:ce(e.onPointerUp,A=>{const G=b.current,z=A.target;if(z.hasPointerCapture(A.pointerId)&&z.releasePointerCapture(A.pointerId),b.current=null,w.current=null,G){const V=A.currentTarget,E={originalEvent:A,delta:G};_f(G,h.swipeDirection,h.swipeThreshold)?ns(Iw,v,E,{discrete:!0}):ns(_w,c,E,{discrete:!0}),V.addEventListener("click",L=>L.preventDefault(),{once:!0})}})})})}),h.viewport)})]}):null}),Uw=e=>{const{__scopeToast:t,children:n,...r}=e,o=Na(Pa,t),[i,s]=y.useState(!1),[a,l]=y.useState(!1);return Hw(()=>s(!0)),y.useEffect(()=>{const u=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(u)},[]),a?null:p.jsx(Vc,{asChild:!0,children:p.jsx(ka,{...r,children:i&&p.jsxs(p.Fragment,{children:[o.label," ",n]})})})},Bw="ToastTitle",Ag=y.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return p.jsx(xe.div,{...r,ref:t})});Ag.displayName=Bw;var Vw="ToastDescription",Mg=y.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return p.jsx(xe.div,{...r,ref:t})});Mg.displayName=Vw;var Dg="ToastAction",_g=y.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?p.jsx(Fg,{altText:n,asChild:!0,children:p.jsx(Kc,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Dg}\`. Expected non-empty \`string\`.`),null)});_g.displayName=Dg;var Ig="ToastClose",Kc=y.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=$w(Ig,n);return p.jsx(Fg,{asChild:!0,children:p.jsx(xe.button,{type:"button",...r,ref:t,onClick:ce(e.onClick,o.onClose)})})});Kc.displayName=Ig;var Fg=y.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return p.jsx(xe.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function $g(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),Ww(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",i=r.dataset.radixToastAnnounceExclude==="";if(!o)if(i){const s=r.dataset.radixToastAnnounceAlt;s&&t.push(s)}else t.push(...$g(r))}}),t}function ns(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?bg(o,i):o.dispatchEvent(i)}var _f=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return t==="left"||t==="right"?i&&r>n:!i&&o>n};function Hw(e=()=>{}){const t=pt(e);Dn(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function Ww(e){return e.nodeType===e.ELEMENT_NODE}function Kw(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function yl(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var Qw=Tg,zg=jg,Ug=Lg,Bg=Ag,Vg=Mg,Hg=_g,Wg=Kc;function Kg(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Kg(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Qg(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Kg(e))&&(r&&(r+=" "),r+=t);return r}const If=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Ff=Qg,Qc=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return Ff(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,s=Object.keys(o).map(u=>{const d=n==null?void 0:n[u],f=i==null?void 0:i[u];if(d===null)return null;const c=If(d)||If(f);return o[u][c]}),a=n&&Object.entries(n).reduce((u,d)=>{let[f,c]=d;return c===void 0||(u[f]=c),u},{}),l=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,d)=>{let{class:f,className:c,...v}=d;return Object.entries(v).every(x=>{let[h,S]=x;return Array.isArray(S)?S.includes({...i,...a}[h]):{...i,...a}[h]===S})?[...u,f,c]:u},[]);return Ff(e,s,l,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gw=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Gg=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Yw={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qw=y.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:s,...a},l)=>y.createElement("svg",{ref:l,...Yw,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Gg("lucide",o),...a},[...s.map(([u,d])=>y.createElement(u,d)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const te=(e,t)=>{const n=y.forwardRef(({className:r,...o},i)=>y.createElement(qw,{ref:i,iconNode:t,className:Gg(`lucide-${Gw(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xw=te("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yg=te("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uo=te("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $f=te("Cog",[["path",{d:"M12 20a8 8 0 1 0 0-16 8 8 0 0 0 0 16Z",key:"sobvz5"}],["path",{d:"M12 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z",key:"11i496"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 22v-2",key:"1osdcq"}],["path",{d:"m17 20.66-1-1.73",key:"eq3orb"}],["path",{d:"M11 10.27 7 3.34",key:"16pf9h"}],["path",{d:"m20.66 17-1.73-1",key:"sg0v6f"}],["path",{d:"m3.34 7 1.73 1",key:"1ulond"}],["path",{d:"M14 12h8",key:"4f43i9"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"m20.66 7-1.73 1",key:"1ow05n"}],["path",{d:"m3.34 17 1.73-1",key:"nuk764"}],["path",{d:"m17 3.34-1 1.73",key:"2wel8s"}],["path",{d:"m11 13.73-4 6.93",key:"794ttg"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zf=te("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zw=te("Cpu",[["rect",{width:"16",height:"16",x:"4",y:"4",rx:"2",key:"14l7u7"}],["rect",{width:"6",height:"6",x:"9",y:"9",rx:"1",key:"5aljv4"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uf=te("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jw=te("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qg=te("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xg=te("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gc=te("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eS=te("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zg=te("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tS=te("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nS=te("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rS=te("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oS=te("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jg=te("Rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bf=te("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vf=te("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iS=te("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sS=te("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aS=te("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lS=te("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uS=te("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hf=te("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yc=te("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const di=te("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),qc="-",cS=e=>{const t=fS(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:s=>{const a=s.split(qc);return a[0]===""&&a.length!==1&&a.shift(),ev(a,t)||dS(s)},getConflictingClassGroupIds:(s,a)=>{const l=n[s]||[];return a&&r[s]?[...l,...r[s]]:l}}},ev=(e,t)=>{var s;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?ev(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(qc);return(s=t.validators.find(({validator:a})=>a(i)))==null?void 0:s.classGroupId},Wf=/^\[(.+)\]$/,dS=e=>{if(Wf.test(e)){const t=Wf.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},fS=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return hS(Object.entries(e.classGroups),n).forEach(([i,s])=>{Lu(s,r,i,t)}),r},Lu=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:Kf(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(pS(o)){Lu(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,s])=>{Lu(s,Kf(t,i),n,r)})})},Kf=(e,t)=>{let n=e;return t.split(qc).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},pS=e=>e.isThemeGetter,hS=(e,t)=>t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([s,a])=>[t+s,a])):i);return[n,o]}):e,mS=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(i,s)=>{n.set(i,s),t++,t>e&&(t=0,r=n,n=new Map)};return{get(i){let s=n.get(i);if(s!==void 0)return s;if((s=r.get(i))!==void 0)return o(i,s),s},set(i,s){n.has(i)?n.set(i,s):o(i,s)}}},tv="!",gS=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],i=t.length,s=a=>{const l=[];let u=0,d=0,f;for(let S=0;S<a.length;S++){let g=a[S];if(u===0){if(g===o&&(r||a.slice(S,S+i)===t)){l.push(a.slice(d,S)),d=S+i;continue}if(g==="/"){f=S;continue}}g==="["?u++:g==="]"&&u--}const c=l.length===0?a:a.substring(d),v=c.startsWith(tv),x=v?c.substring(1):c,h=f&&f>d?f-d:void 0;return{modifiers:l,hasImportantModifier:v,baseClassName:x,maybePostfixModifierPosition:h}};return n?a=>n({className:a,parseClassName:s}):s},vS=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},yS=e=>({cache:mS(e.cacheSize),parseClassName:gS(e),...cS(e)}),xS=/\s+/,wS=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=[],s=e.trim().split(xS);let a="";for(let l=s.length-1;l>=0;l-=1){const u=s[l],{modifiers:d,hasImportantModifier:f,baseClassName:c,maybePostfixModifierPosition:v}=n(u);let x=!!v,h=r(x?c.substring(0,v):c);if(!h){if(!x){a=u+(a.length>0?" "+a:a);continue}if(h=r(c),!h){a=u+(a.length>0?" "+a:a);continue}x=!1}const S=vS(d).join(":"),g=f?S+tv:S,m=g+h;if(i.includes(m))continue;i.push(m);const w=o(h,x);for(let b=0;b<w.length;++b){const C=w[b];i.push(g+C)}a=u+(a.length>0?" "+a:a)}return a};function SS(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=nv(t))&&(r&&(r+=" "),r+=n);return r}const nv=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=nv(e[r]))&&(n&&(n+=" "),n+=t);return n};function bS(e,...t){let n,r,o,i=s;function s(l){const u=t.reduce((d,f)=>f(d),e());return n=yS(u),r=n.cache.get,o=n.cache.set,i=a,a(l)}function a(l){const u=r(l);if(u)return u;const d=wS(l,n);return o(l,d),d}return function(){return i(SS.apply(null,arguments))}}const se=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},rv=/^\[(?:([a-z-]+):)?(.+)\]$/i,CS=/^\d+\/\d+$/,ES=new Set(["px","full","screen"]),kS=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,NS=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,PS=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,TS=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,RS=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ut=e=>Vr(e)||ES.has(e)||CS.test(e),ln=e=>vo(e,"length",IS),Vr=e=>!!e&&!Number.isNaN(Number(e)),xl=e=>vo(e,"number",Vr),jo=e=>!!e&&Number.isInteger(Number(e)),jS=e=>e.endsWith("%")&&Vr(e.slice(0,-1)),Q=e=>rv.test(e),un=e=>kS.test(e),OS=new Set(["length","size","percentage"]),LS=e=>vo(e,OS,ov),AS=e=>vo(e,"position",ov),MS=new Set(["image","url"]),DS=e=>vo(e,MS,$S),_S=e=>vo(e,"",FS),Oo=()=>!0,vo=(e,t,n)=>{const r=rv.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},IS=e=>NS.test(e)&&!PS.test(e),ov=()=>!1,FS=e=>TS.test(e),$S=e=>RS.test(e),zS=()=>{const e=se("colors"),t=se("spacing"),n=se("blur"),r=se("brightness"),o=se("borderColor"),i=se("borderRadius"),s=se("borderSpacing"),a=se("borderWidth"),l=se("contrast"),u=se("grayscale"),d=se("hueRotate"),f=se("invert"),c=se("gap"),v=se("gradientColorStops"),x=se("gradientColorStopPositions"),h=se("inset"),S=se("margin"),g=se("opacity"),m=se("padding"),w=se("saturate"),b=se("scale"),C=se("sepia"),k=se("skew"),N=se("space"),P=se("translate"),O=()=>["auto","contain","none"],R=()=>["auto","hidden","clip","visible","scroll"],$=()=>["auto",Q,t],I=()=>[Q,t],B=()=>["",Ut,ln],A=()=>["auto",Vr,Q],G=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],z=()=>["solid","dashed","dotted","double","none"],V=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],E=()=>["start","end","center","between","around","evenly","stretch"],L=()=>["","0",Q],F=()=>["auto","avoid","all","avoid-page","page","left","right","column"],D=()=>[Vr,Q];return{cacheSize:500,separator:":",theme:{colors:[Oo],spacing:[Ut,ln],blur:["none","",un,Q],brightness:D(),borderColor:[e],borderRadius:["none","","full",un,Q],borderSpacing:I(),borderWidth:B(),contrast:D(),grayscale:L(),hueRotate:D(),invert:L(),gap:I(),gradientColorStops:[e],gradientColorStopPositions:[jS,ln],inset:$(),margin:$(),opacity:D(),padding:I(),saturate:D(),scale:D(),sepia:L(),skew:D(),space:I(),translate:I()},classGroups:{aspect:[{aspect:["auto","square","video",Q]}],container:["container"],columns:[{columns:[un]}],"break-after":[{"break-after":F()}],"break-before":[{"break-before":F()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...G(),Q]}],overflow:[{overflow:R()}],"overflow-x":[{"overflow-x":R()}],"overflow-y":[{"overflow-y":R()}],overscroll:[{overscroll:O()}],"overscroll-x":[{"overscroll-x":O()}],"overscroll-y":[{"overscroll-y":O()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",jo,Q]}],basis:[{basis:$()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",Q]}],grow:[{grow:L()}],shrink:[{shrink:L()}],order:[{order:["first","last","none",jo,Q]}],"grid-cols":[{"grid-cols":[Oo]}],"col-start-end":[{col:["auto",{span:["full",jo,Q]},Q]}],"col-start":[{"col-start":A()}],"col-end":[{"col-end":A()}],"grid-rows":[{"grid-rows":[Oo]}],"row-start-end":[{row:["auto",{span:[jo,Q]},Q]}],"row-start":[{"row-start":A()}],"row-end":[{"row-end":A()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",Q]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",Q]}],gap:[{gap:[c]}],"gap-x":[{"gap-x":[c]}],"gap-y":[{"gap-y":[c]}],"justify-content":[{justify:["normal",...E()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...E(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...E(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[m]}],px:[{px:[m]}],py:[{py:[m]}],ps:[{ps:[m]}],pe:[{pe:[m]}],pt:[{pt:[m]}],pr:[{pr:[m]}],pb:[{pb:[m]}],pl:[{pl:[m]}],m:[{m:[S]}],mx:[{mx:[S]}],my:[{my:[S]}],ms:[{ms:[S]}],me:[{me:[S]}],mt:[{mt:[S]}],mr:[{mr:[S]}],mb:[{mb:[S]}],ml:[{ml:[S]}],"space-x":[{"space-x":[N]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[N]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",Q,t]}],"min-w":[{"min-w":[Q,t,"min","max","fit"]}],"max-w":[{"max-w":[Q,t,"none","full","min","max","fit","prose",{screen:[un]},un]}],h:[{h:[Q,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[Q,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[Q,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[Q,t,"auto","min","max","fit"]}],"font-size":[{text:["base",un,ln]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",xl]}],"font-family":[{font:[Oo]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",Q]}],"line-clamp":[{"line-clamp":["none",Vr,xl]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Ut,Q]}],"list-image":[{"list-image":["none",Q]}],"list-style-type":[{list:["none","disc","decimal",Q]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...z(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Ut,ln]}],"underline-offset":[{"underline-offset":["auto",Ut,Q]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:I()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Q]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Q]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...G(),AS]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",LS]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},DS]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[x]}],"gradient-via-pos":[{via:[x]}],"gradient-to-pos":[{to:[x]}],"gradient-from":[{from:[v]}],"gradient-via":[{via:[v]}],"gradient-to":[{to:[v]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...z(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:z()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...z()]}],"outline-offset":[{"outline-offset":[Ut,Q]}],"outline-w":[{outline:[Ut,ln]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:B()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[Ut,ln]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",un,_S]}],"shadow-color":[{shadow:[Oo]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...V(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":V()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",un,Q]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[f]}],saturate:[{saturate:[w]}],sepia:[{sepia:[C]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[f]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[w]}],"backdrop-sepia":[{"backdrop-sepia":[C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",Q]}],duration:[{duration:D()}],ease:[{ease:["linear","in","out","in-out",Q]}],delay:[{delay:D()}],animate:[{animate:["none","spin","ping","pulse","bounce",Q]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[b]}],"scale-x":[{"scale-x":[b]}],"scale-y":[{"scale-y":[b]}],rotate:[{rotate:[jo,Q]}],"translate-x":[{"translate-x":[P]}],"translate-y":[{"translate-y":[P]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",Q]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Q]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":I()}],"scroll-mx":[{"scroll-mx":I()}],"scroll-my":[{"scroll-my":I()}],"scroll-ms":[{"scroll-ms":I()}],"scroll-me":[{"scroll-me":I()}],"scroll-mt":[{"scroll-mt":I()}],"scroll-mr":[{"scroll-mr":I()}],"scroll-mb":[{"scroll-mb":I()}],"scroll-ml":[{"scroll-ml":I()}],"scroll-p":[{"scroll-p":I()}],"scroll-px":[{"scroll-px":I()}],"scroll-py":[{"scroll-py":I()}],"scroll-ps":[{"scroll-ps":I()}],"scroll-pe":[{"scroll-pe":I()}],"scroll-pt":[{"scroll-pt":I()}],"scroll-pr":[{"scroll-pr":I()}],"scroll-pb":[{"scroll-pb":I()}],"scroll-pl":[{"scroll-pl":I()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Q]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Ut,ln,xl]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},US=bS(zS);function we(...e){return US(Qg(e))}const BS=Qw,iv=y.forwardRef(({className:e,...t},n)=>p.jsx(zg,{ref:n,className:we("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));iv.displayName=zg.displayName;const VS=Qc("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),sv=y.forwardRef(({className:e,variant:t,...n},r)=>p.jsx(Ug,{ref:r,className:we(VS({variant:t}),e),...n}));sv.displayName=Ug.displayName;const HS=y.forwardRef(({className:e,...t},n)=>p.jsx(Hg,{ref:n,className:we("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));HS.displayName=Hg.displayName;const av=y.forwardRef(({className:e,...t},n)=>p.jsx(Wg,{ref:n,className:we("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:p.jsx(Yc,{className:"h-4 w-4"})}));av.displayName=Wg.displayName;const lv=y.forwardRef(({className:e,...t},n)=>p.jsx(Bg,{ref:n,className:we("text-sm font-semibold",e),...t}));lv.displayName=Bg.displayName;const uv=y.forwardRef(({className:e,...t},n)=>p.jsx(Vg,{ref:n,className:we("text-sm opacity-90",e),...t}));uv.displayName=Vg.displayName;function WS(){const{toasts:e}=xg();return p.jsxs(BS,{children:[e.map(function({id:t,title:n,description:r,action:o,...i}){return p.jsxs(sv,{...i,children:[p.jsxs("div",{className:"grid gap-1",children:[n&&p.jsx(lv,{children:n}),r&&p.jsx(uv,{children:r})]}),o,p.jsx(av,{})]},t)}),p.jsx(iv,{})]})}var KS=hh.useId||(()=>{}),QS=0;function wl(e){const[t,n]=y.useState(KS());return Dn(()=>{e||n(r=>r??String(QS++))},[e]),e||(t?`radix-${t}`:"")}const GS=["top","right","bottom","left"],_n=Math.min,Je=Math.max,Xs=Math.round,rs=Math.floor,In=e=>({x:e,y:e}),YS={left:"right",right:"left",bottom:"top",top:"bottom"},qS={start:"end",end:"start"};function Au(e,t,n){return Je(e,_n(t,n))}function Zt(e,t){return typeof e=="function"?e(t):e}function Jt(e){return e.split("-")[0]}function yo(e){return e.split("-")[1]}function Xc(e){return e==="x"?"y":"x"}function Zc(e){return e==="y"?"height":"width"}function Fn(e){return["top","bottom"].includes(Jt(e))?"y":"x"}function Jc(e){return Xc(Fn(e))}function XS(e,t,n){n===void 0&&(n=!1);const r=yo(e),o=Jc(e),i=Zc(o);let s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=Zs(s)),[s,Zs(s)]}function ZS(e){const t=Zs(e);return[Mu(e),t,Mu(t)]}function Mu(e){return e.replace(/start|end/g,t=>qS[t])}function JS(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:s;default:return[]}}function eb(e,t,n,r){const o=yo(e);let i=JS(Jt(e),n==="start",r);return o&&(i=i.map(s=>s+"-"+o),t&&(i=i.concat(i.map(Mu)))),i}function Zs(e){return e.replace(/left|right|bottom|top/g,t=>YS[t])}function tb(e){return{top:0,right:0,bottom:0,left:0,...e}}function cv(e){return typeof e!="number"?tb(e):{top:e,right:e,bottom:e,left:e}}function Js(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Qf(e,t,n){let{reference:r,floating:o}=e;const i=Fn(t),s=Jc(t),a=Zc(s),l=Jt(t),u=i==="y",d=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,c=r[a]/2-o[a]/2;let v;switch(l){case"top":v={x:d,y:r.y-o.height};break;case"bottom":v={x:d,y:r.y+r.height};break;case"right":v={x:r.x+r.width,y:f};break;case"left":v={x:r.x-o.width,y:f};break;default:v={x:r.x,y:r.y}}switch(yo(t)){case"start":v[s]-=c*(n&&u?-1:1);break;case"end":v[s]+=c*(n&&u?-1:1);break}return v}const nb=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,a=i.filter(Boolean),l=await(s.isRTL==null?void 0:s.isRTL(t));let u=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:f}=Qf(u,r,l),c=r,v={},x=0;for(let h=0;h<a.length;h++){const{name:S,fn:g}=a[h],{x:m,y:w,data:b,reset:C}=await g({x:d,y:f,initialPlacement:r,placement:c,strategy:o,middlewareData:v,rects:u,platform:s,elements:{reference:e,floating:t}});d=m??d,f=w??f,v={...v,[S]:{...v[S],...b}},C&&x<=50&&(x++,typeof C=="object"&&(C.placement&&(c=C.placement),C.rects&&(u=C.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):C.rects),{x:d,y:f}=Qf(u,c,l)),h=-1)}return{x:d,y:f,placement:c,strategy:o,middlewareData:v}};async function fi(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:s,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:f="floating",altBoundary:c=!1,padding:v=0}=Zt(t,e),x=cv(v),S=a[c?f==="floating"?"reference":"floating":f],g=Js(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(S)))==null||n?S:S.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:d,strategy:l})),m=f==="floating"?{x:r,y:o,width:s.floating.width,height:s.floating.height}:s.reference,w=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a.floating)),b=await(i.isElement==null?void 0:i.isElement(w))?await(i.getScale==null?void 0:i.getScale(w))||{x:1,y:1}:{x:1,y:1},C=Js(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:m,offsetParent:w,strategy:l}):m);return{top:(g.top-C.top+x.top)/b.y,bottom:(C.bottom-g.bottom+x.bottom)/b.y,left:(g.left-C.left+x.left)/b.x,right:(C.right-g.right+x.right)/b.x}}const rb=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:s,elements:a,middlewareData:l}=t,{element:u,padding:d=0}=Zt(e,t)||{};if(u==null)return{};const f=cv(d),c={x:n,y:r},v=Jc(o),x=Zc(v),h=await s.getDimensions(u),S=v==="y",g=S?"top":"left",m=S?"bottom":"right",w=S?"clientHeight":"clientWidth",b=i.reference[x]+i.reference[v]-c[v]-i.floating[x],C=c[v]-i.reference[v],k=await(s.getOffsetParent==null?void 0:s.getOffsetParent(u));let N=k?k[w]:0;(!N||!await(s.isElement==null?void 0:s.isElement(k)))&&(N=a.floating[w]||i.floating[x]);const P=b/2-C/2,O=N/2-h[x]/2-1,R=_n(f[g],O),$=_n(f[m],O),I=R,B=N-h[x]-$,A=N/2-h[x]/2+P,G=Au(I,A,B),z=!l.arrow&&yo(o)!=null&&A!==G&&i.reference[x]/2-(A<I?R:$)-h[x]/2<0,V=z?A<I?A-I:A-B:0;return{[v]:c[v]+V,data:{[v]:G,centerOffset:A-G-V,...z&&{alignmentOffset:V}},reset:z}}}),ob=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:s,initialPlacement:a,platform:l,elements:u}=t,{mainAxis:d=!0,crossAxis:f=!0,fallbackPlacements:c,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:x="none",flipAlignment:h=!0,...S}=Zt(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const g=Jt(o),m=Fn(a),w=Jt(a)===a,b=await(l.isRTL==null?void 0:l.isRTL(u.floating)),C=c||(w||!h?[Zs(a)]:ZS(a)),k=x!=="none";!c&&k&&C.push(...eb(a,h,x,b));const N=[a,...C],P=await fi(t,S),O=[];let R=((r=i.flip)==null?void 0:r.overflows)||[];if(d&&O.push(P[g]),f){const A=XS(o,s,b);O.push(P[A[0]],P[A[1]])}if(R=[...R,{placement:o,overflows:O}],!O.every(A=>A<=0)){var $,I;const A=((($=i.flip)==null?void 0:$.index)||0)+1,G=N[A];if(G)return{data:{index:A,overflows:R},reset:{placement:G}};let z=(I=R.filter(V=>V.overflows[0]<=0).sort((V,E)=>V.overflows[1]-E.overflows[1])[0])==null?void 0:I.placement;if(!z)switch(v){case"bestFit":{var B;const V=(B=R.filter(E=>{if(k){const L=Fn(E.placement);return L===m||L==="y"}return!0}).map(E=>[E.placement,E.overflows.filter(L=>L>0).reduce((L,F)=>L+F,0)]).sort((E,L)=>E[1]-L[1])[0])==null?void 0:B[0];V&&(z=V);break}case"initialPlacement":z=a;break}if(o!==z)return{reset:{placement:z}}}return{}}}};function Gf(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Yf(e){return GS.some(t=>e[t]>=0)}const ib=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Zt(e,t);switch(r){case"referenceHidden":{const i=await fi(t,{...o,elementContext:"reference"}),s=Gf(i,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:Yf(s)}}}case"escaped":{const i=await fi(t,{...o,altBoundary:!0}),s=Gf(i,n.floating);return{data:{escapedOffsets:s,escaped:Yf(s)}}}default:return{}}}}};async function sb(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),s=Jt(n),a=yo(n),l=Fn(n)==="y",u=["left","top"].includes(s)?-1:1,d=i&&l?-1:1,f=Zt(t,e);let{mainAxis:c,crossAxis:v,alignmentAxis:x}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&typeof x=="number"&&(v=a==="end"?x*-1:x),l?{x:v*d,y:c*u}:{x:c*u,y:v*d}}const ab=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:s,middlewareData:a}=t,l=await sb(t,e);return s===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+l.x,y:i+l.y,data:{...l,placement:s}}}}},lb=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:a={fn:S=>{let{x:g,y:m}=S;return{x:g,y:m}}},...l}=Zt(e,t),u={x:n,y:r},d=await fi(t,l),f=Fn(Jt(o)),c=Xc(f);let v=u[c],x=u[f];if(i){const S=c==="y"?"top":"left",g=c==="y"?"bottom":"right",m=v+d[S],w=v-d[g];v=Au(m,v,w)}if(s){const S=f==="y"?"top":"left",g=f==="y"?"bottom":"right",m=x+d[S],w=x-d[g];x=Au(m,x,w)}const h=a.fn({...t,[c]:v,[f]:x});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[c]:i,[f]:s}}}}}},ub=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:s}=t,{offset:a=0,mainAxis:l=!0,crossAxis:u=!0}=Zt(e,t),d={x:n,y:r},f=Fn(o),c=Xc(f);let v=d[c],x=d[f];const h=Zt(a,t),S=typeof h=="number"?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(l){const w=c==="y"?"height":"width",b=i.reference[c]-i.floating[w]+S.mainAxis,C=i.reference[c]+i.reference[w]-S.mainAxis;v<b?v=b:v>C&&(v=C)}if(u){var g,m;const w=c==="y"?"width":"height",b=["top","left"].includes(Jt(o)),C=i.reference[f]-i.floating[w]+(b&&((g=s.offset)==null?void 0:g[f])||0)+(b?0:S.crossAxis),k=i.reference[f]+i.reference[w]+(b?0:((m=s.offset)==null?void 0:m[f])||0)-(b?S.crossAxis:0);x<C?x=C:x>k&&(x=k)}return{[c]:v,[f]:x}}}},cb=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:s,elements:a}=t,{apply:l=()=>{},...u}=Zt(e,t),d=await fi(t,u),f=Jt(o),c=yo(o),v=Fn(o)==="y",{width:x,height:h}=i.floating;let S,g;f==="top"||f==="bottom"?(S=f,g=c===(await(s.isRTL==null?void 0:s.isRTL(a.floating))?"start":"end")?"left":"right"):(g=f,S=c==="end"?"top":"bottom");const m=h-d.top-d.bottom,w=x-d.left-d.right,b=_n(h-d[S],m),C=_n(x-d[g],w),k=!t.middlewareData.shift;let N=b,P=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(P=w),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(N=m),k&&!c){const R=Je(d.left,0),$=Je(d.right,0),I=Je(d.top,0),B=Je(d.bottom,0);v?P=x-2*(R!==0||$!==0?R+$:Je(d.left,d.right)):N=h-2*(I!==0||B!==0?I+B:Je(d.top,d.bottom))}await l({...t,availableWidth:P,availableHeight:N});const O=await s.getDimensions(a.floating);return x!==O.width||h!==O.height?{reset:{rects:!0}}:{}}}};function Ta(){return typeof window<"u"}function xo(e){return dv(e)?(e.nodeName||"").toLowerCase():"#document"}function nt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function zt(e){var t;return(t=(dv(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function dv(e){return Ta()?e instanceof Node||e instanceof nt(e).Node:!1}function Et(e){return Ta()?e instanceof Element||e instanceof nt(e).Element:!1}function $t(e){return Ta()?e instanceof HTMLElement||e instanceof nt(e).HTMLElement:!1}function qf(e){return!Ta()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof nt(e).ShadowRoot}function Ti(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=kt(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function db(e){return["table","td","th"].includes(xo(e))}function Ra(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function ed(e){const t=td(),n=Et(e)?kt(e):e;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function fb(e){let t=$n(e);for(;$t(t)&&!co(t);){if(ed(t))return t;if(Ra(t))return null;t=$n(t)}return null}function td(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function co(e){return["html","body","#document"].includes(xo(e))}function kt(e){return nt(e).getComputedStyle(e)}function ja(e){return Et(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function $n(e){if(xo(e)==="html")return e;const t=e.assignedSlot||e.parentNode||qf(e)&&e.host||zt(e);return qf(t)?t.host:t}function fv(e){const t=$n(e);return co(t)?e.ownerDocument?e.ownerDocument.body:e.body:$t(t)&&Ti(t)?t:fv(t)}function pi(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=fv(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),s=nt(o);if(i){const a=Du(s);return t.concat(s,s.visualViewport||[],Ti(o)?o:[],a&&n?pi(a):[])}return t.concat(o,pi(o,[],n))}function Du(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function pv(e){const t=kt(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=$t(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,a=Xs(n)!==i||Xs(r)!==s;return a&&(n=i,r=s),{width:n,height:r,$:a}}function nd(e){return Et(e)?e:e.contextElement}function Hr(e){const t=nd(e);if(!$t(t))return In(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=pv(t);let s=(i?Xs(n.width):n.width)/r,a=(i?Xs(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}const pb=In(0);function hv(e){const t=nt(e);return!td()||!t.visualViewport?pb:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function hb(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==nt(e)?!1:t}function lr(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=nd(e);let s=In(1);t&&(r?Et(r)&&(s=Hr(r)):s=Hr(e));const a=hb(i,n,r)?hv(i):In(0);let l=(o.left+a.x)/s.x,u=(o.top+a.y)/s.y,d=o.width/s.x,f=o.height/s.y;if(i){const c=nt(i),v=r&&Et(r)?nt(r):r;let x=c,h=Du(x);for(;h&&r&&v!==x;){const S=Hr(h),g=h.getBoundingClientRect(),m=kt(h),w=g.left+(h.clientLeft+parseFloat(m.paddingLeft))*S.x,b=g.top+(h.clientTop+parseFloat(m.paddingTop))*S.y;l*=S.x,u*=S.y,d*=S.x,f*=S.y,l+=w,u+=b,x=nt(h),h=Du(x)}}return Js({width:d,height:f,x:l,y:u})}function mb(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",s=zt(r),a=t?Ra(t.floating):!1;if(r===s||a&&i)return n;let l={scrollLeft:0,scrollTop:0},u=In(1);const d=In(0),f=$t(r);if((f||!f&&!i)&&((xo(r)!=="body"||Ti(s))&&(l=ja(r)),$t(r))){const c=lr(r);u=Hr(r),d.x=c.x+r.clientLeft,d.y=c.y+r.clientTop}return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+d.x,y:n.y*u.y-l.scrollTop*u.y+d.y}}function gb(e){return Array.from(e.getClientRects())}function _u(e,t){const n=ja(e).scrollLeft;return t?t.left+n:lr(zt(e)).left+n}function vb(e){const t=zt(e),n=ja(e),r=e.ownerDocument.body,o=Je(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Je(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+_u(e);const a=-n.scrollTop;return kt(r).direction==="rtl"&&(s+=Je(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:a}}function yb(e,t){const n=nt(e),r=zt(e),o=n.visualViewport;let i=r.clientWidth,s=r.clientHeight,a=0,l=0;if(o){i=o.width,s=o.height;const u=td();(!u||u&&t==="fixed")&&(a=o.offsetLeft,l=o.offsetTop)}return{width:i,height:s,x:a,y:l}}function xb(e,t){const n=lr(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=$t(e)?Hr(e):In(1),s=e.clientWidth*i.x,a=e.clientHeight*i.y,l=o*i.x,u=r*i.y;return{width:s,height:a,x:l,y:u}}function Xf(e,t,n){let r;if(t==="viewport")r=yb(e,n);else if(t==="document")r=vb(zt(e));else if(Et(t))r=xb(t,n);else{const o=hv(e);r={...t,x:t.x-o.x,y:t.y-o.y}}return Js(r)}function mv(e,t){const n=$n(e);return n===t||!Et(n)||co(n)?!1:kt(n).position==="fixed"||mv(n,t)}function wb(e,t){const n=t.get(e);if(n)return n;let r=pi(e,[],!1).filter(a=>Et(a)&&xo(a)!=="body"),o=null;const i=kt(e).position==="fixed";let s=i?$n(e):e;for(;Et(s)&&!co(s);){const a=kt(s),l=ed(s);!l&&a.position==="fixed"&&(o=null),(i?!l&&!o:!l&&a.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Ti(s)&&!l&&mv(e,s))?r=r.filter(d=>d!==s):o=a,s=$n(s)}return t.set(e,r),r}function Sb(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const s=[...n==="clippingAncestors"?Ra(t)?[]:wb(t,this._c):[].concat(n),r],a=s[0],l=s.reduce((u,d)=>{const f=Xf(t,d,o);return u.top=Je(f.top,u.top),u.right=_n(f.right,u.right),u.bottom=_n(f.bottom,u.bottom),u.left=Je(f.left,u.left),u},Xf(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function bb(e){const{width:t,height:n}=pv(e);return{width:t,height:n}}function Cb(e,t,n){const r=$t(t),o=zt(t),i=n==="fixed",s=lr(e,!0,i,t);let a={scrollLeft:0,scrollTop:0};const l=In(0);if(r||!r&&!i)if((xo(t)!=="body"||Ti(o))&&(a=ja(t)),r){const v=lr(t,!0,i,t);l.x=v.x+t.clientLeft,l.y=v.y+t.clientTop}else o&&(l.x=_u(o));let u=0,d=0;if(o&&!r&&!i){const v=o.getBoundingClientRect();d=v.top+a.scrollTop,u=v.left+a.scrollLeft-_u(o,v)}const f=s.left+a.scrollLeft-l.x-u,c=s.top+a.scrollTop-l.y-d;return{x:f,y:c,width:s.width,height:s.height}}function Sl(e){return kt(e).position==="static"}function Zf(e,t){if(!$t(e)||kt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return zt(e)===n&&(n=n.ownerDocument.body),n}function gv(e,t){const n=nt(e);if(Ra(e))return n;if(!$t(e)){let o=$n(e);for(;o&&!co(o);){if(Et(o)&&!Sl(o))return o;o=$n(o)}return n}let r=Zf(e,t);for(;r&&db(r)&&Sl(r);)r=Zf(r,t);return r&&co(r)&&Sl(r)&&!ed(r)?n:r||fb(e)||n}const Eb=async function(e){const t=this.getOffsetParent||gv,n=this.getDimensions,r=await n(e.floating);return{reference:Cb(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function kb(e){return kt(e).direction==="rtl"}const Nb={convertOffsetParentRelativeRectToViewportRelativeRect:mb,getDocumentElement:zt,getClippingRect:Sb,getOffsetParent:gv,getElementRects:Eb,getClientRects:gb,getDimensions:bb,getScale:Hr,isElement:Et,isRTL:kb};function Pb(e,t){let n=null,r;const o=zt(e);function i(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function s(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),i();const{left:u,top:d,width:f,height:c}=e.getBoundingClientRect();if(a||t(),!f||!c)return;const v=rs(d),x=rs(o.clientWidth-(u+f)),h=rs(o.clientHeight-(d+c)),S=rs(u),m={rootMargin:-v+"px "+-x+"px "+-h+"px "+-S+"px",threshold:Je(0,_n(1,l))||1};let w=!0;function b(C){const k=C[0].intersectionRatio;if(k!==l){if(!w)return s();k?s(!1,k):r=setTimeout(()=>{s(!1,1e-7)},1e3)}w=!1}try{n=new IntersectionObserver(b,{...m,root:o.ownerDocument})}catch{n=new IntersectionObserver(b,m)}n.observe(e)}return s(!0),i}function Tb(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,u=nd(e),d=o||i?[...u?pi(u):[],...pi(t)]:[];d.forEach(g=>{o&&g.addEventListener("scroll",n,{passive:!0}),i&&g.addEventListener("resize",n)});const f=u&&a?Pb(u,n):null;let c=-1,v=null;s&&(v=new ResizeObserver(g=>{let[m]=g;m&&m.target===u&&v&&(v.unobserve(t),cancelAnimationFrame(c),c=requestAnimationFrame(()=>{var w;(w=v)==null||w.observe(t)})),n()}),u&&!l&&v.observe(u),v.observe(t));let x,h=l?lr(e):null;l&&S();function S(){const g=lr(e);h&&(g.x!==h.x||g.y!==h.y||g.width!==h.width||g.height!==h.height)&&n(),h=g,x=requestAnimationFrame(S)}return n(),()=>{var g;d.forEach(m=>{o&&m.removeEventListener("scroll",n),i&&m.removeEventListener("resize",n)}),f==null||f(),(g=v)==null||g.disconnect(),v=null,l&&cancelAnimationFrame(x)}}const Rb=ab,jb=lb,Ob=ob,Lb=cb,Ab=ib,Jf=rb,Mb=ub,Db=(e,t,n)=>{const r=new Map,o={platform:Nb,...n},i={...o.platform,_c:r};return nb(e,t,{...o,platform:i})};var Es=typeof document<"u"?y.useLayoutEffect:y.useEffect;function ea(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!ea(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!ea(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function vv(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){const n=vv(e);return Math.round(t*n)/n}function bl(e){const t=y.useRef(e);return Es(()=>{t.current=e}),t}function _b(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:s}={},transform:a=!0,whileElementsMounted:l,open:u}=e,[d,f]=y.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[c,v]=y.useState(r);ea(c,r)||v(r);const[x,h]=y.useState(null),[S,g]=y.useState(null),m=y.useCallback(E=>{E!==k.current&&(k.current=E,h(E))},[]),w=y.useCallback(E=>{E!==N.current&&(N.current=E,g(E))},[]),b=i||x,C=s||S,k=y.useRef(null),N=y.useRef(null),P=y.useRef(d),O=l!=null,R=bl(l),$=bl(o),I=bl(u),B=y.useCallback(()=>{if(!k.current||!N.current)return;const E={placement:t,strategy:n,middleware:c};$.current&&(E.platform=$.current),Db(k.current,N.current,E).then(L=>{const F={...L,isPositioned:I.current!==!1};A.current&&!ea(P.current,F)&&(P.current=F,Pi.flushSync(()=>{f(F)}))})},[c,t,n,$,I]);Es(()=>{u===!1&&P.current.isPositioned&&(P.current.isPositioned=!1,f(E=>({...E,isPositioned:!1})))},[u]);const A=y.useRef(!1);Es(()=>(A.current=!0,()=>{A.current=!1}),[]),Es(()=>{if(b&&(k.current=b),C&&(N.current=C),b&&C){if(R.current)return R.current(b,C,B);B()}},[b,C,B,R,O]);const G=y.useMemo(()=>({reference:k,floating:N,setReference:m,setFloating:w}),[m,w]),z=y.useMemo(()=>({reference:b,floating:C}),[b,C]),V=y.useMemo(()=>{const E={position:n,left:0,top:0};if(!z.floating)return E;const L=ep(z.floating,d.x),F=ep(z.floating,d.y);return a?{...E,transform:"translate("+L+"px, "+F+"px)",...vv(z.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:L,top:F}},[n,a,z.floating,d.x,d.y]);return y.useMemo(()=>({...d,update:B,refs:G,elements:z,floatingStyles:V}),[d,B,G,z,V])}const Ib=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Jf({element:r.current,padding:o}).fn(n):{}:r?Jf({element:r,padding:o}).fn(n):{}}}},Fb=(e,t)=>({...Rb(e),options:[e,t]}),$b=(e,t)=>({...jb(e),options:[e,t]}),zb=(e,t)=>({...Mb(e),options:[e,t]}),Ub=(e,t)=>({...Ob(e),options:[e,t]}),Bb=(e,t)=>({...Lb(e),options:[e,t]}),Vb=(e,t)=>({...Ab(e),options:[e,t]}),Hb=(e,t)=>({...Ib(e),options:[e,t]});var Wb="Arrow",yv=y.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return p.jsx(xe.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:p.jsx("polygon",{points:"0,0 30,0 15,10"})})});yv.displayName=Wb;var Kb=yv;function Qb(e,t=[]){let n=[];function r(i,s){const a=y.createContext(s),l=n.length;n=[...n,s];function u(f){const{scope:c,children:v,...x}=f,h=(c==null?void 0:c[e][l])||a,S=y.useMemo(()=>x,Object.values(x));return p.jsx(h.Provider,{value:S,children:v})}function d(f,c){const v=(c==null?void 0:c[e][l])||a,x=y.useContext(v);if(x)return x;if(s!==void 0)return s;throw new Error(`\`${f}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,d]}const o=()=>{const i=n.map(s=>y.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return y.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,Gb(o,...t)]}function Gb(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const f=l(i)[`__scope${u}`];return{...a,...f}},{});return y.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function Yb(e){const[t,n]=y.useState(void 0);return Dn(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let s,a;if("borderBoxSize"in i){const l=i.borderBoxSize,u=Array.isArray(l)?l[0]:l;s=u.inlineSize,a=u.blockSize}else s=e.offsetWidth,a=e.offsetHeight;n({width:s,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var xv="Popper",[wv,Sv]=Qb(xv),[DN,bv]=wv(xv),Cv="PopperAnchor",Ev=y.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=bv(Cv,n),s=y.useRef(null),a=Be(t,s);return y.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||s.current)}),r?null:p.jsx(xe.div,{...o,ref:a})});Ev.displayName=Cv;var rd="PopperContent",[qb,Xb]=wv(rd),kv=y.forwardRef((e,t)=>{var ht,tn,nn,rn,Li,hr;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:s=0,arrowPadding:a=0,avoidCollisions:l=!0,collisionBoundary:u=[],collisionPadding:d=0,sticky:f="partial",hideWhenDetached:c=!1,updatePositionStrategy:v="optimized",onPlaced:x,...h}=e,S=bv(rd,n),[g,m]=y.useState(null),w=Be(t,Hn=>m(Hn)),[b,C]=y.useState(null),k=Yb(b),N=(k==null?void 0:k.width)??0,P=(k==null?void 0:k.height)??0,O=r+(i!=="center"?"-"+i:""),R=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},$=Array.isArray(u)?u:[u],I=$.length>0,B={padding:R,boundary:$.filter(Jb),altBoundary:I},{refs:A,floatingStyles:G,placement:z,isPositioned:V,middlewareData:E}=_b({strategy:"fixed",placement:O,whileElementsMounted:(...Hn)=>Tb(...Hn,{animationFrame:v==="always"}),elements:{reference:S.anchor},middleware:[Fb({mainAxis:o+P,alignmentAxis:s}),l&&$b({mainAxis:!0,crossAxis:!1,limiter:f==="partial"?zb():void 0,...B}),l&&Ub({...B}),Bb({...B,apply:({elements:Hn,rects:So,availableWidth:Ai,availableHeight:on})=>{const{width:Fa,height:$a}=So.reference,Ne=Hn.floating.style;Ne.setProperty("--radix-popper-available-width",`${Ai}px`),Ne.setProperty("--radix-popper-available-height",`${on}px`),Ne.setProperty("--radix-popper-anchor-width",`${Fa}px`),Ne.setProperty("--radix-popper-anchor-height",`${$a}px`)}}),b&&Hb({element:b,padding:a}),e2({arrowWidth:N,arrowHeight:P}),c&&Vb({strategy:"referenceHidden",...B})]}),[L,F]=Tv(z),D=pt(x);Dn(()=>{V&&(D==null||D())},[V,D]);const U=(ht=E.arrow)==null?void 0:ht.x,W=(tn=E.arrow)==null?void 0:tn.y,ne=((nn=E.arrow)==null?void 0:nn.centerOffset)!==0,[je,J]=y.useState();return Dn(()=>{g&&J(window.getComputedStyle(g).zIndex)},[g]),p.jsx("div",{ref:A.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:V?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:je,"--radix-popper-transform-origin":[(rn=E.transformOrigin)==null?void 0:rn.x,(Li=E.transformOrigin)==null?void 0:Li.y].join(" "),...((hr=E.hide)==null?void 0:hr.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:p.jsx(qb,{scope:n,placedSide:L,onArrowChange:C,arrowX:U,arrowY:W,shouldHideArrow:ne,children:p.jsx(xe.div,{"data-side":L,"data-align":F,...h,ref:w,style:{...h.style,animation:V?void 0:"none"}})})})});kv.displayName=rd;var Nv="PopperArrow",Zb={top:"bottom",right:"left",bottom:"top",left:"right"},Pv=y.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,i=Xb(Nv,r),s=Zb[i.placedSide];return p.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:p.jsx(Kb,{...o,ref:n,style:{...o.style,display:"block"}})})});Pv.displayName=Nv;function Jb(e){return e!==null}var e2=e=>({name:"transformOrigin",options:e,fn(t){var S,g,m;const{placement:n,rects:r,middlewareData:o}=t,s=((S=o.arrow)==null?void 0:S.centerOffset)!==0,a=s?0:e.arrowWidth,l=s?0:e.arrowHeight,[u,d]=Tv(n),f={start:"0%",center:"50%",end:"100%"}[d],c=(((g=o.arrow)==null?void 0:g.x)??0)+a/2,v=(((m=o.arrow)==null?void 0:m.y)??0)+l/2;let x="",h="";return u==="bottom"?(x=s?f:`${c}px`,h=`${-l}px`):u==="top"?(x=s?f:`${c}px`,h=`${r.floating.height+l}px`):u==="right"?(x=`${-l}px`,h=s?f:`${v}px`):u==="left"&&(x=`${r.floating.width+l}px`,h=s?f:`${v}px`),{data:{x,y:h}}}});function Tv(e){const[t,n="center"]=e.split("-");return[t,n]}var t2=Ev,n2=kv,r2=Pv,[Oa,_N]=Bc("Tooltip",[Sv]),od=Sv(),Rv="TooltipProvider",o2=700,tp="tooltip.open",[i2,jv]=Oa(Rv),Ov=e=>{const{__scopeTooltip:t,delayDuration:n=o2,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:i}=e,[s,a]=y.useState(!0),l=y.useRef(!1),u=y.useRef(0);return y.useEffect(()=>{const d=u.current;return()=>window.clearTimeout(d)},[]),p.jsx(i2,{scope:t,isOpenDelayed:s,delayDuration:n,onOpen:y.useCallback(()=>{window.clearTimeout(u.current),a(!1)},[]),onClose:y.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a(!0),r)},[r]),isPointerInTransitRef:l,onPointerInTransitChange:y.useCallback(d=>{l.current=d},[]),disableHoverableContent:o,children:i})};Ov.displayName=Rv;var Lv="Tooltip",[IN,La]=Oa(Lv),Iu="TooltipTrigger",s2=y.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=La(Iu,n),i=jv(Iu,n),s=od(n),a=y.useRef(null),l=Be(t,a,o.onTriggerChange),u=y.useRef(!1),d=y.useRef(!1),f=y.useCallback(()=>u.current=!1,[]);return y.useEffect(()=>()=>document.removeEventListener("pointerup",f),[f]),p.jsx(t2,{asChild:!0,...s,children:p.jsx(xe.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:l,onPointerMove:ce(e.onPointerMove,c=>{c.pointerType!=="touch"&&!d.current&&!i.isPointerInTransitRef.current&&(o.onTriggerEnter(),d.current=!0)}),onPointerLeave:ce(e.onPointerLeave,()=>{o.onTriggerLeave(),d.current=!1}),onPointerDown:ce(e.onPointerDown,()=>{u.current=!0,document.addEventListener("pointerup",f,{once:!0})}),onFocus:ce(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:ce(e.onBlur,o.onClose),onClick:ce(e.onClick,o.onClose)})})});s2.displayName=Iu;var a2="TooltipPortal",[FN,l2]=Oa(a2,{forceMount:void 0}),fo="TooltipContent",Av=y.forwardRef((e,t)=>{const n=l2(fo,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,s=La(fo,e.__scopeTooltip);return p.jsx(go,{present:r||s.open,children:s.disableHoverableContent?p.jsx(Mv,{side:o,...i,ref:t}):p.jsx(u2,{side:o,...i,ref:t})})}),u2=y.forwardRef((e,t)=>{const n=La(fo,e.__scopeTooltip),r=jv(fo,e.__scopeTooltip),o=y.useRef(null),i=Be(t,o),[s,a]=y.useState(null),{trigger:l,onClose:u}=n,d=o.current,{onPointerInTransitChange:f}=r,c=y.useCallback(()=>{a(null),f(!1)},[f]),v=y.useCallback((x,h)=>{const S=x.currentTarget,g={x:x.clientX,y:x.clientY},m=p2(g,S.getBoundingClientRect()),w=h2(g,m),b=m2(h.getBoundingClientRect()),C=v2([...w,...b]);a(C),f(!0)},[f]);return y.useEffect(()=>()=>c(),[c]),y.useEffect(()=>{if(l&&d){const x=S=>v(S,d),h=S=>v(S,l);return l.addEventListener("pointerleave",x),d.addEventListener("pointerleave",h),()=>{l.removeEventListener("pointerleave",x),d.removeEventListener("pointerleave",h)}}},[l,d,v,c]),y.useEffect(()=>{if(s){const x=h=>{const S=h.target,g={x:h.clientX,y:h.clientY},m=(l==null?void 0:l.contains(S))||(d==null?void 0:d.contains(S)),w=!g2(g,s);m?c():w&&(c(),u())};return document.addEventListener("pointermove",x),()=>document.removeEventListener("pointermove",x)}},[l,d,s,u,c]),p.jsx(Mv,{...e,ref:i})}),[c2,d2]=Oa(Lv,{isInside:!1}),Mv=y.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:i,onPointerDownOutside:s,...a}=e,l=La(fo,n),u=od(n),{onClose:d}=l;return y.useEffect(()=>(document.addEventListener(tp,d),()=>document.removeEventListener(tp,d)),[d]),y.useEffect(()=>{if(l.trigger){const f=c=>{const v=c.target;v!=null&&v.contains(l.trigger)&&d()};return window.addEventListener("scroll",f,{capture:!0}),()=>window.removeEventListener("scroll",f,{capture:!0})}},[l.trigger,d]),p.jsx(Ea,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:f=>f.preventDefault(),onDismiss:d,children:p.jsxs(n2,{"data-state":l.stateAttribute,...u,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[p.jsx(Sg,{children:r}),p.jsx(c2,{scope:n,isInside:!0,children:p.jsx(Rw,{id:l.contentId,role:"tooltip",children:o||r})})]})})});Av.displayName=fo;var Dv="TooltipArrow",f2=y.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=od(n);return d2(Dv,n).isInside?null:p.jsx(r2,{...o,...r,ref:t})});f2.displayName=Dv;function p2(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function h2(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function m2(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function g2(e,t){const{x:n,y:r}=e;let o=!1;for(let i=0,s=t.length-1;i<t.length;s=i++){const a=t[i].x,l=t[i].y,u=t[s].x,d=t[s].y;l>r!=d>r&&n<(u-a)*(r-l)/(d-l)+a&&(o=!o)}return o}function v2(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),y2(t)}function y2(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const i=t[t.length-1],s=t[t.length-2];if((i.x-s.x)*(o.y-s.y)>=(i.y-s.y)*(o.x-s.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const i=n[n.length-1],s=n[n.length-2];if((i.x-s.x)*(o.y-s.y)>=(i.y-s.y)*(o.x-s.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var x2=Ov,_v=Av;const w2=x2,S2=y.forwardRef(({className:e,sideOffset:t=4,...n},r)=>p.jsx(_v,{ref:r,sideOffset:t,className:we("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));S2.displayName=_v.displayName;var Aa=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Ma=typeof window>"u"||"Deno"in globalThis;function vt(){}function b2(e,t){return typeof e=="function"?e(t):e}function C2(e){return typeof e=="number"&&e>=0&&e!==1/0}function E2(e,t){return Math.max(e+(t||0)-Date.now(),0)}function np(e,t){return typeof e=="function"?e(t):e}function k2(e,t){return typeof e=="function"?e(t):e}function rp(e,t){const{type:n="all",exact:r,fetchStatus:o,predicate:i,queryKey:s,stale:a}=e;if(s){if(r){if(t.queryHash!==id(s,t.options))return!1}else if(!mi(t.queryKey,s))return!1}if(n!=="all"){const l=t.isActive();if(n==="active"&&!l||n==="inactive"&&l)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||o&&o!==t.state.fetchStatus||i&&!i(t))}function op(e,t){const{exact:n,status:r,predicate:o,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(n){if(hi(t.options.mutationKey)!==hi(i))return!1}else if(!mi(t.options.mutationKey,i))return!1}return!(r&&t.state.status!==r||o&&!o(t))}function id(e,t){return((t==null?void 0:t.queryKeyHashFn)||hi)(e)}function hi(e){return JSON.stringify(e,(t,n)=>Fu(n)?Object.keys(n).sort().reduce((r,o)=>(r[o]=n[o],r),{}):n)}function mi(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(n=>!mi(e[n],t[n])):!1}function Iv(e,t){if(e===t)return e;const n=ip(e)&&ip(t);if(n||Fu(e)&&Fu(t)){const r=n?e:Object.keys(e),o=r.length,i=n?t:Object.keys(t),s=i.length,a=n?[]:{};let l=0;for(let u=0;u<s;u++){const d=n?u:i[u];(!n&&r.includes(d)||n)&&e[d]===void 0&&t[d]===void 0?(a[d]=void 0,l++):(a[d]=Iv(e[d],t[d]),a[d]===e[d]&&e[d]!==void 0&&l++)}return o===s&&l===o?e:a}return t}function ip(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Fu(e){if(!sp(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!sp(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function sp(e){return Object.prototype.toString.call(e)==="[object Object]"}function N2(e){return new Promise(t=>{setTimeout(t,e)})}function P2(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?Iv(e,t):t}function T2(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function R2(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var sd=Symbol();function Fv(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===sd?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var qn,gn,Kr,Yp,j2=(Yp=class extends Aa{constructor(){super();Z(this,qn);Z(this,gn);Z(this,Kr);Y(this,Kr,t=>{if(!Ma&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){T(this,gn)||this.setEventListener(T(this,Kr))}onUnsubscribe(){var t;this.hasListeners()||((t=T(this,gn))==null||t.call(this),Y(this,gn,void 0))}setEventListener(t){var n;Y(this,Kr,t),(n=T(this,gn))==null||n.call(this),Y(this,gn,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){T(this,qn)!==t&&(Y(this,qn,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof T(this,qn)=="boolean"?T(this,qn):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},qn=new WeakMap,gn=new WeakMap,Kr=new WeakMap,Yp),$v=new j2,Qr,vn,Gr,qp,O2=(qp=class extends Aa{constructor(){super();Z(this,Qr,!0);Z(this,vn);Z(this,Gr);Y(this,Gr,t=>{if(!Ma&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){T(this,vn)||this.setEventListener(T(this,Gr))}onUnsubscribe(){var t;this.hasListeners()||((t=T(this,vn))==null||t.call(this),Y(this,vn,void 0))}setEventListener(t){var n;Y(this,Gr,t),(n=T(this,vn))==null||n.call(this),Y(this,vn,t(this.setOnline.bind(this)))}setOnline(t){T(this,Qr)!==t&&(Y(this,Qr,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return T(this,Qr)}},Qr=new WeakMap,vn=new WeakMap,Gr=new WeakMap,qp),ta=new O2;function L2(){let e,t;const n=new Promise((o,i)=>{e=o,t=i});n.status="pending",n.catch(()=>{});function r(o){Object.assign(n,o),delete n.resolve,delete n.reject}return n.resolve=o=>{r({status:"fulfilled",value:o}),e(o)},n.reject=o=>{r({status:"rejected",reason:o}),t(o)},n}function A2(e){return Math.min(1e3*2**e,3e4)}function zv(e){return(e??"online")==="online"?ta.isOnline():!0}var Uv=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Cl(e){return e instanceof Uv}function Bv(e){let t=!1,n=0,r=!1,o;const i=L2(),s=h=>{var S;r||(c(new Uv(h)),(S=e.abort)==null||S.call(e))},a=()=>{t=!0},l=()=>{t=!1},u=()=>$v.isFocused()&&(e.networkMode==="always"||ta.isOnline())&&e.canRun(),d=()=>zv(e.networkMode)&&e.canRun(),f=h=>{var S;r||(r=!0,(S=e.onSuccess)==null||S.call(e,h),o==null||o(),i.resolve(h))},c=h=>{var S;r||(r=!0,(S=e.onError)==null||S.call(e,h),o==null||o(),i.reject(h))},v=()=>new Promise(h=>{var S;o=g=>{(r||u())&&h(g)},(S=e.onPause)==null||S.call(e)}).then(()=>{var h;o=void 0,r||(h=e.onContinue)==null||h.call(e)}),x=()=>{if(r)return;let h;const S=n===0?e.initialPromise:void 0;try{h=S??e.fn()}catch(g){h=Promise.reject(g)}Promise.resolve(h).then(f).catch(g=>{var k;if(r)return;const m=e.retry??(Ma?0:3),w=e.retryDelay??A2,b=typeof w=="function"?w(n,g):w,C=m===!0||typeof m=="number"&&n<m||typeof m=="function"&&m(n,g);if(t||!C){c(g);return}n++,(k=e.onFail)==null||k.call(e,n,g),N2(b).then(()=>u()?void 0:v()).then(()=>{t?c(g):x()})})};return{promise:i,cancel:s,continue:()=>(o==null||o(),i),cancelRetry:a,continueRetry:l,canStart:d,start:()=>(d()?x():v().then(x),i)}}function M2(){let e=[],t=0,n=a=>{a()},r=a=>{a()},o=a=>setTimeout(a,0);const i=a=>{t?e.push(a):o(()=>{n(a)})},s=()=>{const a=e;e=[],a.length&&o(()=>{r(()=>{a.forEach(l=>{n(l)})})})};return{batch:a=>{let l;t++;try{l=a()}finally{t--,t||s()}return l},batchCalls:a=>(...l)=>{i(()=>{a(...l)})},schedule:i,setNotifyFunction:a=>{n=a},setBatchNotifyFunction:a=>{r=a},setScheduler:a=>{o=a}}}var ze=M2(),Xn,Xp,Vv=(Xp=class{constructor(){Z(this,Xn)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),C2(this.gcTime)&&Y(this,Xn,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(Ma?1/0:5*60*1e3))}clearGcTimeout(){T(this,Xn)&&(clearTimeout(T(this,Xn)),Y(this,Xn,void 0))}},Xn=new WeakMap,Xp),Yr,qr,st,Me,wi,Zn,yt,Bt,Zp,D2=(Zp=class extends Vv{constructor(t){super();Z(this,yt);Z(this,Yr);Z(this,qr);Z(this,st);Z(this,Me);Z(this,wi);Z(this,Zn);Y(this,Zn,!1),Y(this,wi,t.defaultOptions),this.setOptions(t.options),this.observers=[],Y(this,st,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,Y(this,Yr,I2(this.options)),this.state=t.state??T(this,Yr),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=T(this,Me))==null?void 0:t.promise}setOptions(t){this.options={...T(this,wi),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&T(this,st).remove(this)}setData(t,n){const r=P2(this.state.data,t,this.options);return Oe(this,yt,Bt).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){Oe(this,yt,Bt).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,o;const n=(r=T(this,Me))==null?void 0:r.promise;return(o=T(this,Me))==null||o.cancel(t),n?n.then(vt).catch(vt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(T(this,Yr))}isActive(){return this.observers.some(t=>k2(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===sd||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!E2(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=T(this,Me))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=T(this,Me))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),T(this,st).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(T(this,Me)&&(T(this,Zn)?T(this,Me).cancel({revert:!0}):T(this,Me).cancelRetry()),this.scheduleGc()),T(this,st).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||Oe(this,yt,Bt).call(this,{type:"invalidate"})}fetch(t,n){var l,u,d;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(T(this,Me))return T(this,Me).continueRetry(),T(this,Me).promise}if(t&&this.setOptions(t),!this.options.queryFn){const f=this.observers.find(c=>c.options.queryFn);f&&this.setOptions(f.options)}const r=new AbortController,o=f=>{Object.defineProperty(f,"signal",{enumerable:!0,get:()=>(Y(this,Zn,!0),r.signal)})},i=()=>{const f=Fv(this.options,n),c={queryKey:this.queryKey,meta:this.meta};return o(c),Y(this,Zn,!1),this.options.persister?this.options.persister(f,c,this):f(c)},s={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:i};o(s),(l=this.options.behavior)==null||l.onFetch(s,this),Y(this,qr,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((u=s.fetchOptions)==null?void 0:u.meta))&&Oe(this,yt,Bt).call(this,{type:"fetch",meta:(d=s.fetchOptions)==null?void 0:d.meta});const a=f=>{var c,v,x,h;Cl(f)&&f.silent||Oe(this,yt,Bt).call(this,{type:"error",error:f}),Cl(f)||((v=(c=T(this,st).config).onError)==null||v.call(c,f,this),(h=(x=T(this,st).config).onSettled)==null||h.call(x,this.state.data,f,this)),this.scheduleGc()};return Y(this,Me,Bv({initialPromise:n==null?void 0:n.initialPromise,fn:s.fetchFn,abort:r.abort.bind(r),onSuccess:f=>{var c,v,x,h;if(f===void 0){a(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(f)}catch(S){a(S);return}(v=(c=T(this,st).config).onSuccess)==null||v.call(c,f,this),(h=(x=T(this,st).config).onSettled)==null||h.call(x,f,this.state.error,this),this.scheduleGc()},onError:a,onFail:(f,c)=>{Oe(this,yt,Bt).call(this,{type:"failed",failureCount:f,error:c})},onPause:()=>{Oe(this,yt,Bt).call(this,{type:"pause"})},onContinue:()=>{Oe(this,yt,Bt).call(this,{type:"continue"})},retry:s.options.retry,retryDelay:s.options.retryDelay,networkMode:s.options.networkMode,canRun:()=>!0})),T(this,Me).start()}},Yr=new WeakMap,qr=new WeakMap,st=new WeakMap,Me=new WeakMap,wi=new WeakMap,Zn=new WeakMap,yt=new WeakSet,Bt=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,..._2(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=t.error;return Cl(o)&&o.revert&&T(this,qr)?{...T(this,qr),fetchStatus:"idle"}:{...r,error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),ze.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),T(this,st).notify({query:this,type:"updated",action:t})})},Zp);function _2(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:zv(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function I2(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var Ot,Jp,F2=(Jp=class extends Aa{constructor(t={}){super();Z(this,Ot);this.config=t,Y(this,Ot,new Map)}build(t,n,r){const o=n.queryKey,i=n.queryHash??id(o,n);let s=this.get(i);return s||(s=new D2({cache:this,queryKey:o,queryHash:i,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(o)}),this.add(s)),s}add(t){T(this,Ot).has(t.queryHash)||(T(this,Ot).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=T(this,Ot).get(t.queryHash);n&&(t.destroy(),n===t&&T(this,Ot).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){ze.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return T(this,Ot).get(t)}getAll(){return[...T(this,Ot).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>rp(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>rp(t,r)):n}notify(t){ze.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){ze.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){ze.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},Ot=new WeakMap,Jp),Lt,Fe,Jn,At,cn,eh,$2=(eh=class extends Vv{constructor(t){super();Z(this,At);Z(this,Lt);Z(this,Fe);Z(this,Jn);this.mutationId=t.mutationId,Y(this,Fe,t.mutationCache),Y(this,Lt,[]),this.state=t.state||z2(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){T(this,Lt).includes(t)||(T(this,Lt).push(t),this.clearGcTimeout(),T(this,Fe).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){Y(this,Lt,T(this,Lt).filter(n=>n!==t)),this.scheduleGc(),T(this,Fe).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){T(this,Lt).length||(this.state.status==="pending"?this.scheduleGc():T(this,Fe).remove(this))}continue(){var t;return((t=T(this,Jn))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var o,i,s,a,l,u,d,f,c,v,x,h,S,g,m,w,b,C,k,N;Y(this,Jn,Bv({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(P,O)=>{Oe(this,At,cn).call(this,{type:"failed",failureCount:P,error:O})},onPause:()=>{Oe(this,At,cn).call(this,{type:"pause"})},onContinue:()=>{Oe(this,At,cn).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>T(this,Fe).canRun(this)}));const n=this.state.status==="pending",r=!T(this,Jn).canStart();try{if(!n){Oe(this,At,cn).call(this,{type:"pending",variables:t,isPaused:r}),await((i=(o=T(this,Fe).config).onMutate)==null?void 0:i.call(o,t,this));const O=await((a=(s=this.options).onMutate)==null?void 0:a.call(s,t));O!==this.state.context&&Oe(this,At,cn).call(this,{type:"pending",context:O,variables:t,isPaused:r})}const P=await T(this,Jn).start();return await((u=(l=T(this,Fe).config).onSuccess)==null?void 0:u.call(l,P,t,this.state.context,this)),await((f=(d=this.options).onSuccess)==null?void 0:f.call(d,P,t,this.state.context)),await((v=(c=T(this,Fe).config).onSettled)==null?void 0:v.call(c,P,null,this.state.variables,this.state.context,this)),await((h=(x=this.options).onSettled)==null?void 0:h.call(x,P,null,t,this.state.context)),Oe(this,At,cn).call(this,{type:"success",data:P}),P}catch(P){try{throw await((g=(S=T(this,Fe).config).onError)==null?void 0:g.call(S,P,t,this.state.context,this)),await((w=(m=this.options).onError)==null?void 0:w.call(m,P,t,this.state.context)),await((C=(b=T(this,Fe).config).onSettled)==null?void 0:C.call(b,void 0,P,this.state.variables,this.state.context,this)),await((N=(k=this.options).onSettled)==null?void 0:N.call(k,void 0,P,t,this.state.context)),P}finally{Oe(this,At,cn).call(this,{type:"error",error:P})}}finally{T(this,Fe).runNext(this)}}},Lt=new WeakMap,Fe=new WeakMap,Jn=new WeakMap,At=new WeakSet,cn=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),ze.batch(()=>{T(this,Lt).forEach(r=>{r.onMutationUpdate(t)}),T(this,Fe).notify({mutation:this,type:"updated",action:t})})},eh);function z2(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var qe,Si,th,U2=(th=class extends Aa{constructor(t={}){super();Z(this,qe);Z(this,Si);this.config=t,Y(this,qe,new Map),Y(this,Si,Date.now())}build(t,n,r){const o=new $2({mutationCache:this,mutationId:++_i(this,Si)._,options:t.defaultMutationOptions(n),state:r});return this.add(o),o}add(t){const n=os(t),r=T(this,qe).get(n)??[];r.push(t),T(this,qe).set(n,r),this.notify({type:"added",mutation:t})}remove(t){var r;const n=os(t);if(T(this,qe).has(n)){const o=(r=T(this,qe).get(n))==null?void 0:r.filter(i=>i!==t);o&&(o.length===0?T(this,qe).delete(n):T(this,qe).set(n,o))}this.notify({type:"removed",mutation:t})}canRun(t){var r;const n=(r=T(this,qe).get(os(t)))==null?void 0:r.find(o=>o.state.status==="pending");return!n||n===t}runNext(t){var r;const n=(r=T(this,qe).get(os(t)))==null?void 0:r.find(o=>o!==t&&o.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}clear(){ze.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...T(this,qe).values()].flat()}find(t){const n={exact:!0,...t};return this.getAll().find(r=>op(n,r))}findAll(t={}){return this.getAll().filter(n=>op(t,n))}notify(t){ze.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return ze.batch(()=>Promise.all(t.map(n=>n.continue().catch(vt))))}},qe=new WeakMap,Si=new WeakMap,th);function os(e){var t;return((t=e.options.scope)==null?void 0:t.id)??String(e.mutationId)}function ap(e){return{onFetch:(t,n)=>{var d,f,c,v,x;const r=t.options,o=(c=(f=(d=t.fetchOptions)==null?void 0:d.meta)==null?void 0:f.fetchMore)==null?void 0:c.direction,i=((v=t.state.data)==null?void 0:v.pages)||[],s=((x=t.state.data)==null?void 0:x.pageParams)||[];let a={pages:[],pageParams:[]},l=0;const u=async()=>{let h=!1;const S=w=>{Object.defineProperty(w,"signal",{enumerable:!0,get:()=>(t.signal.aborted?h=!0:t.signal.addEventListener("abort",()=>{h=!0}),t.signal)})},g=Fv(t.options,t.fetchOptions),m=async(w,b,C)=>{if(h)return Promise.reject();if(b==null&&w.pages.length)return Promise.resolve(w);const k={queryKey:t.queryKey,pageParam:b,direction:C?"backward":"forward",meta:t.options.meta};S(k);const N=await g(k),{maxPages:P}=t.options,O=C?R2:T2;return{pages:O(w.pages,N,P),pageParams:O(w.pageParams,b,P)}};if(o&&i.length){const w=o==="backward",b=w?B2:lp,C={pages:i,pageParams:s},k=b(r,C);a=await m(C,k,w)}else{const w=e??i.length;do{const b=l===0?s[0]??r.initialPageParam:lp(r,a);if(l>0&&b==null)break;a=await m(a,b),l++}while(l<w)}return a};t.options.persister?t.fetchFn=()=>{var h,S;return(S=(h=t.options).persister)==null?void 0:S.call(h,u,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=u}}}function lp(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function B2(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var he,yn,xn,Xr,Zr,wn,Jr,eo,nh,V2=(nh=class{constructor(e={}){Z(this,he);Z(this,yn);Z(this,xn);Z(this,Xr);Z(this,Zr);Z(this,wn);Z(this,Jr);Z(this,eo);Y(this,he,e.queryCache||new F2),Y(this,yn,e.mutationCache||new U2),Y(this,xn,e.defaultOptions||{}),Y(this,Xr,new Map),Y(this,Zr,new Map),Y(this,wn,0)}mount(){_i(this,wn)._++,T(this,wn)===1&&(Y(this,Jr,$v.subscribe(async e=>{e&&(await this.resumePausedMutations(),T(this,he).onFocus())})),Y(this,eo,ta.subscribe(async e=>{e&&(await this.resumePausedMutations(),T(this,he).onOnline())})))}unmount(){var e,t;_i(this,wn)._--,T(this,wn)===0&&((e=T(this,Jr))==null||e.call(this),Y(this,Jr,void 0),(t=T(this,eo))==null||t.call(this),Y(this,eo,void 0))}isFetching(e){return T(this,he).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return T(this,yn).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=T(this,he).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const n=this.defaultQueryOptions(e),r=T(this,he).build(this,n);return e.revalidateIfStale&&r.isStaleByTime(np(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return T(this,he).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),o=T(this,he).get(r.queryHash),i=o==null?void 0:o.state.data,s=b2(t,i);if(s!==void 0)return T(this,he).build(this,r).setData(s,{...n,manual:!0})}setQueriesData(e,t,n){return ze.batch(()=>T(this,he).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=T(this,he).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=T(this,he);ze.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=T(this,he),r={type:"active",...e};return ze.batch(()=>(n.findAll(e).forEach(o=>{o.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){const n={revert:!0,...t},r=ze.batch(()=>T(this,he).findAll(e).map(o=>o.cancel(n)));return Promise.all(r).then(vt).catch(vt)}invalidateQueries(e={},t={}){return ze.batch(()=>{if(T(this,he).findAll(e).forEach(r=>{r.invalidate()}),e.refetchType==="none")return Promise.resolve();const n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)})}refetchQueries(e={},t){const n={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},r=ze.batch(()=>T(this,he).findAll(e).filter(o=>!o.isDisabled()).map(o=>{let i=o.fetch(void 0,n);return n.throwOnError||(i=i.catch(vt)),o.state.fetchStatus==="paused"?Promise.resolve():i}));return Promise.all(r).then(vt)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=T(this,he).build(this,t);return n.isStaleByTime(np(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(vt).catch(vt)}fetchInfiniteQuery(e){return e.behavior=ap(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(vt).catch(vt)}ensureInfiniteQueryData(e){return e.behavior=ap(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return ta.isOnline()?T(this,yn).resumePausedMutations():Promise.resolve()}getQueryCache(){return T(this,he)}getMutationCache(){return T(this,yn)}getDefaultOptions(){return T(this,xn)}setDefaultOptions(e){Y(this,xn,e)}setQueryDefaults(e,t){T(this,Xr).set(hi(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...T(this,Xr).values()];let n={};return t.forEach(r=>{mi(e,r.queryKey)&&(n={...n,...r.defaultOptions})}),n}setMutationDefaults(e,t){T(this,Zr).set(hi(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...T(this,Zr).values()];let n={};return t.forEach(r=>{mi(e,r.mutationKey)&&(n={...n,...r.defaultOptions})}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...T(this,xn).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=id(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===sd&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...T(this,xn).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){T(this,he).clear(),T(this,yn).clear()}},he=new WeakMap,yn=new WeakMap,xn=new WeakMap,Xr=new WeakMap,Zr=new WeakMap,wn=new WeakMap,Jr=new WeakMap,eo=new WeakMap,nh),H2=y.createContext(void 0),W2=({client:e,children:t})=>(y.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),p.jsx(H2.Provider,{value:e,children:t}));/**
 * @remix-run/router v1.20.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function gi(){return gi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},gi.apply(this,arguments)}var Cn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Cn||(Cn={}));const up="popstate";function K2(e){e===void 0&&(e={});function t(r,o){let{pathname:i,search:s,hash:a}=r.location;return $u("",{pathname:i,search:s,hash:a},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:na(o)}return G2(t,n,null,e)}function ye(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Hv(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Q2(){return Math.random().toString(36).substr(2,8)}function cp(e,t){return{usr:e.state,key:e.key,idx:t}}function $u(e,t,n,r){return n===void 0&&(n=null),gi({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?wo(t):t,{state:n,key:t&&t.key||r||Q2()})}function na(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function wo(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function G2(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:i=!1}=r,s=o.history,a=Cn.Pop,l=null,u=d();u==null&&(u=0,s.replaceState(gi({},s.state,{idx:u}),""));function d(){return(s.state||{idx:null}).idx}function f(){a=Cn.Pop;let S=d(),g=S==null?null:S-u;u=S,l&&l({action:a,location:h.location,delta:g})}function c(S,g){a=Cn.Push;let m=$u(h.location,S,g);u=d()+1;let w=cp(m,u),b=h.createHref(m);try{s.pushState(w,"",b)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;o.location.assign(b)}i&&l&&l({action:a,location:h.location,delta:1})}function v(S,g){a=Cn.Replace;let m=$u(h.location,S,g);u=d();let w=cp(m,u),b=h.createHref(m);s.replaceState(w,"",b),i&&l&&l({action:a,location:h.location,delta:0})}function x(S){let g=o.location.origin!=="null"?o.location.origin:o.location.href,m=typeof S=="string"?S:na(S);return m=m.replace(/ $/,"%20"),ye(g,"No window.location.(origin|href) available to create URL for href: "+m),new URL(m,g)}let h={get action(){return a},get location(){return e(o,s)},listen(S){if(l)throw new Error("A history only accepts one active listener");return o.addEventListener(up,f),l=S,()=>{o.removeEventListener(up,f),l=null}},createHref(S){return t(o,S)},createURL:x,encodeLocation(S){let g=x(S);return{pathname:g.pathname,search:g.search,hash:g.hash}},push:c,replace:v,go(S){return s.go(S)}};return h}var dp;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(dp||(dp={}));function Y2(e,t,n){return n===void 0&&(n="/"),q2(e,t,n,!1)}function q2(e,t,n,r){let o=typeof t=="string"?wo(t):t,i=ad(o.pathname||"/",n);if(i==null)return null;let s=Wv(e);X2(s);let a=null;for(let l=0;a==null&&l<s.length;++l){let u=lC(i);a=sC(s[l],u,r)}return a}function Wv(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(i,s,a)=>{let l={relativePath:a===void 0?i.path||"":a,caseSensitive:i.caseSensitive===!0,childrenIndex:s,route:i};l.relativePath.startsWith("/")&&(ye(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=Ln([r,l.relativePath]),d=n.concat(l);i.children&&i.children.length>0&&(ye(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Wv(i.children,t,d,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:oC(u,i.index),routesMeta:d})};return e.forEach((i,s)=>{var a;if(i.path===""||!((a=i.path)!=null&&a.includes("?")))o(i,s);else for(let l of Kv(i.path))o(i,s,l)}),t}function Kv(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return o?[i,""]:[i];let s=Kv(r.join("/")),a=[];return a.push(...s.map(l=>l===""?i:[i,l].join("/"))),o&&a.push(...s),a.map(l=>e.startsWith("/")&&l===""?"/":l)}function X2(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:iC(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Z2=/^:[\w-]+$/,J2=3,eC=2,tC=1,nC=10,rC=-2,fp=e=>e==="*";function oC(e,t){let n=e.split("/"),r=n.length;return n.some(fp)&&(r+=rC),t&&(r+=eC),n.filter(o=>!fp(o)).reduce((o,i)=>o+(Z2.test(i)?J2:i===""?tC:nC),r)}function iC(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function sC(e,t,n){let{routesMeta:r}=e,o={},i="/",s=[];for(let a=0;a<r.length;++a){let l=r[a],u=a===r.length-1,d=i==="/"?t:t.slice(i.length)||"/",f=pp({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},d),c=l.route;if(!f&&u&&n&&!r[r.length-1].route.index&&(f=pp({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},d)),!f)return null;Object.assign(o,f.params),s.push({params:o,pathname:Ln([i,f.pathname]),pathnameBase:fC(Ln([i,f.pathnameBase])),route:c}),f.pathnameBase!=="/"&&(i=Ln([i,f.pathnameBase]))}return s}function pp(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=aC(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],s=i.replace(/(.)\/+$/,"$1"),a=o.slice(1);return{params:r.reduce((u,d,f)=>{let{paramName:c,isOptional:v}=d;if(c==="*"){let h=a[f]||"";s=i.slice(0,i.length-h.length).replace(/(.)\/+$/,"$1")}const x=a[f];return v&&!x?u[c]=void 0:u[c]=(x||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:s,pattern:e}}function aC(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Hv(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function lC(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Hv(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function ad(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function uC(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:o=""}=typeof e=="string"?wo(e):e;return{pathname:n?n.startsWith("/")?n:cC(n,t):t,search:pC(r),hash:hC(o)}}function cC(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function El(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function dC(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Qv(e,t){let n=dC(e);return t?n.map((r,o)=>o===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Gv(e,t,n,r){r===void 0&&(r=!1);let o;typeof e=="string"?o=wo(e):(o=gi({},e),ye(!o.pathname||!o.pathname.includes("?"),El("?","pathname","search",o)),ye(!o.pathname||!o.pathname.includes("#"),El("#","pathname","hash",o)),ye(!o.search||!o.search.includes("#"),El("#","search","hash",o)));let i=e===""||o.pathname==="",s=i?"/":o.pathname,a;if(s==null)a=n;else{let f=t.length-1;if(!r&&s.startsWith("..")){let c=s.split("/");for(;c[0]==="..";)c.shift(),f-=1;o.pathname=c.join("/")}a=f>=0?t[f]:"/"}let l=uC(o,a),u=s&&s!=="/"&&s.endsWith("/"),d=(i||s===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||d)&&(l.pathname+="/"),l}const Ln=e=>e.join("/").replace(/\/\/+/g,"/"),fC=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),pC=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,hC=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function mC(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Yv=["post","put","patch","delete"];new Set(Yv);const gC=["get",...Yv];new Set(gC);/**
 * React Router v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function vi(){return vi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},vi.apply(this,arguments)}const ld=y.createContext(null),vC=y.createContext(null),fr=y.createContext(null),Da=y.createContext(null),pr=y.createContext({outlet:null,matches:[],isDataRoute:!1}),qv=y.createContext(null);function yC(e,t){let{relative:n}=t===void 0?{}:t;Ri()||ye(!1);let{basename:r,navigator:o}=y.useContext(fr),{hash:i,pathname:s,search:a}=Zv(e,{relative:n}),l=s;return r!=="/"&&(l=s==="/"?r:Ln([r,s])),o.createHref({pathname:l,search:a,hash:i})}function Ri(){return y.useContext(Da)!=null}function ji(){return Ri()||ye(!1),y.useContext(Da).location}function Xv(e){y.useContext(fr).static||y.useLayoutEffect(e)}function xC(){let{isDataRoute:e}=y.useContext(pr);return e?LC():wC()}function wC(){Ri()||ye(!1);let e=y.useContext(ld),{basename:t,future:n,navigator:r}=y.useContext(fr),{matches:o}=y.useContext(pr),{pathname:i}=ji(),s=JSON.stringify(Qv(o,n.v7_relativeSplatPath)),a=y.useRef(!1);return Xv(()=>{a.current=!0}),y.useCallback(function(u,d){if(d===void 0&&(d={}),!a.current)return;if(typeof u=="number"){r.go(u);return}let f=Gv(u,JSON.parse(s),i,d.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:Ln([t,f.pathname])),(d.replace?r.replace:r.push)(f,d.state,d)},[t,r,s,i,e])}function Zv(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=y.useContext(fr),{matches:o}=y.useContext(pr),{pathname:i}=ji(),s=JSON.stringify(Qv(o,r.v7_relativeSplatPath));return y.useMemo(()=>Gv(e,JSON.parse(s),i,n==="path"),[e,s,i,n])}function SC(e,t){return bC(e,t)}function bC(e,t,n,r){Ri()||ye(!1);let{navigator:o}=y.useContext(fr),{matches:i}=y.useContext(pr),s=i[i.length-1],a=s?s.params:{};s&&s.pathname;let l=s?s.pathnameBase:"/";s&&s.route;let u=ji(),d;if(t){var f;let S=typeof t=="string"?wo(t):t;l==="/"||(f=S.pathname)!=null&&f.startsWith(l)||ye(!1),d=S}else d=u;let c=d.pathname||"/",v=c;if(l!=="/"){let S=l.replace(/^\//,"").split("/");v="/"+c.replace(/^\//,"").split("/").slice(S.length).join("/")}let x=Y2(e,{pathname:v}),h=PC(x&&x.map(S=>Object.assign({},S,{params:Object.assign({},a,S.params),pathname:Ln([l,o.encodeLocation?o.encodeLocation(S.pathname).pathname:S.pathname]),pathnameBase:S.pathnameBase==="/"?l:Ln([l,o.encodeLocation?o.encodeLocation(S.pathnameBase).pathname:S.pathnameBase])})),i,n,r);return t&&h?y.createElement(Da.Provider,{value:{location:vi({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:Cn.Pop}},h):h}function CC(){let e=OC(),t=mC(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return y.createElement(y.Fragment,null,y.createElement("h2",null,"Unexpected Application Error!"),y.createElement("h3",{style:{fontStyle:"italic"}},t),n?y.createElement("pre",{style:o},n):null,null)}const EC=y.createElement(CC,null);class kC extends y.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?y.createElement(pr.Provider,{value:this.props.routeContext},y.createElement(qv.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function NC(e){let{routeContext:t,match:n,children:r}=e,o=y.useContext(ld);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),y.createElement(pr.Provider,{value:t},r)}function PC(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let s=e,a=(o=n)==null?void 0:o.errors;if(a!=null){let d=s.findIndex(f=>f.route.id&&(a==null?void 0:a[f.route.id])!==void 0);d>=0||ye(!1),s=s.slice(0,Math.min(s.length,d+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<s.length;d++){let f=s[d];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(u=d),f.route.id){let{loaderData:c,errors:v}=n,x=f.route.loader&&c[f.route.id]===void 0&&(!v||v[f.route.id]===void 0);if(f.route.lazy||x){l=!0,u>=0?s=s.slice(0,u+1):s=[s[0]];break}}}return s.reduceRight((d,f,c)=>{let v,x=!1,h=null,S=null;n&&(v=a&&f.route.id?a[f.route.id]:void 0,h=f.route.errorElement||EC,l&&(u<0&&c===0?(x=!0,S=null):u===c&&(x=!0,S=f.route.hydrateFallbackElement||null)));let g=t.concat(s.slice(0,c+1)),m=()=>{let w;return v?w=h:x?w=S:f.route.Component?w=y.createElement(f.route.Component,null):f.route.element?w=f.route.element:w=d,y.createElement(NC,{match:f,routeContext:{outlet:d,matches:g,isDataRoute:n!=null},children:w})};return n&&(f.route.ErrorBoundary||f.route.errorElement||c===0)?y.createElement(kC,{location:n.location,revalidation:n.revalidation,component:h,error:v,children:m(),routeContext:{outlet:null,matches:g,isDataRoute:!0}}):m()},null)}var Jv=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Jv||{}),ra=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ra||{});function TC(e){let t=y.useContext(ld);return t||ye(!1),t}function RC(e){let t=y.useContext(vC);return t||ye(!1),t}function jC(e){let t=y.useContext(pr);return t||ye(!1),t}function ey(e){let t=jC(),n=t.matches[t.matches.length-1];return n.route.id||ye(!1),n.route.id}function OC(){var e;let t=y.useContext(qv),n=RC(ra.UseRouteError),r=ey(ra.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function LC(){let{router:e}=TC(Jv.UseNavigateStable),t=ey(ra.UseNavigateStable),n=y.useRef(!1);return Xv(()=>{n.current=!0}),y.useCallback(function(o,i){i===void 0&&(i={}),n.current&&(typeof o=="number"?e.navigate(o):e.navigate(o,vi({fromRouteId:t},i)))},[e,t])}function zu(e){ye(!1)}function AC(e){let{basename:t="/",children:n=null,location:r,navigationType:o=Cn.Pop,navigator:i,static:s=!1,future:a}=e;Ri()&&ye(!1);let l=t.replace(/^\/*/,"/"),u=y.useMemo(()=>({basename:l,navigator:i,static:s,future:vi({v7_relativeSplatPath:!1},a)}),[l,a,i,s]);typeof r=="string"&&(r=wo(r));let{pathname:d="/",search:f="",hash:c="",state:v=null,key:x="default"}=r,h=y.useMemo(()=>{let S=ad(d,l);return S==null?null:{location:{pathname:S,search:f,hash:c,state:v,key:x},navigationType:o}},[l,d,f,c,v,x,o]);return h==null?null:y.createElement(fr.Provider,{value:u},y.createElement(Da.Provider,{children:n,value:h}))}function MC(e){let{children:t,location:n}=e;return SC(Uu(t),n)}new Promise(()=>{});function Uu(e,t){t===void 0&&(t=[]);let n=[];return y.Children.forEach(e,(r,o)=>{if(!y.isValidElement(r))return;let i=[...t,o];if(r.type===y.Fragment){n.push.apply(n,Uu(r.props.children,i));return}r.type!==zu&&ye(!1),!r.props.index||!r.props.children||ye(!1);let s={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(s.children=Uu(r.props.children,i)),n.push(s)}),n}/**
 * React Router DOM v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Bu(){return Bu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Bu.apply(this,arguments)}function DC(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function _C(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function IC(e,t){return e.button===0&&(!t||t==="_self")&&!_C(e)}const FC=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],$C="6";try{window.__reactRouterVersion=$C}catch{}const zC="startTransition",hp=hh[zC];function UC(e){let{basename:t,children:n,future:r,window:o}=e,i=y.useRef();i.current==null&&(i.current=K2({window:o,v5Compat:!0}));let s=i.current,[a,l]=y.useState({action:s.action,location:s.location}),{v7_startTransition:u}=r||{},d=y.useCallback(f=>{u&&hp?hp(()=>l(f)):l(f)},[l,u]);return y.useLayoutEffect(()=>s.listen(d),[s,d]),y.createElement(AC,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:s,future:r})}const BC=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",VC=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,kl=y.forwardRef(function(t,n){let{onClick:r,relative:o,reloadDocument:i,replace:s,state:a,target:l,to:u,preventScrollReset:d,viewTransition:f}=t,c=DC(t,FC),{basename:v}=y.useContext(fr),x,h=!1;if(typeof u=="string"&&VC.test(u)&&(x=u,BC))try{let w=new URL(window.location.href),b=u.startsWith("//")?new URL(w.protocol+u):new URL(u),C=ad(b.pathname,v);b.origin===w.origin&&C!=null?u=C+b.search+b.hash:h=!0}catch{}let S=yC(u,{relative:o}),g=HC(u,{replace:s,state:a,target:l,preventScrollReset:d,relative:o,viewTransition:f});function m(w){r&&r(w),w.defaultPrevented||g(w)}return y.createElement("a",Bu({},c,{href:x||S,onClick:h||i?r:m,ref:n,target:l}))});var mp;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(mp||(mp={}));var gp;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(gp||(gp={}));function HC(e,t){let{target:n,replace:r,state:o,preventScrollReset:i,relative:s,viewTransition:a}=t===void 0?{}:t,l=xC(),u=ji(),d=Zv(e,{relative:s});return y.useCallback(f=>{if(IC(f,n)){f.preventDefault();let c=r!==void 0?r:na(u)===na(d);l(e,{replace:c,state:o,preventScrollReset:i,relative:s,viewTransition:a})}},[u,l,d,r,o,n,e,i,s,a])}const WC=Qc("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),Ee=y.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},i)=>{const s=r?lo:"button";return p.jsx(s,{className:we(WC({variant:t,size:n,className:e})),ref:i,...o})});Ee.displayName="Button";class vp extends y.Component{constructor(n){super(n);Ba(this,"handleRefresh",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0}),window.location.reload()});Ba(this,"handleGoHome",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0}),window.location.href="/"});this.state={hasError:!1}}static getDerivedStateFromError(n){return{hasError:!0,error:n}}componentDidCatch(n,r){console.error("ErrorBoundary caught an error:",n,r),this.setState({error:n,errorInfo:r})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:p.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:p.jsxs("div",{className:"max-w-md w-full text-center space-y-6",children:[p.jsx("div",{className:"flex justify-center",children:p.jsx("div",{className:"p-4 bg-destructive/10 rounded-full",children:p.jsx(uS,{className:"w-12 h-12 text-destructive"})})}),p.jsxs("div",{className:"space-y-2",children:[p.jsx("h1",{className:"text-2xl font-bold text-foreground",children:"出现了一些问题"}),p.jsx("p",{className:"text-muted-foreground",children:"很抱歉，页面遇到了意外错误。我们已经记录了这个问题。"})]}),!1,p.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[p.jsxs(Ee,{onClick:this.handleRefresh,className:"flex items-center gap-2",variant:"default",children:[p.jsx(oS,{className:"w-4 h-4"}),"刷新页面"]}),p.jsxs(Ee,{onClick:this.handleGoHome,variant:"outline",className:"flex items-center gap-2",children:[p.jsx(Zg,{className:"w-4 h-4"}),"返回首页"]})]}),p.jsx("div",{className:"text-sm text-muted-foreground",children:p.jsx("p",{children:"如果问题持续存在，请联系我们的技术支持"})})]})}):this.props.children}}const KC=(e,t,n,r)=>{var i,s,a,l;const o=[n,{code:t,...r||{}}];if((s=(i=e==null?void 0:e.services)==null?void 0:i.logger)!=null&&s.forward)return e.services.logger.forward(o,"warn","react-i18next::",!0);nr(o[0])&&(o[0]=`react-i18next:: ${o[0]}`),(l=(a=e==null?void 0:e.services)==null?void 0:a.logger)!=null&&l.warn?e.services.logger.warn(...o):console!=null&&console.warn&&console.warn(...o)},yp={},Vu=(e,t,n,r)=>{nr(n)&&yp[n]||(nr(n)&&(yp[n]=new Date),KC(e,t,n,r))},ty=(e,t)=>()=>{if(e.isInitialized)t();else{const n=()=>{setTimeout(()=>{e.off("initialized",n)},0),t()};e.on("initialized",n)}},Hu=(e,t,n)=>{e.loadNamespaces(t,ty(e,n))},xp=(e,t,n,r)=>{if(nr(n)&&(n=[n]),e.options.preload&&e.options.preload.indexOf(t)>-1)return Hu(e,n,r);n.forEach(o=>{e.options.ns.indexOf(o)<0&&e.options.ns.push(o)}),e.loadLanguages(t,ty(e,r))},QC=(e,t,n={})=>!t.languages||!t.languages.length?(Vu(t,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:t.languages}),!0):t.hasLoadedNamespace(e,{lng:n.lng,precheck:(r,o)=>{var i;if(((i=n.bindI18n)==null?void 0:i.indexOf("languageChanging"))>-1&&r.services.backendConnector.backend&&r.isLanguageChangingTo&&!o(r.isLanguageChangingTo,e))return!1}}),nr=e=>typeof e=="string",GC=e=>typeof e=="object"&&e!==null,YC=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,qC={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},XC=e=>qC[e],ZC=e=>e.replace(YC,XC);let Wu={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:ZC};const JC=(e={})=>{Wu={...Wu,...e}},eE=()=>Wu;let ny;const tE=e=>{ny=e},nE=()=>ny,rE={type:"3rdParty",init(e){JC(e.options.react),tE(e)}},oE=y.createContext();class iE{constructor(){this.usedNamespaces={}}addUsedNamespaces(t){t.forEach(n=>{this.usedNamespaces[n]||(this.usedNamespaces[n]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const sE=(e,t)=>{const n=y.useRef();return y.useEffect(()=>{n.current=e},[e,t]),n.current},ry=(e,t,n,r)=>e.getFixedT(t,n,r),aE=(e,t,n,r)=>y.useCallback(ry(e,t,n,r),[e,t,n,r]),Vn=(e,t={})=>{var b,C,k,N;const{i18n:n}=t,{i18n:r,defaultNS:o}=y.useContext(oE)||{},i=n||r||nE();if(i&&!i.reportNamespaces&&(i.reportNamespaces=new iE),!i){Vu(i,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const P=(R,$)=>nr($)?$:GC($)&&nr($.defaultValue)?$.defaultValue:Array.isArray(R)?R[R.length-1]:R,O=[P,{},!1];return O.t=P,O.i18n={},O.ready=!1,O}(b=i.options.react)!=null&&b.wait&&Vu(i,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const s={...eE(),...i.options.react,...t},{useSuspense:a,keyPrefix:l}=s;let u=o||((C=i.options)==null?void 0:C.defaultNS);u=nr(u)?[u]:u||["translation"],(N=(k=i.reportNamespaces).addUsedNamespaces)==null||N.call(k,u);const d=(i.isInitialized||i.initializedStoreOnce)&&u.every(P=>QC(P,i,s)),f=aE(i,t.lng||null,s.nsMode==="fallback"?u:u[0],l),c=()=>f,v=()=>ry(i,t.lng||null,s.nsMode==="fallback"?u:u[0],l),[x,h]=y.useState(c);let S=u.join();t.lng&&(S=`${t.lng}${S}`);const g=sE(S),m=y.useRef(!0);y.useEffect(()=>{const{bindI18n:P,bindI18nStore:O}=s;m.current=!0,!d&&!a&&(t.lng?xp(i,t.lng,u,()=>{m.current&&h(v)}):Hu(i,u,()=>{m.current&&h(v)})),d&&g&&g!==S&&m.current&&h(v);const R=()=>{m.current&&h(v)};return P&&(i==null||i.on(P,R)),O&&(i==null||i.store.on(O,R)),()=>{m.current=!1,i&&(P==null||P.split(" ").forEach($=>i.off($,R))),O&&i&&O.split(" ").forEach($=>i.store.off($,R))}},[i,S]),y.useEffect(()=>{m.current&&d&&h(c)},[i,l,d]);const w=[x,i,d];if(w.t=x,w.i18n=i,w.ready=d,d||!d&&!a)return w;throw new Promise(P=>{t.lng?xp(i,t.lng,u,()=>P()):Hu(i,u,()=>P())})};var Nl="focusScope.autoFocusOnMount",Pl="focusScope.autoFocusOnUnmount",wp={bubbles:!1,cancelable:!0},lE="FocusScope",oy=y.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...s}=e,[a,l]=y.useState(null),u=pt(o),d=pt(i),f=y.useRef(null),c=Be(t,h=>l(h)),v=y.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;y.useEffect(()=>{if(r){let h=function(w){if(v.paused||!a)return;const b=w.target;a.contains(b)?f.current=b:dn(f.current,{select:!0})},S=function(w){if(v.paused||!a)return;const b=w.relatedTarget;b!==null&&(a.contains(b)||dn(f.current,{select:!0}))},g=function(w){if(document.activeElement===document.body)for(const C of w)C.removedNodes.length>0&&dn(a)};document.addEventListener("focusin",h),document.addEventListener("focusout",S);const m=new MutationObserver(g);return a&&m.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",h),document.removeEventListener("focusout",S),m.disconnect()}}},[r,a,v.paused]),y.useEffect(()=>{if(a){bp.add(v);const h=document.activeElement;if(!a.contains(h)){const g=new CustomEvent(Nl,wp);a.addEventListener(Nl,u),a.dispatchEvent(g),g.defaultPrevented||(uE(hE(iy(a)),{select:!0}),document.activeElement===h&&dn(a))}return()=>{a.removeEventListener(Nl,u),setTimeout(()=>{const g=new CustomEvent(Pl,wp);a.addEventListener(Pl,d),a.dispatchEvent(g),g.defaultPrevented||dn(h??document.body,{select:!0}),a.removeEventListener(Pl,d),bp.remove(v)},0)}}},[a,u,d,v]);const x=y.useCallback(h=>{if(!n&&!r||v.paused)return;const S=h.key==="Tab"&&!h.altKey&&!h.ctrlKey&&!h.metaKey,g=document.activeElement;if(S&&g){const m=h.currentTarget,[w,b]=cE(m);w&&b?!h.shiftKey&&g===b?(h.preventDefault(),n&&dn(w,{select:!0})):h.shiftKey&&g===w&&(h.preventDefault(),n&&dn(b,{select:!0})):g===m&&h.preventDefault()}},[n,r,v.paused]);return p.jsx(xe.div,{tabIndex:-1,...s,ref:c,onKeyDown:x})});oy.displayName=lE;function uE(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(dn(r,{select:t}),document.activeElement!==n)return}function cE(e){const t=iy(e),n=Sp(t,e),r=Sp(t.reverse(),e);return[n,r]}function iy(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Sp(e,t){for(const n of e)if(!dE(n,{upTo:t}))return n}function dE(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function fE(e){return e instanceof HTMLInputElement&&"select"in e}function dn(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&fE(e)&&t&&e.select()}}var bp=pE();function pE(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Cp(e,t),e.unshift(t)},remove(t){var n;e=Cp(e,t),(n=e[0])==null||n.resume()}}}function Cp(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function hE(e){return e.filter(t=>t.tagName!=="A")}var Tl=0;function mE(){y.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Ep()),document.body.insertAdjacentElement("beforeend",e[1]??Ep()),Tl++,()=>{Tl===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Tl--}},[])}function Ep(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Dt=function(){return Dt=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},Dt.apply(this,arguments)};function sy(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function gE(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var ks="right-scroll-bar-position",Ns="width-before-scroll-bar",vE="with-scroll-bars-hidden",yE="--removed-body-scroll-bar-size";function Rl(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function xE(e,t){var n=y.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var wE=typeof window<"u"?y.useLayoutEffect:y.useEffect,kp=new WeakMap;function SE(e,t){var n=xE(null,function(r){return e.forEach(function(o){return Rl(o,r)})});return wE(function(){var r=kp.get(n);if(r){var o=new Set(r),i=new Set(e),s=n.current;o.forEach(function(a){i.has(a)||Rl(a,null)}),i.forEach(function(a){o.has(a)||Rl(a,s)})}kp.set(n,e)},[e]),n}function bE(e){return e}function CE(e,t){t===void 0&&(t=bE);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var s=t(i,r);return n.push(s),function(){n=n.filter(function(a){return a!==s})}},assignSyncMedium:function(i){for(r=!0;n.length;){var s=n;n=[],s.forEach(i)}n={push:function(a){return i(a)},filter:function(){return n}}},assignMedium:function(i){r=!0;var s=[];if(n.length){var a=n;n=[],a.forEach(i),s=n}var l=function(){var d=s;s=[],d.forEach(i)},u=function(){return Promise.resolve().then(l)};u(),n={push:function(d){s.push(d),u()},filter:function(d){return s=s.filter(d),n}}}};return o}function EE(e){e===void 0&&(e={});var t=CE(null);return t.options=Dt({async:!0,ssr:!1},e),t}var ay=function(e){var t=e.sideCar,n=sy(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return y.createElement(r,Dt({},n))};ay.isSideCarExport=!0;function kE(e,t){return e.useMedium(t),ay}var ly=EE(),jl=function(){},_a=y.forwardRef(function(e,t){var n=y.useRef(null),r=y.useState({onScrollCapture:jl,onWheelCapture:jl,onTouchMoveCapture:jl}),o=r[0],i=r[1],s=e.forwardProps,a=e.children,l=e.className,u=e.removeScrollBar,d=e.enabled,f=e.shards,c=e.sideCar,v=e.noIsolation,x=e.inert,h=e.allowPinchZoom,S=e.as,g=S===void 0?"div":S,m=e.gapMode,w=sy(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),b=c,C=SE([n,t]),k=Dt(Dt({},w),o);return y.createElement(y.Fragment,null,d&&y.createElement(b,{sideCar:ly,removeScrollBar:u,shards:f,noIsolation:v,inert:x,setCallbacks:i,allowPinchZoom:!!h,lockRef:n,gapMode:m}),s?y.cloneElement(y.Children.only(a),Dt(Dt({},k),{ref:C})):y.createElement(g,Dt({},k,{className:l,ref:C}),a))});_a.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};_a.classNames={fullWidth:Ns,zeroRight:ks};var NE=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function PE(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=NE();return t&&e.setAttribute("nonce",t),e}function TE(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function RE(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var jE=function(){var e=0,t=null;return{add:function(n){e==0&&(t=PE())&&(TE(t,n),RE(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},OE=function(){var e=jE();return function(t,n){y.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},uy=function(){var e=OE(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},LE={left:0,top:0,right:0,gap:0},Ol=function(e){return parseInt(e||"",10)||0},AE=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Ol(n),Ol(r),Ol(o)]},ME=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return LE;var t=AE(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},DE=uy(),Wr="data-scroll-locked",_E=function(e,t,n,r){var o=e.left,i=e.top,s=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(vE,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(Wr,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(s,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(ks,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(Ns,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(ks," .").concat(ks,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Ns," .").concat(Ns,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Wr,`] {
    `).concat(yE,": ").concat(a,`px;
  }
`)},Np=function(){var e=parseInt(document.body.getAttribute(Wr)||"0",10);return isFinite(e)?e:0},IE=function(){y.useEffect(function(){return document.body.setAttribute(Wr,(Np()+1).toString()),function(){var e=Np()-1;e<=0?document.body.removeAttribute(Wr):document.body.setAttribute(Wr,e.toString())}},[])},FE=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;IE();var i=y.useMemo(function(){return ME(o)},[o]);return y.createElement(DE,{styles:_E(i,!t,o,n?"":"!important")})},Ku=!1;if(typeof window<"u")try{var is=Object.defineProperty({},"passive",{get:function(){return Ku=!0,!0}});window.addEventListener("test",is,is),window.removeEventListener("test",is,is)}catch{Ku=!1}var xr=Ku?{passive:!1}:!1,$E=function(e){return e.tagName==="TEXTAREA"},cy=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!$E(e)&&n[t]==="visible")},zE=function(e){return cy(e,"overflowY")},UE=function(e){return cy(e,"overflowX")},Pp=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=dy(e,r);if(o){var i=fy(e,r),s=i[1],a=i[2];if(s>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},BE=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},VE=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},dy=function(e,t){return e==="v"?zE(t):UE(t)},fy=function(e,t){return e==="v"?BE(t):VE(t)},HE=function(e,t){return e==="h"&&t==="rtl"?-1:1},WE=function(e,t,n,r,o){var i=HE(e,window.getComputedStyle(t).direction),s=i*r,a=n.target,l=t.contains(a),u=!1,d=s>0,f=0,c=0;do{var v=fy(e,a),x=v[0],h=v[1],S=v[2],g=h-S-i*x;(x||g)&&dy(e,a)&&(f+=g,c+=x),a instanceof ShadowRoot?a=a.host:a=a.parentNode}while(!l&&a!==document.body||l&&(t.contains(a)||t===a));return(d&&(Math.abs(f)<1||!o)||!d&&(Math.abs(c)<1||!o))&&(u=!0),u},ss=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Tp=function(e){return[e.deltaX,e.deltaY]},Rp=function(e){return e&&"current"in e?e.current:e},KE=function(e,t){return e[0]===t[0]&&e[1]===t[1]},QE=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},GE=0,wr=[];function YE(e){var t=y.useRef([]),n=y.useRef([0,0]),r=y.useRef(),o=y.useState(GE++)[0],i=y.useState(uy)[0],s=y.useRef(e);y.useEffect(function(){s.current=e},[e]),y.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var h=gE([e.lockRef.current],(e.shards||[]).map(Rp),!0).filter(Boolean);return h.forEach(function(S){return S.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),h.forEach(function(S){return S.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=y.useCallback(function(h,S){if("touches"in h&&h.touches.length===2||h.type==="wheel"&&h.ctrlKey)return!s.current.allowPinchZoom;var g=ss(h),m=n.current,w="deltaX"in h?h.deltaX:m[0]-g[0],b="deltaY"in h?h.deltaY:m[1]-g[1],C,k=h.target,N=Math.abs(w)>Math.abs(b)?"h":"v";if("touches"in h&&N==="h"&&k.type==="range")return!1;var P=Pp(N,k);if(!P)return!0;if(P?C=N:(C=N==="v"?"h":"v",P=Pp(N,k)),!P)return!1;if(!r.current&&"changedTouches"in h&&(w||b)&&(r.current=C),!C)return!0;var O=r.current||C;return WE(O,S,h,O==="h"?w:b,!0)},[]),l=y.useCallback(function(h){var S=h;if(!(!wr.length||wr[wr.length-1]!==i)){var g="deltaY"in S?Tp(S):ss(S),m=t.current.filter(function(C){return C.name===S.type&&(C.target===S.target||S.target===C.shadowParent)&&KE(C.delta,g)})[0];if(m&&m.should){S.cancelable&&S.preventDefault();return}if(!m){var w=(s.current.shards||[]).map(Rp).filter(Boolean).filter(function(C){return C.contains(S.target)}),b=w.length>0?a(S,w[0]):!s.current.noIsolation;b&&S.cancelable&&S.preventDefault()}}},[]),u=y.useCallback(function(h,S,g,m){var w={name:h,delta:S,target:g,should:m,shadowParent:qE(g)};t.current.push(w),setTimeout(function(){t.current=t.current.filter(function(b){return b!==w})},1)},[]),d=y.useCallback(function(h){n.current=ss(h),r.current=void 0},[]),f=y.useCallback(function(h){u(h.type,Tp(h),h.target,a(h,e.lockRef.current))},[]),c=y.useCallback(function(h){u(h.type,ss(h),h.target,a(h,e.lockRef.current))},[]);y.useEffect(function(){return wr.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:c}),document.addEventListener("wheel",l,xr),document.addEventListener("touchmove",l,xr),document.addEventListener("touchstart",d,xr),function(){wr=wr.filter(function(h){return h!==i}),document.removeEventListener("wheel",l,xr),document.removeEventListener("touchmove",l,xr),document.removeEventListener("touchstart",d,xr)}},[]);var v=e.removeScrollBar,x=e.inert;return y.createElement(y.Fragment,null,x?y.createElement(i,{styles:QE(o)}):null,v?y.createElement(FE,{gapMode:e.gapMode}):null)}function qE(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const XE=kE(ly,YE);var py=y.forwardRef(function(e,t){return y.createElement(_a,Dt({},e,{ref:t,sideCar:XE}))});py.classNames=_a.classNames;var ZE=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Sr=new WeakMap,as=new WeakMap,ls={},Ll=0,hy=function(e){return e&&(e.host||hy(e.parentNode))},JE=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=hy(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},ek=function(e,t,n,r){var o=JE(t,Array.isArray(e)?e:[e]);ls[n]||(ls[n]=new WeakMap);var i=ls[n],s=[],a=new Set,l=new Set(o),u=function(f){!f||a.has(f)||(a.add(f),u(f.parentNode))};o.forEach(u);var d=function(f){!f||l.has(f)||Array.prototype.forEach.call(f.children,function(c){if(a.has(c))d(c);else try{var v=c.getAttribute(r),x=v!==null&&v!=="false",h=(Sr.get(c)||0)+1,S=(i.get(c)||0)+1;Sr.set(c,h),i.set(c,S),s.push(c),h===1&&x&&as.set(c,!0),S===1&&c.setAttribute(n,"true"),x||c.setAttribute(r,"true")}catch(g){console.error("aria-hidden: cannot operate on ",c,g)}})};return d(t),a.clear(),Ll++,function(){s.forEach(function(f){var c=Sr.get(f)-1,v=i.get(f)-1;Sr.set(f,c),i.set(f,v),c||(as.has(f)||f.removeAttribute(r),as.delete(f)),v||f.removeAttribute(n)}),Ll--,Ll||(Sr=new WeakMap,Sr=new WeakMap,as=new WeakMap,ls={})}},tk=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=ZE(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),ek(r,o,n,"aria-hidden")):function(){return null}},ud="Dialog",[my,$N]=Bc(ud),[nk,Nt]=my(ud),gy=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:i,modal:s=!0}=e,a=y.useRef(null),l=y.useRef(null),[u=!1,d]=Ng({prop:r,defaultProp:o,onChange:i});return p.jsx(nk,{scope:t,triggerRef:a,contentRef:l,contentId:wl(),titleId:wl(),descriptionId:wl(),open:u,onOpenChange:d,onOpenToggle:y.useCallback(()=>d(f=>!f),[d]),modal:s,children:n})};gy.displayName=ud;var vy="DialogTrigger",rk=y.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Nt(vy,n),i=Be(t,o.triggerRef);return p.jsx(xe.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":fd(o.open),...r,ref:i,onClick:ce(e.onClick,o.onOpenToggle)})});rk.displayName=vy;var cd="DialogPortal",[ok,yy]=my(cd,{forceMount:void 0}),xy=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,i=Nt(cd,t);return p.jsx(ok,{scope:t,forceMount:n,children:y.Children.map(r,s=>p.jsx(go,{present:n||i.open,children:p.jsx(Vc,{asChild:!0,container:o,children:s})}))})};xy.displayName=cd;var oa="DialogOverlay",wy=y.forwardRef((e,t)=>{const n=yy(oa,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=Nt(oa,e.__scopeDialog);return i.modal?p.jsx(go,{present:r||i.open,children:p.jsx(ik,{...o,ref:t})}):null});wy.displayName=oa;var ik=y.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Nt(oa,n);return p.jsx(py,{as:lo,allowPinchZoom:!0,shards:[o.contentRef],children:p.jsx(xe.div,{"data-state":fd(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),ur="DialogContent",Sy=y.forwardRef((e,t)=>{const n=yy(ur,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=Nt(ur,e.__scopeDialog);return p.jsx(go,{present:r||i.open,children:i.modal?p.jsx(sk,{...o,ref:t}):p.jsx(ak,{...o,ref:t})})});Sy.displayName=ur;var sk=y.forwardRef((e,t)=>{const n=Nt(ur,e.__scopeDialog),r=y.useRef(null),o=Be(t,n.contentRef,r);return y.useEffect(()=>{const i=r.current;if(i)return tk(i)},[]),p.jsx(by,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:ce(e.onCloseAutoFocus,i=>{var s;i.preventDefault(),(s=n.triggerRef.current)==null||s.focus()}),onPointerDownOutside:ce(e.onPointerDownOutside,i=>{const s=i.detail.originalEvent,a=s.button===0&&s.ctrlKey===!0;(s.button===2||a)&&i.preventDefault()}),onFocusOutside:ce(e.onFocusOutside,i=>i.preventDefault())})}),ak=y.forwardRef((e,t)=>{const n=Nt(ur,e.__scopeDialog),r=y.useRef(!1),o=y.useRef(!1);return p.jsx(by,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:i=>{var s,a;(s=e.onCloseAutoFocus)==null||s.call(e,i),i.defaultPrevented||(r.current||(a=n.triggerRef.current)==null||a.focus(),i.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:i=>{var l,u;(l=e.onInteractOutside)==null||l.call(e,i),i.defaultPrevented||(r.current=!0,i.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const s=i.target;((u=n.triggerRef.current)==null?void 0:u.contains(s))&&i.preventDefault(),i.detail.originalEvent.type==="focusin"&&o.current&&i.preventDefault()}})}),by=y.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,...s}=e,a=Nt(ur,n),l=y.useRef(null),u=Be(t,l);return mE(),p.jsxs(p.Fragment,{children:[p.jsx(oy,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i,children:p.jsx(Ea,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":fd(a.open),...s,ref:u,onDismiss:()=>a.onOpenChange(!1)})}),p.jsxs(p.Fragment,{children:[p.jsx(lk,{titleId:a.titleId}),p.jsx(ck,{contentRef:l,descriptionId:a.descriptionId})]})]})}),dd="DialogTitle",Cy=y.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Nt(dd,n);return p.jsx(xe.h2,{id:o.titleId,...r,ref:t})});Cy.displayName=dd;var Ey="DialogDescription",ky=y.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Nt(Ey,n);return p.jsx(xe.p,{id:o.descriptionId,...r,ref:t})});ky.displayName=Ey;var Ny="DialogClose",Py=y.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Nt(Ny,n);return p.jsx(xe.button,{type:"button",...r,ref:t,onClick:ce(e.onClick,()=>o.onOpenChange(!1))})});Py.displayName=Ny;function fd(e){return e?"open":"closed"}var Ty="DialogTitleWarning",[zN,Ry]=dw(Ty,{contentName:ur,titleName:dd,docsSlug:"dialog"}),lk=({titleId:e})=>{const t=Ry(Ty),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return y.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},uk="DialogDescriptionWarning",ck=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Ry(uk).contentName}}.`;return y.useEffect(()=>{var i;const o=(i=e.current)==null?void 0:i.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},dk=gy,fk=xy,jy=wy,Oy=Sy,Ly=Cy,Ay=ky,pk=Py;const hk=dk,mk=fk,My=y.forwardRef(({className:e,...t},n)=>p.jsx(jy,{ref:n,className:we("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));My.displayName=jy.displayName;const Dy=y.forwardRef(({className:e,children:t,...n},r)=>p.jsxs(mk,{children:[p.jsx(My,{}),p.jsxs(Oy,{ref:r,className:we("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...n,children:[t,p.jsxs(pk,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[p.jsx(Yc,{className:"h-4 w-4"}),p.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Dy.displayName=Oy.displayName;const _y=({className:e,...t})=>p.jsx("div",{className:we("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});_y.displayName="DialogHeader";const Iy=y.forwardRef(({className:e,...t},n)=>p.jsx(Ly,{ref:n,className:we("text-lg font-semibold leading-none tracking-tight",e),...t}));Iy.displayName=Ly.displayName;const gk=y.forwardRef(({className:e,...t},n)=>p.jsx(Ay,{ref:n,className:we("text-sm text-muted-foreground",e),...t}));gk.displayName=Ay.displayName;const Oi=({isOpen:e,onClose:t})=>{const{toast:n}=xg(),r="https://t.me/ZhuaMaCode",o="@ZHUAMACODE",i=a=>{navigator.clipboard.writeText(a),n({title:"已复制到剪贴板",description:"Telegram联系方式已复制"})},s=()=>{window.open(r,"_blank")};return p.jsx(hk,{open:e,onOpenChange:t,children:p.jsxs(Dy,{className:"max-w-md mx-auto bg-card/95 backdrop-blur-lg border-primary/20",children:[p.jsx(_y,{children:p.jsx(Iy,{className:"text-center text-2xl font-bold bg-gradient-primary bg-clip-text text-transparent",children:"联系我们"})}),p.jsxs("div",{className:"space-y-6 py-4",children:[p.jsx("div",{className:"flex justify-center",children:p.jsxs("div",{className:"relative",children:[p.jsx("img",{src:"/lovable-uploads/49b2daf0-5632-4ea8-9987-23f4f03c5173.png",alt:"Telegram QR Code",className:"w-48 h-48 rounded-2xl shadow-glow animate-glow"}),p.jsx("div",{className:"absolute inset-0 bg-gradient-primary opacity-10 rounded-2xl"})]})}),p.jsxs("div",{className:"text-center space-y-4",children:[p.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[p.jsx(Bf,{className:"w-5 h-5 text-primary animate-pulse"}),p.jsx("span",{className:"text-lg font-semibold text-foreground",children:"Telegram"})]}),p.jsxs("div",{className:"bg-muted/50 rounded-lg p-4 border border-primary/20",children:[p.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"用户名"}),p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsx("code",{className:"text-primary font-mono text-lg",children:o}),p.jsx(Ee,{variant:"ghost",size:"sm",onClick:()=>i(o),className:"text-primary hover:bg-primary/10",children:p.jsx(zf,{className:"w-4 h-4"})})]})]}),p.jsxs("div",{className:"bg-muted/50 rounded-lg p-4 border border-primary/20",children:[p.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"直接链接"}),p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsx("code",{className:"text-primary font-mono text-sm break-all",children:r}),p.jsx(Ee,{variant:"ghost",size:"sm",onClick:()=>i(r),className:"text-primary hover:bg-primary/10 ml-2",children:p.jsx(zf,{className:"w-4 h-4"})})]})]})]}),p.jsxs("div",{className:"bg-primary/10 border border-primary/30 rounded-lg p-4",children:[p.jsx("p",{className:"text-center text-primary font-medium",children:"💬 任何定制化需求请Telegram沟通"}),p.jsx("p",{className:"text-center text-sm text-muted-foreground mt-1",children:"我们会在第一时间回复您的消息"})]}),p.jsxs("div",{className:"flex flex-col space-y-3",children:[p.jsxs(Ee,{onClick:s,className:"bg-gradient-primary hover:shadow-glow transition-all duration-300 group",size:"lg",children:[p.jsx(Bf,{className:"w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform duration-300"}),"打开 Telegram",p.jsx(qg,{className:"w-4 h-4 ml-2"})]}),p.jsx(Ee,{variant:"outline",onClick:t,className:"border-primary/30 hover:bg-primary/5",children:"稍后联系"})]})]})]})})},vk=()=>{const{t:e}=Vn(),[t,n]=y.useState(!1);return p.jsxs("section",{id:"home",className:"relative min-h-screen flex items-center justify-center overflow-hidden bg-background",children:[p.jsxs("div",{className:"absolute inset-0",children:[p.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-background via-primary/5 to-accent/5"}),p.jsx("div",{className:"absolute inset-0 opacity-5",children:p.jsx("div",{className:"w-full h-full grid-pattern"})}),p.jsx("div",{className:"absolute top-20 left-20 w-2 h-2 bg-primary rounded-full animate-pulse"}),p.jsx("div",{className:"absolute top-40 right-32 w-1.5 h-1.5 bg-accent rounded-full animate-ping"}),p.jsx("div",{className:"absolute bottom-32 left-32 w-1 h-1 bg-primary rounded-full animate-pulse",style:{animationDelay:"1s"}}),p.jsx("div",{className:"absolute bottom-20 right-20 w-2.5 h-2.5 bg-accent/50 rounded-full animate-pulse",style:{animationDelay:"2s"}})]}),p.jsx("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:p.jsxs("div",{className:"animate-fade-in",children:[p.jsxs("div",{className:"inline-flex items-center px-4 py-2 bg-primary/10 border border-primary/20 rounded-full text-primary text-sm font-medium mb-8 hover:bg-primary/20 transition-all duration-300",children:[p.jsx(uo,{className:"w-4 h-4 mr-2"}),e("hero.badge")]}),p.jsxs("h1",{className:"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight",children:[p.jsx("span",{className:"block text-foreground mb-2",children:e("hero.title")}),p.jsx("span",{className:"block bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent",children:e("hero.subtitle")})]}),p.jsxs("p",{className:"text-lg md:text-xl text-muted-foreground mb-10 max-w-3xl mx-auto leading-relaxed",children:[e("hero.description1"),p.jsx("br",{}),e("hero.description2")]}),p.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[p.jsxs(Ee,{size:"lg",onClick:()=>n(!0),className:"bg-gradient-primary hover:shadow-lg hover:scale-105 transition-all duration-300 text-lg px-8 py-3 group",children:[p.jsx(Jg,{className:"mr-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300"}),e("hero.cta"),p.jsx(Yg,{className:"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300"})]}),p.jsxs(Ee,{variant:"outline",size:"lg",onClick:()=>n(!0),className:"border-2 border-primary/50 hover:border-primary hover:bg-primary/10 hover:scale-105 text-lg px-8 py-3 transition-all duration-300",children:[p.jsx(uo,{className:"mr-2 h-5 w-5"}),e("hero.contact")]})]})]})}),p.jsx(Oi,{isOpen:t,onClose:()=>n(!1)})]})},jp=()=>{const{i18n:e}=Vn(),t=()=>{const n=e.language==="zh"?"en":"zh";e.changeLanguage(n)};return p.jsxs("button",{onClick:t,className:"relative flex items-center space-x-2 px-4 py-2 rounded-lg bg-background/50 border border-primary/20 hover:border-primary/50 backdrop-blur-sm transition-all duration-300 group tech-glow","aria-label":"Switch Language",children:[p.jsx(Gc,{className:"h-4 w-4 text-primary group-hover:rotate-12 transition-transform duration-300"}),p.jsx("span",{className:"text-sm font-medium text-foreground group-hover:text-primary transition-colors duration-300",children:e.language==="zh"?"EN":"中文"}),p.jsx("div",{className:"absolute inset-0 bg-gradient-primary/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]})},yk=()=>{const{t:e}=Vn(),[t,n]=y.useState(!1),[r,o]=y.useState(!1),i=[{name:e("nav.home"),href:"#home"},{name:e("nav.services"),href:"#services"},{name:e("nav.tools"),href:"#tools"}],s=(l,u)=>{(l.key==="Enter"||l.key===" ")&&(l.preventDefault(),u())},a=l=>{l.key==="Escape"&&t&&n(!1)};return p.jsxs(p.Fragment,{children:[p.jsxs("nav",{className:"fixed top-0 w-full z-50 bg-background/90 backdrop-blur-md border-b border-primary/20 tech-glow",children:[p.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-accent/5"}),p.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[p.jsxs("div",{className:"flex justify-between items-center h-16",children:[p.jsxs("div",{className:"flex items-center space-x-3",children:[p.jsxs("picture",{children:[p.jsx("source",{srcSet:"/logo-64.webp",media:"(max-width: 768px)"}),p.jsx("source",{srcSet:"/logo-128.webp",media:"(max-width: 1024px)"}),p.jsx("img",{src:"/logo-128.webp",alt:"Drama Code Logo - 专业定制化技术服务",className:"h-10 w-10 object-contain hover:scale-110 transition-transform duration-300",loading:"eager",width:"40",height:"40",decoding:"async"})]}),p.jsxs("div",{className:"flex flex-col",children:[p.jsx("span",{className:"text-xl font-bold bg-gradient-primary bg-clip-text text-transparent hover:scale-105 transition-transform duration-300 cursor-default",role:"heading","aria-level":1,children:"Drama Code"}),p.jsx("span",{className:"text-xs text-muted-foreground","aria-label":"中文名称",children:"抓马代码"})]})]}),p.jsxs("div",{className:"hidden md:flex items-center space-x-8",children:[i.map(l=>p.jsxs("a",{href:l.href,className:"text-foreground hover:text-primary transition-colors duration-300 relative group",children:[l.name,p.jsx("span",{className:"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-primary transition-all duration-300 group-hover:w-full"})]},l.name)),p.jsx(jp,{}),p.jsxs(Ee,{variant:"default",onClick:()=>o(!0),className:"bg-gradient-primary hover:shadow-glow hover:scale-105 transition-all duration-300 group relative overflow-hidden",children:[p.jsx("div",{className:"absolute inset-0 bg-white/20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"}),p.jsx(di,{className:"w-4 h-4 mr-2 group-hover:rotate-12 transition-transform duration-300"}),e("hero.cta")]})]}),p.jsxs("div",{className:"md:hidden flex items-center space-x-2",children:[p.jsx(jp,{}),p.jsx(Ee,{variant:"ghost",size:"sm",onClick:()=>n(!t),onKeyDown:l=>s(l,()=>n(!t)),className:"text-foreground hover:text-primary","aria-label":t?"关闭导航菜单":"打开导航菜单","aria-expanded":t,"aria-controls":"mobile-menu",children:t?p.jsx(Yc,{className:"h-6 w-6"}):p.jsx(tS,{className:"h-6 w-6"})})]})]}),t&&p.jsx("div",{className:"md:hidden animate-fade-in",id:"mobile-menu",role:"navigation","aria-label":"移动端导航菜单",onKeyDown:a,children:p.jsxs("div",{className:"px-2 pt-2 pb-3 space-y-1 bg-card border border-border rounded-lg mt-2",children:[i.map(l=>p.jsx("a",{href:l.href,className:"block px-3 py-2 text-foreground hover:text-primary hover:bg-muted/50 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",onClick:()=>n(!1),onKeyDown:u=>s(u,()=>n(!1)),tabIndex:0,role:"menuitem",children:l.name},l.name)),p.jsx("div",{className:"px-3 pt-2",children:p.jsx(Ee,{className:"w-full bg-gradient-primary hover:shadow-glow transition-all duration-300",onClick:()=>{o(!0),n(!1)},children:e("hero.cta")})})]})})]})]}),p.jsx(Oi,{isOpen:r,onClose:()=>o(!1)})]})},xk=()=>{const{t:e}=Vn(),t=[nS,Xg,uo,lS,Jg,Vf],n=e("process.steps",{returnObjects:!0});return p.jsxs("section",{className:"relative py-20 bg-background overflow-hidden",children:[p.jsxs("div",{className:"absolute inset-0",children:[p.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-accent/5 to-background"}),p.jsx("div",{className:"absolute inset-0 opacity-10",children:p.jsx("div",{className:"w-full h-full hex-pattern"})}),p.jsx("div",{className:"absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-accent/30 to-transparent"}),p.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary/30 to-transparent"})]}),p.jsxs("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[p.jsxs("div",{className:"text-center mb-16 animate-fade-in",children:[p.jsxs("div",{className:"inline-flex items-center px-6 py-3 bg-accent/10 border-2 border-accent/20 rounded-full text-accent text-sm font-medium mb-6 hover:bg-accent/20 transition-all duration-300 group",children:[p.jsx(Vf,{className:"w-4 h-4 mr-2 group-hover:rotate-12 transition-transform duration-300"}),"专业流程"]}),p.jsx("h2",{className:"text-4xl font-bold mb-4 bg-gradient-to-r from-foreground to-accent bg-clip-text text-transparent",children:e("process.title")}),p.jsx("p",{className:"text-xl text-muted-foreground max-w-3xl mx-auto",children:e("process.subtitle")})]}),p.jsx("div",{className:"max-w-6xl mx-auto",children:p.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:n.map((r,o)=>{const i=t[o];return p.jsxs("div",{className:"group relative bg-card/50 backdrop-blur-sm border border-border hover:border-accent/50 rounded-2xl p-8 transition-all duration-500 hover:shadow-glow hover:scale-105 animate-fade-in tech-glow",style:{animationDelay:`${o*.1}s`},children:[p.jsx("div",{className:"absolute -top-4 -left-4 w-12 h-12 bg-gradient-to-r from-accent to-primary rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg group-hover:animate-pulse",children:o+1}),p.jsx("div",{className:"mb-6 pt-4",children:p.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-accent/20 to-primary/20 rounded-2xl flex items-center justify-center group-hover:bg-gradient-to-r group-hover:from-accent/30 group-hover:to-primary/30 transition-all duration-300",children:p.jsx(i,{className:"h-8 w-8 text-accent group-hover:text-primary transition-colors duration-300 group-hover:scale-110"})})}),p.jsx("h3",{className:"text-xl font-bold text-foreground mb-4 group-hover:text-accent transition-colors duration-300",children:r.title}),p.jsx("p",{className:"text-muted-foreground leading-relaxed group-hover:text-foreground/80 transition-colors duration-300",children:r.description}),o<n.length-1&&p.jsx("div",{className:"hidden lg:block absolute top-1/2 -right-4 w-8 h-px bg-gradient-to-r from-accent/50 to-primary/50 transform -translate-y-1/2 animate-pulse"}),p.jsx("div",{className:"absolute top-4 right-4 w-2 h-2 bg-accent rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-ping transition-opacity duration-300"})]},o)})})}),p.jsx("div",{className:"text-center mt-16 animate-fade-in",style:{animationDelay:"0.6s"},children:p.jsxs("div",{className:"relative bg-card/50 backdrop-blur-sm border border-primary/20 rounded-2xl p-8 max-w-2xl mx-auto tech-glow group hover:border-primary/50 transition-all duration-500",children:[p.jsx("div",{className:"absolute inset-0 bg-gradient-primary/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),p.jsxs("div",{className:"relative z-10",children:[p.jsx("h3",{className:"text-2xl font-bold text-foreground mb-4 group-hover:text-primary transition-colors duration-300",children:"准备开始您的项目了吗？"}),p.jsx("p",{className:"text-muted-foreground mb-6 group-hover:text-foreground/80 transition-colors duration-300",children:"联系我们获取免费咨询和项目报价"}),p.jsxs("button",{className:"relative bg-gradient-primary text-primary-foreground px-8 py-3 rounded-full font-semibold hover:shadow-glow transition-all duration-300 transform hover:scale-105 overflow-hidden group/btn",children:[p.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary to-accent opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"}),p.jsx("span",{className:"relative z-10",children:e("hero.cta")})]})]}),p.jsx("div",{className:"absolute top-4 left-4 w-2 h-2 bg-primary rounded-full animate-pulse"}),p.jsx("div",{className:"absolute bottom-4 right-4 w-1.5 h-1.5 bg-accent rounded-full animate-ping"})]})})]})]})},pd=y.forwardRef(({className:e,...t},n)=>p.jsx("div",{ref:n,className:we("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));pd.displayName="Card";const wk=y.forwardRef(({className:e,...t},n)=>p.jsx("div",{ref:n,className:we("flex flex-col space-y-1.5 p-6",e),...t}));wk.displayName="CardHeader";const Sk=y.forwardRef(({className:e,...t},n)=>p.jsx("h3",{ref:n,className:we("text-2xl font-semibold leading-none tracking-tight",e),...t}));Sk.displayName="CardTitle";const bk=y.forwardRef(({className:e,...t},n)=>p.jsx("p",{ref:n,className:we("text-sm text-muted-foreground",e),...t}));bk.displayName="CardDescription";const hd=y.forwardRef(({className:e,...t},n)=>p.jsx("div",{ref:n,className:we("p-6 pt-0",e),...t}));hd.displayName="CardContent";const Ck=y.forwardRef(({className:e,...t},n)=>p.jsx("div",{ref:n,className:we("flex items-center p-6 pt-0",e),...t}));Ck.displayName="CardFooter";const Ek=({title:e,description:t,features:n,serviceType:r})=>{const[o,i]=y.useState(!1),a=(()=>{switch(r){case"web":return{icon:Gc,gradient:"from-blue-500/20 via-purple-500/20 to-cyan-500/20",iconColor:"text-blue-400",pattern:"web-pattern"};case"script":return{icon:uo,gradient:"from-green-500/20 via-purple-500/20 to-emerald-500/20",iconColor:"text-green-400",pattern:"code-pattern"};case"miniprogram":return{icon:sS,gradient:"from-orange-500/20 via-purple-500/20 to-yellow-500/20",iconColor:"text-orange-400",pattern:"hex-pattern"};case"mobile":return{icon:rS,gradient:"from-red-500/20 via-purple-500/20 to-pink-500/20",iconColor:"text-red-400",pattern:"circle-pattern"};default:return{icon:di,gradient:"from-purple-500/20 to-cyan-500/20",iconColor:"text-purple-400",pattern:"default-pattern"}}})(),l=a.icon;return p.jsxs(p.Fragment,{children:[p.jsx(pd,{className:"group relative overflow-hidden bg-card/50 backdrop-blur-sm border-border hover:border-primary/50 transition-all duration-500 hover:shadow-glow hover:scale-105",children:p.jsxs(hd,{className:"p-0",children:[p.jsxs("div",{className:`relative h-48 overflow-hidden bg-gradient-to-br ${a.gradient}`,children:[p.jsx("div",{className:"absolute inset-0 opacity-10",children:p.jsx("div",{className:`w-full h-full ${a.pattern}`})}),p.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:p.jsx(l,{className:`w-20 h-20 ${a.iconColor} group-hover:scale-110 transition-transform duration-500`})}),p.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-background/80 to-transparent"}),p.jsx("div",{className:"absolute inset-0 bg-gradient-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),p.jsxs("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700",children:[p.jsx("div",{className:"absolute top-4 left-4 w-2 h-2 bg-primary rounded-full animate-pulse"}),p.jsx("div",{className:"absolute top-8 right-8 w-1 h-1 bg-accent rounded-full animate-ping"}),p.jsx("div",{className:"absolute bottom-6 left-8 w-1.5 h-1.5 bg-primary rounded-full animate-bounce"})]})]}),p.jsxs("div",{className:"p-6",children:[p.jsxs("h3",{className:"text-xl font-bold text-foreground mb-3 group-hover:text-primary transition-colors duration-300 flex items-center",children:[e,p.jsx(di,{className:"ml-2 w-5 h-5 opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-all duration-300"})]}),p.jsx("p",{className:"text-muted-foreground mb-4 leading-relaxed group-hover:text-foreground/80 transition-colors duration-300",children:t}),p.jsx("ul",{className:"space-y-2 mb-6",children:n.map((u,d)=>p.jsxs("li",{className:"flex items-center text-sm text-muted-foreground group-hover:text-foreground/70 transition-colors duration-300",children:[p.jsx("div",{className:"w-1.5 h-1.5 bg-primary rounded-full mr-3 flex-shrink-0 group-hover:animate-pulse"}),u]},d))}),p.jsxs(Ee,{onClick:()=>i(!0),variant:"outline",className:"w-full group/btn hover:bg-gradient-primary hover:text-primary-foreground border-primary/30 hover:border-primary transition-all duration-500 relative overflow-hidden",children:[p.jsx("div",{className:"absolute inset-0 bg-gradient-primary transform scale-x-0 group-hover/btn:scale-x-100 transition-transform duration-500 origin-left"}),p.jsxs("span",{className:"relative z-10 flex items-center justify-center",children:["立即咨询",p.jsx(Yg,{className:"ml-2 h-4 w-4 transition-transform duration-300 group-hover/btn:translate-x-1 group-hover/btn:scale-110"})]})]})]})]})}),p.jsx(Oi,{isOpen:o,onClose:()=>i(!1)})]})},kk=()=>{const{t:e}=Vn(),[t,n]=y.useState(!1),r=i=>{const s=e(i,{returnObjects:!0});return Array.isArray(s)?s:[]},o=[{title:e("services.web.title"),description:e("services.web.description"),features:r("services.web.features"),serviceType:"web"},{title:e("services.script.title"),description:e("services.script.description"),features:r("services.script.features"),serviceType:"script"},{title:e("services.miniprogram.title"),description:e("services.miniprogram.description"),features:r("services.miniprogram.features"),serviceType:"miniprogram"},{title:e("services.mobile.title"),description:e("services.mobile.description"),features:r("services.mobile.features"),serviceType:"mobile"}];return p.jsxs("section",{id:"services",className:"relative py-20 bg-background overflow-hidden",children:[p.jsxs("div",{className:"absolute inset-0",children:[p.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background"}),p.jsx("div",{className:"absolute inset-0 opacity-10",children:p.jsx("div",{className:"w-full h-full default-pattern"})}),p.jsx("div",{className:"absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary/30 to-transparent"}),p.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-accent/30 to-transparent"})]}),p.jsxs("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[p.jsxs("div",{className:"text-center mb-16 animate-fade-in",children:[p.jsxs("div",{className:"inline-flex items-center px-6 py-3 bg-primary/10 border-2 border-primary/20 rounded-full text-primary text-sm font-medium mb-6 hover:bg-primary/20 transition-all duration-300 group",children:[p.jsx(Gc,{className:"w-4 h-4 mr-2 group-hover:rotate-12 transition-transform duration-300"}),e("services.badge"),p.jsx(eS,{className:"w-4 h-4 ml-2 text-red-400 animate-pulse"})]}),p.jsxs("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:[e("services.title")," ",p.jsx("span",{className:"bg-gradient-primary bg-clip-text text-transparent hover:animate-glow transition-all duration-300",children:e("services.titleHighlight")})]}),p.jsxs("p",{className:"text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed",children:[p.jsx("span",{className:"inline-block hover:text-primary transition-colors duration-300",children:e("services.description1")}),p.jsx("br",{}),p.jsx("span",{className:"inline-block hover:text-accent transition-colors duration-300",children:e("services.description2")})]})]}),p.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8",children:o.map((i,s)=>p.jsx("div",{className:"animate-fade-in",style:{animationDelay:`${s*.2}s`},children:p.jsx(Ek,{...i})},i.title))})]}),p.jsx(Oi,{isOpen:t,onClose:()=>n(!1)})]})},Nk=Qc("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function Pk({className:e,variant:t,...n}){return p.jsx("div",{className:we(Nk({variant:t}),e),...n})}const Tk=()=>{const{t:e}=Vn(),[t,n]=y.useState("all"),[r,o]=y.useState(!1),i=[{id:"all",name:e("tools.categories.all"),icon:p.jsx(Hf,{className:"w-4 h-4"})},{id:"development",name:e("tools.categories.development"),icon:p.jsx(uo,{className:"w-4 h-4"})},{id:"data",name:e("tools.categories.data"),icon:p.jsx(Uf,{className:"w-4 h-4"})},{id:"automation",name:e("tools.categories.automation"),icon:p.jsx($f,{className:"w-4 h-4"})},{id:"utilities",name:e("tools.categories.utilities"),icon:p.jsx(di,{className:"w-4 h-4"})}],s=[{id:"api-tester",name:"API接口测试器",description:"强大的API接口测试工具，支持多种请求方式，自动生成测试报告",category:"development",icon:p.jsx(uo,{className:"w-6 h-6"}),features:["RESTful API测试","自动化测试脚本","性能监控","报告生成"],tags:["API","测试","自动化"],popularity:95},{id:"data-processor",name:"数据处理工具",description:"高效的数据清洗、转换和分析工具，支持多种数据格式",category:"data",icon:p.jsx(Uf,{className:"w-6 h-6"}),features:["数据清洗","格式转换","批量处理","可视化分析"],tags:["数据","分析","清洗"],popularity:88},{id:"automation-suite",name:"自动化工具套件",description:"全面的自动化解决方案，包含任务调度、监控和报警功能",category:"automation",icon:p.jsx($f,{className:"w-6 h-6"}),features:["任务调度","系统监控","自动报警","日志分析"],tags:["自动化","监控","调度"],popularity:92},{id:"security-scanner",name:"安全扫描器",description:"专业的安全漏洞扫描工具，帮助识别和修复安全问题",category:"utilities",icon:p.jsx(iS,{className:"w-6 h-6"}),features:["漏洞扫描","安全评估","合规检查","修复建议"],tags:["安全","扫描","漏洞"],popularity:90},{id:"performance-monitor",name:"性能监控工具",description:"实时监控系统性能，提供详细的性能分析和优化建议",category:"utilities",icon:p.jsx(Zw,{className:"w-6 h-6"}),features:["实时监控","性能分析","资源优化","告警通知"],tags:["性能","监控","优化"],popularity:87},{id:"report-generator",name:"报告生成器",description:"智能报告生成工具，支持多种模板和自定义格式",category:"utilities",icon:p.jsx(Xg,{className:"w-6 h-6"}),features:["模板定制","数据可视化","自动生成","多格式导出"],tags:["报告","模板","导出"],popularity:85}],a=t==="all"?s:s.filter(l=>l.category===t);return p.jsxs("section",{id:"tools",className:"relative py-20 bg-background overflow-hidden",children:[p.jsxs("div",{className:"absolute inset-0",children:[p.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background"}),p.jsx("div",{className:"absolute inset-0 opacity-10",children:p.jsx("div",{className:"w-full h-full code-pattern"})}),p.jsx("div",{className:"absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary/30 to-transparent"}),p.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-accent/30 to-transparent"})]}),p.jsxs("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[p.jsxs("div",{className:"text-center mb-16 animate-fade-in",children:[p.jsxs("div",{className:"inline-flex items-center px-6 py-3 bg-accent/10 border-2 border-accent/20 rounded-full text-accent text-sm font-medium mb-6 hover:bg-accent/20 transition-all duration-300 group",children:[p.jsx(Hf,{className:"w-4 h-4 mr-2 group-hover:rotate-12 transition-transform duration-300"}),e("tools.badge"),p.jsx(di,{className:"w-4 h-4 ml-2 text-yellow-400 animate-pulse"})]}),p.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-foreground mb-4",children:e("tools.title")}),p.jsx("p",{className:"text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed",children:e("tools.subtitle")})]}),p.jsx("div",{className:"flex flex-wrap justify-center gap-4 mb-12",children:i.map(l=>p.jsxs(Ee,{variant:t===l.id?"default":"outline",onClick:()=>n(l.id),className:"flex items-center gap-2 px-6 py-3 rounded-full transition-all duration-300 hover:scale-105",children:[l.icon,l.name]},l.id))}),p.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:a.map((l,u)=>p.jsx("div",{className:"animate-fade-in",style:{animationDelay:`${u*.1}s`},children:p.jsx(pd,{className:"h-full hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group border-2 hover:border-primary/20",children:p.jsxs(hd,{className:"p-6",children:[p.jsx("div",{className:"flex items-start justify-between mb-4",children:p.jsxs("div",{className:"flex items-center gap-3",children:[p.jsx("div",{className:"p-3 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors duration-300",children:l.icon}),p.jsxs("div",{children:[p.jsx("h3",{className:"font-semibold text-lg text-foreground group-hover:text-primary transition-colors duration-300",children:l.name}),p.jsx("div",{className:"flex items-center gap-2 mt-1",children:p.jsxs("div",{className:"flex items-center gap-1",children:[p.jsx(aS,{className:"w-4 h-4 text-yellow-400 fill-current"}),p.jsxs("span",{className:"text-sm text-muted-foreground",children:[l.popularity,"%"]})]})})]})]})}),p.jsx("p",{className:"text-muted-foreground mb-4 leading-relaxed",children:l.description}),p.jsxs("div",{className:"space-y-3 mb-4",children:[p.jsx("h4",{className:"font-medium text-sm text-foreground",children:"主要功能："}),p.jsx("ul",{className:"space-y-1",children:l.features.slice(0,3).map((d,f)=>p.jsxs("li",{className:"text-sm text-muted-foreground flex items-center gap-2",children:[p.jsx("div",{className:"w-1.5 h-1.5 bg-primary rounded-full"}),d]},f))})]}),p.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:l.tags.map(d=>p.jsx(Pk,{variant:"secondary",className:"text-xs",children:d},d))}),p.jsxs("div",{className:"flex gap-2",children:[p.jsxs(Ee,{size:"sm",className:"flex-1",onClick:()=>o(!0),children:[p.jsx(Jw,{className:"w-4 h-4 mr-2"}),"获取工具"]}),p.jsx(Ee,{size:"sm",variant:"outline",children:p.jsx(qg,{className:"w-4 h-4"})})]})]})})},l.id))})]}),p.jsx(Oi,{isOpen:r,onClose:()=>o(!1)})]})},Rk=()=>p.jsx("a",{href:"#main-content",className:"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md z-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",children:"跳转到主内容"}),jk=()=>{const{t:e}=Vn();return p.jsxs("div",{className:"min-h-screen bg-background",children:[p.jsx(Rk,{}),p.jsx(yk,{}),p.jsxs("main",{id:"main-content",role:"main","aria-label":"主要内容",children:[p.jsx(vk,{}),p.jsx(kk,{}),p.jsx(xk,{}),p.jsx(Tk,{})]}),p.jsxs("footer",{className:"relative bg-background border-t border-primary/20 py-12 overflow-hidden",children:[p.jsxs("div",{className:"absolute inset-0",children:[p.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-primary/5 to-transparent"}),p.jsx("div",{className:"absolute inset-0 opacity-10",children:p.jsx("div",{className:"w-full h-full default-pattern"})}),p.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary/50 to-transparent"})]}),p.jsx("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:p.jsxs("div",{className:"text-center",children:[p.jsxs("div",{className:"mb-6",children:[p.jsx("h3",{className:"text-2xl font-bold bg-gradient-primary bg-clip-text text-transparent",children:"Drama Code"}),p.jsx("div",{className:"w-20 h-px bg-gradient-primary mx-auto mt-2"})]}),p.jsx("p",{className:"text-muted-foreground mb-2 animate-fade-in",children:e("footer.copyright")}),p.jsx("p",{className:"text-sm text-muted-foreground/80 animate-fade-in",style:{animationDelay:"0.2s"},children:e("footer.description")}),p.jsxs("div",{className:"flex justify-center items-center mt-6 space-x-4",children:[p.jsx("div",{className:"w-2 h-2 bg-primary rounded-full animate-pulse"}),p.jsx("div",{className:"w-1 h-1 bg-accent rounded-full animate-ping"}),p.jsx("div",{className:"w-2 h-2 bg-primary rounded-full animate-pulse",style:{animationDelay:"1s"}})]})]})})]})]})},Ok=()=>{const e=ji();return Vn(),y.useEffect(()=>{console.error("404 Error: User attempted to access non-existent route:",e.pathname),typeof window<"u"&&window.gtag&&window.gtag("event","page_not_found",{page_path:e.pathname,page_title:"404 - Page Not Found"})},[e.pathname]),p.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:p.jsxs("div",{className:"max-w-md w-full text-center space-y-8",children:[p.jsxs("div",{className:"space-y-4",children:[p.jsx("div",{className:"text-8xl font-bold bg-gradient-primary bg-clip-text text-transparent animate-pulse",children:"404"}),p.jsx("div",{className:"w-24 h-1 bg-gradient-primary mx-auto rounded-full"})]}),p.jsxs("div",{className:"space-y-3",children:[p.jsx("h1",{className:"text-2xl font-bold text-foreground",children:"页面未找到"}),p.jsxs("p",{className:"text-muted-foreground leading-relaxed",children:["抱歉，您访问的页面不存在或已被移动。",p.jsx("br",{}),"请检查URL是否正确，或返回首页继续浏览。"]}),p.jsxs("div",{className:"bg-muted/50 p-3 rounded-lg border text-sm",children:[p.jsx("span",{className:"text-muted-foreground",children:"尝试访问："}),p.jsx("code",{className:"text-primary font-mono ml-2",children:e.pathname})]})]}),p.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[p.jsx(Ee,{asChild:!0,className:"flex items-center gap-2 bg-gradient-primary hover:shadow-glow",children:p.jsxs(kl,{to:"/",children:[p.jsx(Zg,{className:"w-4 h-4"}),"返回首页"]})}),p.jsxs(Ee,{variant:"outline",onClick:()=>window.history.back(),className:"flex items-center gap-2",children:[p.jsx(Xw,{className:"w-4 h-4"}),"返回上页"]})]}),p.jsxs("div",{className:"pt-4 border-t border-border",children:[p.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:"或者您可以："}),p.jsxs("div",{className:"flex flex-col gap-2 text-sm",children:[p.jsx(kl,{to:"/#services",className:"text-primary hover:text-primary/80 transition-colors",children:"• 查看我们的服务"}),p.jsx(kl,{to:"/#tools",className:"text-primary hover:text-primary/80 transition-colors",children:"• 浏览技术工具"}),p.jsx("a",{href:"mailto:<EMAIL>",className:"text-primary hover:text-primary/80 transition-colors",children:"• 联系我们获取帮助"})]})]})]})})},Lk=new V2({defaultOptions:{queries:{retry:3,staleTime:5*60*1e3,gcTime:10*60*1e3},mutations:{retry:1}}}),Ak=()=>p.jsx(vp,{children:p.jsx(W2,{client:Lk,children:p.jsxs(w2,{children:[p.jsx(WS,{}),p.jsx(Z1,{}),p.jsx(UC,{children:p.jsx(vp,{children:p.jsxs(MC,{children:[p.jsx(zu,{path:"/",element:p.jsx(jk,{})}),p.jsx(zu,{path:"*",element:p.jsx(Ok,{})})]})})})]})})}),H=e=>typeof e=="string",Lo=()=>{let e,t;const n=new Promise((r,o)=>{e=r,t=o});return n.resolve=e,n.reject=t,n},Op=e=>e==null?"":""+e,Mk=(e,t,n)=>{e.forEach(r=>{t[r]&&(n[r]=t[r])})},Dk=/###/g,Lp=e=>e&&e.indexOf("###")>-1?e.replace(Dk,"."):e,Ap=e=>!e||H(e),Qo=(e,t,n)=>{const r=H(t)?t.split("."):t;let o=0;for(;o<r.length-1;){if(Ap(e))return{};const i=Lp(r[o]);!e[i]&&n&&(e[i]=new n),Object.prototype.hasOwnProperty.call(e,i)?e=e[i]:e={},++o}return Ap(e)?{}:{obj:e,k:Lp(r[o])}},Mp=(e,t,n)=>{const{obj:r,k:o}=Qo(e,t,Object);if(r!==void 0||t.length===1){r[o]=n;return}let i=t[t.length-1],s=t.slice(0,t.length-1),a=Qo(e,s,Object);for(;a.obj===void 0&&s.length;)i=`${s[s.length-1]}.${i}`,s=s.slice(0,s.length-1),a=Qo(e,s,Object),a!=null&&a.obj&&typeof a.obj[`${a.k}.${i}`]<"u"&&(a.obj=void 0);a.obj[`${a.k}.${i}`]=n},_k=(e,t,n,r)=>{const{obj:o,k:i}=Qo(e,t,Object);o[i]=o[i]||[],o[i].push(n)},ia=(e,t)=>{const{obj:n,k:r}=Qo(e,t);if(n&&Object.prototype.hasOwnProperty.call(n,r))return n[r]},Ik=(e,t,n)=>{const r=ia(e,n);return r!==void 0?r:ia(t,n)},Fy=(e,t,n)=>{for(const r in t)r!=="__proto__"&&r!=="constructor"&&(r in e?H(e[r])||e[r]instanceof String||H(t[r])||t[r]instanceof String?n&&(e[r]=t[r]):Fy(e[r],t[r],n):e[r]=t[r]);return e},br=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var Fk={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const $k=e=>H(e)?e.replace(/[&<>"'\/]/g,t=>Fk[t]):e;class zk{constructor(t){this.capacity=t,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(t){const n=this.regExpMap.get(t);if(n!==void 0)return n;const r=new RegExp(t);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(t,r),this.regExpQueue.push(t),r}}const Uk=[" ",",","?","!",";"],Bk=new zk(20),Vk=(e,t,n)=>{t=t||"",n=n||"";const r=Uk.filter(s=>t.indexOf(s)<0&&n.indexOf(s)<0);if(r.length===0)return!0;const o=Bk.getRegExp(`(${r.map(s=>s==="?"?"\\?":s).join("|")})`);let i=!o.test(e);if(!i){const s=e.indexOf(n);s>0&&!o.test(e.substring(0,s))&&(i=!0)}return i},Qu=(e,t,n=".")=>{if(!e)return;if(e[t])return Object.prototype.hasOwnProperty.call(e,t)?e[t]:void 0;const r=t.split(n);let o=e;for(let i=0;i<r.length;){if(!o||typeof o!="object")return;let s,a="";for(let l=i;l<r.length;++l)if(l!==i&&(a+=n),a+=r[l],s=o[a],s!==void 0){if(["string","number","boolean"].indexOf(typeof s)>-1&&l<r.length-1)continue;i+=l-i+1;break}o=s}return o},yi=e=>e==null?void 0:e.replace("_","-"),Hk={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){var n,r;(r=(n=console==null?void 0:console[e])==null?void 0:n.apply)==null||r.call(n,console,t)}};class sa{constructor(t,n={}){this.init(t,n)}init(t,n={}){this.prefix=n.prefix||"i18next:",this.logger=t||Hk,this.options=n,this.debug=n.debug}log(...t){return this.forward(t,"log","",!0)}warn(...t){return this.forward(t,"warn","",!0)}error(...t){return this.forward(t,"error","")}deprecate(...t){return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(t,n,r,o){return o&&!this.debug?null:(H(t[0])&&(t[0]=`${r}${this.prefix} ${t[0]}`),this.logger[n](t))}create(t){return new sa(this.logger,{prefix:`${this.prefix}:${t}:`,...this.options})}clone(t){return t=t||this.options,t.prefix=t.prefix||this.prefix,new sa(this.logger,t)}}var _t=new sa;class Ia{constructor(){this.observers={}}on(t,n){return t.split(" ").forEach(r=>{this.observers[r]||(this.observers[r]=new Map);const o=this.observers[r].get(n)||0;this.observers[r].set(n,o+1)}),this}off(t,n){if(this.observers[t]){if(!n){delete this.observers[t];return}this.observers[t].delete(n)}}emit(t,...n){this.observers[t]&&Array.from(this.observers[t].entries()).forEach(([o,i])=>{for(let s=0;s<i;s++)o(...n)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([o,i])=>{for(let s=0;s<i;s++)o.apply(o,[t,...n])})}}class Dp extends Ia{constructor(t,n={ns:["translation"],defaultNS:"translation"}){super(),this.data=t||{},this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(t){this.options.ns.indexOf(t)<0&&this.options.ns.push(t)}removeNamespaces(t){const n=this.options.ns.indexOf(t);n>-1&&this.options.ns.splice(n,1)}getResource(t,n,r,o={}){var u,d;const i=o.keySeparator!==void 0?o.keySeparator:this.options.keySeparator,s=o.ignoreJSONStructure!==void 0?o.ignoreJSONStructure:this.options.ignoreJSONStructure;let a;t.indexOf(".")>-1?a=t.split("."):(a=[t,n],r&&(Array.isArray(r)?a.push(...r):H(r)&&i?a.push(...r.split(i)):a.push(r)));const l=ia(this.data,a);return!l&&!n&&!r&&t.indexOf(".")>-1&&(t=a[0],n=a[1],r=a.slice(2).join(".")),l||!s||!H(r)?l:Qu((d=(u=this.data)==null?void 0:u[t])==null?void 0:d[n],r,i)}addResource(t,n,r,o,i={silent:!1}){const s=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator;let a=[t,n];r&&(a=a.concat(s?r.split(s):r)),t.indexOf(".")>-1&&(a=t.split("."),o=n,n=a[1]),this.addNamespaces(n),Mp(this.data,a,o),i.silent||this.emit("added",t,n,r,o)}addResources(t,n,r,o={silent:!1}){for(const i in r)(H(r[i])||Array.isArray(r[i]))&&this.addResource(t,n,i,r[i],{silent:!0});o.silent||this.emit("added",t,n,r)}addResourceBundle(t,n,r,o,i,s={silent:!1,skipCopy:!1}){let a=[t,n];t.indexOf(".")>-1&&(a=t.split("."),o=r,r=n,n=a[1]),this.addNamespaces(n);let l=ia(this.data,a)||{};s.skipCopy||(r=JSON.parse(JSON.stringify(r))),o?Fy(l,r,i):l={...l,...r},Mp(this.data,a,l),s.silent||this.emit("added",t,n,r)}removeResourceBundle(t,n){this.hasResourceBundle(t,n)&&delete this.data[t][n],this.removeNamespaces(n),this.emit("removed",t,n)}hasResourceBundle(t,n){return this.getResource(t,n)!==void 0}getResourceBundle(t,n){return n||(n=this.options.defaultNS),this.getResource(t,n)}getDataByLanguage(t){return this.data[t]}hasLanguageSomeTranslations(t){const n=this.getDataByLanguage(t);return!!(n&&Object.keys(n)||[]).find(o=>n[o]&&Object.keys(n[o]).length>0)}toJSON(){return this.data}}var $y={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,n,r,o){return e.forEach(i=>{var s;t=((s=this.processors[i])==null?void 0:s.process(t,n,r,o))??t}),t}};const _p={},Ip=e=>!H(e)&&typeof e!="boolean"&&typeof e!="number";class aa extends Ia{constructor(t,n={}){super(),Mk(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],t,this),this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=_t.create("translator")}changeLanguage(t){t&&(this.language=t)}exists(t,n={interpolation:{}}){const r={...n};if(t==null)return!1;const o=this.resolve(t,r);return(o==null?void 0:o.res)!==void 0}extractFromKey(t,n){let r=n.nsSeparator!==void 0?n.nsSeparator:this.options.nsSeparator;r===void 0&&(r=":");const o=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator;let i=n.ns||this.options.defaultNS||[];const s=r&&t.indexOf(r)>-1,a=!this.options.userDefinedKeySeparator&&!n.keySeparator&&!this.options.userDefinedNsSeparator&&!n.nsSeparator&&!Vk(t,r,o);if(s&&!a){const l=t.match(this.interpolator.nestingRegexp);if(l&&l.length>0)return{key:t,namespaces:H(i)?[i]:i};const u=t.split(r);(r!==o||r===o&&this.options.ns.indexOf(u[0])>-1)&&(i=u.shift()),t=u.join(o)}return{key:t,namespaces:H(i)?[i]:i}}translate(t,n,r){let o=typeof n=="object"?{...n}:n;if(typeof o!="object"&&this.options.overloadTranslationOptionHandler&&(o=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(o={...o}),o||(o={}),t==null)return"";Array.isArray(t)||(t=[String(t)]);const i=o.returnDetails!==void 0?o.returnDetails:this.options.returnDetails,s=o.keySeparator!==void 0?o.keySeparator:this.options.keySeparator,{key:a,namespaces:l}=this.extractFromKey(t[t.length-1],o),u=l[l.length-1];let d=o.nsSeparator!==void 0?o.nsSeparator:this.options.nsSeparator;d===void 0&&(d=":");const f=o.lng||this.language,c=o.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((f==null?void 0:f.toLowerCase())==="cimode")return c?i?{res:`${u}${d}${a}`,usedKey:a,exactUsedKey:a,usedLng:f,usedNS:u,usedParams:this.getUsedParamsDetails(o)}:`${u}${d}${a}`:i?{res:a,usedKey:a,exactUsedKey:a,usedLng:f,usedNS:u,usedParams:this.getUsedParamsDetails(o)}:a;const v=this.resolve(t,o);let x=v==null?void 0:v.res;const h=(v==null?void 0:v.usedKey)||a,S=(v==null?void 0:v.exactUsedKey)||a,g=["[object Number]","[object Function]","[object RegExp]"],m=o.joinArrays!==void 0?o.joinArrays:this.options.joinArrays,w=!this.i18nFormat||this.i18nFormat.handleAsObject,b=o.count!==void 0&&!H(o.count),C=aa.hasDefaultValue(o),k=b?this.pluralResolver.getSuffix(f,o.count,o):"",N=o.ordinal&&b?this.pluralResolver.getSuffix(f,o.count,{ordinal:!1}):"",P=b&&!o.ordinal&&o.count===0,O=P&&o[`defaultValue${this.options.pluralSeparator}zero`]||o[`defaultValue${k}`]||o[`defaultValue${N}`]||o.defaultValue;let R=x;w&&!x&&C&&(R=O);const $=Ip(R),I=Object.prototype.toString.apply(R);if(w&&R&&$&&g.indexOf(I)<0&&!(H(m)&&Array.isArray(R))){if(!o.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const B=this.options.returnedObjectHandler?this.options.returnedObjectHandler(h,R,{...o,ns:l}):`key '${a} (${this.language})' returned an object instead of string.`;return i?(v.res=B,v.usedParams=this.getUsedParamsDetails(o),v):B}if(s){const B=Array.isArray(R),A=B?[]:{},G=B?S:h;for(const z in R)if(Object.prototype.hasOwnProperty.call(R,z)){const V=`${G}${s}${z}`;C&&!x?A[z]=this.translate(V,{...o,defaultValue:Ip(O)?O[z]:void 0,joinArrays:!1,ns:l}):A[z]=this.translate(V,{...o,joinArrays:!1,ns:l}),A[z]===V&&(A[z]=R[z])}x=A}}else if(w&&H(m)&&Array.isArray(x))x=x.join(m),x&&(x=this.extendTranslation(x,t,o,r));else{let B=!1,A=!1;!this.isValidLookup(x)&&C&&(B=!0,x=O),this.isValidLookup(x)||(A=!0,x=a);const z=(o.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&A?void 0:x,V=C&&O!==x&&this.options.updateMissing;if(A||B||V){if(this.logger.log(V?"updateKey":"missingKey",f,u,a,V?O:x),s){const D=this.resolve(a,{...o,keySeparator:!1});D&&D.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let E=[];const L=this.languageUtils.getFallbackCodes(this.options.fallbackLng,o.lng||this.language);if(this.options.saveMissingTo==="fallback"&&L&&L[0])for(let D=0;D<L.length;D++)E.push(L[D]);else this.options.saveMissingTo==="all"?E=this.languageUtils.toResolveHierarchy(o.lng||this.language):E.push(o.lng||this.language);const F=(D,U,W)=>{var je;const ne=C&&W!==x?W:z;this.options.missingKeyHandler?this.options.missingKeyHandler(D,u,U,ne,V,o):(je=this.backendConnector)!=null&&je.saveMissing&&this.backendConnector.saveMissing(D,u,U,ne,V,o),this.emit("missingKey",D,u,U,x)};this.options.saveMissing&&(this.options.saveMissingPlurals&&b?E.forEach(D=>{const U=this.pluralResolver.getSuffixes(D,o);P&&o[`defaultValue${this.options.pluralSeparator}zero`]&&U.indexOf(`${this.options.pluralSeparator}zero`)<0&&U.push(`${this.options.pluralSeparator}zero`),U.forEach(W=>{F([D],a+W,o[`defaultValue${W}`]||O)})}):F(E,a,O))}x=this.extendTranslation(x,t,o,v,r),A&&x===a&&this.options.appendNamespaceToMissingKey&&(x=`${u}${d}${a}`),(A||B)&&this.options.parseMissingKeyHandler&&(x=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${u}${d}${a}`:a,B?x:void 0,o))}return i?(v.res=x,v.usedParams=this.getUsedParamsDetails(o),v):x}extendTranslation(t,n,r,o,i){var l,u;if((l=this.i18nFormat)!=null&&l.parse)t=this.i18nFormat.parse(t,{...this.options.interpolation.defaultVariables,...r},r.lng||this.language||o.usedLng,o.usedNS,o.usedKey,{resolved:o});else if(!r.skipInterpolation){r.interpolation&&this.interpolator.init({...r,interpolation:{...this.options.interpolation,...r.interpolation}});const d=H(t)&&(((u=r==null?void 0:r.interpolation)==null?void 0:u.skipOnVariables)!==void 0?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let f;if(d){const v=t.match(this.interpolator.nestingRegexp);f=v&&v.length}let c=r.replace&&!H(r.replace)?r.replace:r;if(this.options.interpolation.defaultVariables&&(c={...this.options.interpolation.defaultVariables,...c}),t=this.interpolator.interpolate(t,c,r.lng||this.language||o.usedLng,r),d){const v=t.match(this.interpolator.nestingRegexp),x=v&&v.length;f<x&&(r.nest=!1)}!r.lng&&o&&o.res&&(r.lng=this.language||o.usedLng),r.nest!==!1&&(t=this.interpolator.nest(t,(...v)=>(i==null?void 0:i[0])===v[0]&&!r.context?(this.logger.warn(`It seems you are nesting recursively key: ${v[0]} in key: ${n[0]}`),null):this.translate(...v,n),r)),r.interpolation&&this.interpolator.reset()}const s=r.postProcess||this.options.postProcess,a=H(s)?[s]:s;return t!=null&&(a!=null&&a.length)&&r.applyPostProcessor!==!1&&(t=$y.handle(a,t,n,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...o,usedParams:this.getUsedParamsDetails(r)},...r}:r,this)),t}resolve(t,n={}){let r,o,i,s,a;return H(t)&&(t=[t]),t.forEach(l=>{if(this.isValidLookup(r))return;const u=this.extractFromKey(l,n),d=u.key;o=d;let f=u.namespaces;this.options.fallbackNS&&(f=f.concat(this.options.fallbackNS));const c=n.count!==void 0&&!H(n.count),v=c&&!n.ordinal&&n.count===0,x=n.context!==void 0&&(H(n.context)||typeof n.context=="number")&&n.context!=="",h=n.lngs?n.lngs:this.languageUtils.toResolveHierarchy(n.lng||this.language,n.fallbackLng);f.forEach(S=>{var g,m;this.isValidLookup(r)||(a=S,!_p[`${h[0]}-${S}`]&&((g=this.utils)!=null&&g.hasLoadedNamespace)&&!((m=this.utils)!=null&&m.hasLoadedNamespace(a))&&(_p[`${h[0]}-${S}`]=!0,this.logger.warn(`key "${o}" for languages "${h.join(", ")}" won't get resolved as namespace "${a}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),h.forEach(w=>{var k;if(this.isValidLookup(r))return;s=w;const b=[d];if((k=this.i18nFormat)!=null&&k.addLookupKeys)this.i18nFormat.addLookupKeys(b,d,w,S,n);else{let N;c&&(N=this.pluralResolver.getSuffix(w,n.count,n));const P=`${this.options.pluralSeparator}zero`,O=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(c&&(b.push(d+N),n.ordinal&&N.indexOf(O)===0&&b.push(d+N.replace(O,this.options.pluralSeparator)),v&&b.push(d+P)),x){const R=`${d}${this.options.contextSeparator}${n.context}`;b.push(R),c&&(b.push(R+N),n.ordinal&&N.indexOf(O)===0&&b.push(R+N.replace(O,this.options.pluralSeparator)),v&&b.push(R+P))}}let C;for(;C=b.pop();)this.isValidLookup(r)||(i=C,r=this.getResource(w,S,C,n))}))})}),{res:r,usedKey:o,exactUsedKey:i,usedLng:s,usedNS:a}}isValidLookup(t){return t!==void 0&&!(!this.options.returnNull&&t===null)&&!(!this.options.returnEmptyString&&t==="")}getResource(t,n,r,o={}){var i;return(i=this.i18nFormat)!=null&&i.getResource?this.i18nFormat.getResource(t,n,r,o):this.resourceStore.getResource(t,n,r,o)}getUsedParamsDetails(t={}){const n=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],r=t.replace&&!H(t.replace);let o=r?t.replace:t;if(r&&typeof t.count<"u"&&(o.count=t.count),this.options.interpolation.defaultVariables&&(o={...this.options.interpolation.defaultVariables,...o}),!r){o={...o};for(const i of n)delete o[i]}return o}static hasDefaultValue(t){const n="defaultValue";for(const r in t)if(Object.prototype.hasOwnProperty.call(t,r)&&n===r.substring(0,n.length)&&t[r]!==void 0)return!0;return!1}}class Fp{constructor(t){this.options=t,this.supportedLngs=this.options.supportedLngs||!1,this.logger=_t.create("languageUtils")}getScriptPartFromCode(t){if(t=yi(t),!t||t.indexOf("-")<0)return null;const n=t.split("-");return n.length===2||(n.pop(),n[n.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(n.join("-"))}getLanguagePartFromCode(t){if(t=yi(t),!t||t.indexOf("-")<0)return t;const n=t.split("-");return this.formatLanguageCode(n[0])}formatLanguageCode(t){if(H(t)&&t.indexOf("-")>-1){let n;try{n=Intl.getCanonicalLocales(t)[0]}catch{}return n&&this.options.lowerCaseLng&&(n=n.toLowerCase()),n||(this.options.lowerCaseLng?t.toLowerCase():t)}return this.options.cleanCode||this.options.lowerCaseLng?t.toLowerCase():t}isSupportedCode(t){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(t=this.getLanguagePartFromCode(t)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(t)>-1}getBestMatchFromCodes(t){if(!t)return null;let n;return t.forEach(r=>{if(n)return;const o=this.formatLanguageCode(r);(!this.options.supportedLngs||this.isSupportedCode(o))&&(n=o)}),!n&&this.options.supportedLngs&&t.forEach(r=>{if(n)return;const o=this.getScriptPartFromCode(r);if(this.isSupportedCode(o))return n=o;const i=this.getLanguagePartFromCode(r);if(this.isSupportedCode(i))return n=i;n=this.options.supportedLngs.find(s=>{if(s===i)return s;if(!(s.indexOf("-")<0&&i.indexOf("-")<0)&&(s.indexOf("-")>0&&i.indexOf("-")<0&&s.substring(0,s.indexOf("-"))===i||s.indexOf(i)===0&&i.length>1))return s})}),n||(n=this.getFallbackCodes(this.options.fallbackLng)[0]),n}getFallbackCodes(t,n){if(!t)return[];if(typeof t=="function"&&(t=t(n)),H(t)&&(t=[t]),Array.isArray(t))return t;if(!n)return t.default||[];let r=t[n];return r||(r=t[this.getScriptPartFromCode(n)]),r||(r=t[this.formatLanguageCode(n)]),r||(r=t[this.getLanguagePartFromCode(n)]),r||(r=t.default),r||[]}toResolveHierarchy(t,n){const r=this.getFallbackCodes((n===!1?[]:n)||this.options.fallbackLng||[],t),o=[],i=s=>{s&&(this.isSupportedCode(s)?o.push(s):this.logger.warn(`rejecting language code not found in supportedLngs: ${s}`))};return H(t)&&(t.indexOf("-")>-1||t.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&i(this.formatLanguageCode(t)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&i(this.getScriptPartFromCode(t)),this.options.load!=="currentOnly"&&i(this.getLanguagePartFromCode(t))):H(t)&&i(this.formatLanguageCode(t)),r.forEach(s=>{o.indexOf(s)<0&&i(this.formatLanguageCode(s))}),o}}const $p={zero:0,one:1,two:2,few:3,many:4,other:5},zp={select:e=>e===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class Wk{constructor(t,n={}){this.languageUtils=t,this.options=n,this.logger=_t.create("pluralResolver"),this.pluralRulesCache={}}addRule(t,n){this.rules[t]=n}clearCache(){this.pluralRulesCache={}}getRule(t,n={}){const r=yi(t==="dev"?"en":t),o=n.ordinal?"ordinal":"cardinal",i=JSON.stringify({cleanedCode:r,type:o});if(i in this.pluralRulesCache)return this.pluralRulesCache[i];let s;try{s=new Intl.PluralRules(r,{type:o})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),zp;if(!t.match(/-|_/))return zp;const l=this.languageUtils.getLanguagePartFromCode(t);s=this.getRule(l,n)}return this.pluralRulesCache[i]=s,s}needsPlural(t,n={}){let r=this.getRule(t,n);return r||(r=this.getRule("dev",n)),(r==null?void 0:r.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(t,n,r={}){return this.getSuffixes(t,r).map(o=>`${n}${o}`)}getSuffixes(t,n={}){let r=this.getRule(t,n);return r||(r=this.getRule("dev",n)),r?r.resolvedOptions().pluralCategories.sort((o,i)=>$p[o]-$p[i]).map(o=>`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${o}`):[]}getSuffix(t,n,r={}){const o=this.getRule(t,r);return o?`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${o.select(n)}`:(this.logger.warn(`no plural rule found for: ${t}`),this.getSuffix("dev",n,r))}}const Up=(e,t,n,r=".",o=!0)=>{let i=Ik(e,t,n);return!i&&o&&H(n)&&(i=Qu(e,n,r),i===void 0&&(i=Qu(t,n,r))),i},Al=e=>e.replace(/\$/g,"$$$$");class Kk{constructor(t={}){var n;this.logger=_t.create("interpolator"),this.options=t,this.format=((n=t==null?void 0:t.interpolation)==null?void 0:n.format)||(r=>r),this.init(t)}init(t={}){t.interpolation||(t.interpolation={escapeValue:!0});const{escape:n,escapeValue:r,useRawValueToEscape:o,prefix:i,prefixEscaped:s,suffix:a,suffixEscaped:l,formatSeparator:u,unescapeSuffix:d,unescapePrefix:f,nestingPrefix:c,nestingPrefixEscaped:v,nestingSuffix:x,nestingSuffixEscaped:h,nestingOptionsSeparator:S,maxReplaces:g,alwaysFormat:m}=t.interpolation;this.escape=n!==void 0?n:$k,this.escapeValue=r!==void 0?r:!0,this.useRawValueToEscape=o!==void 0?o:!1,this.prefix=i?br(i):s||"{{",this.suffix=a?br(a):l||"}}",this.formatSeparator=u||",",this.unescapePrefix=d?"":f||"-",this.unescapeSuffix=this.unescapePrefix?"":d||"",this.nestingPrefix=c?br(c):v||br("$t("),this.nestingSuffix=x?br(x):h||br(")"),this.nestingOptionsSeparator=S||",",this.maxReplaces=g||1e3,this.alwaysFormat=m!==void 0?m:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const t=(n,r)=>(n==null?void 0:n.source)===r?(n.lastIndex=0,n):new RegExp(r,"g");this.regexp=t(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=t(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=t(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(t,n,r,o){var v;let i,s,a;const l=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},u=x=>{if(x.indexOf(this.formatSeparator)<0){const m=Up(n,l,x,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(m,void 0,r,{...o,...n,interpolationkey:x}):m}const h=x.split(this.formatSeparator),S=h.shift().trim(),g=h.join(this.formatSeparator).trim();return this.format(Up(n,l,S,this.options.keySeparator,this.options.ignoreJSONStructure),g,r,{...o,...n,interpolationkey:S})};this.resetRegExp();const d=(o==null?void 0:o.missingInterpolationHandler)||this.options.missingInterpolationHandler,f=((v=o==null?void 0:o.interpolation)==null?void 0:v.skipOnVariables)!==void 0?o.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:x=>Al(x)},{regex:this.regexp,safeValue:x=>this.escapeValue?Al(this.escape(x)):Al(x)}].forEach(x=>{for(a=0;i=x.regex.exec(t);){const h=i[1].trim();if(s=u(h),s===void 0)if(typeof d=="function"){const g=d(t,i,o);s=H(g)?g:""}else if(o&&Object.prototype.hasOwnProperty.call(o,h))s="";else if(f){s=i[0];continue}else this.logger.warn(`missed to pass in variable ${h} for interpolating ${t}`),s="";else!H(s)&&!this.useRawValueToEscape&&(s=Op(s));const S=x.safeValue(s);if(t=t.replace(i[0],S),f?(x.regex.lastIndex+=s.length,x.regex.lastIndex-=i[0].length):x.regex.lastIndex=0,a++,a>=this.maxReplaces)break}}),t}nest(t,n,r={}){let o,i,s;const a=(l,u)=>{const d=this.nestingOptionsSeparator;if(l.indexOf(d)<0)return l;const f=l.split(new RegExp(`${d}[ ]*{`));let c=`{${f[1]}`;l=f[0],c=this.interpolate(c,s);const v=c.match(/'/g),x=c.match(/"/g);(((v==null?void 0:v.length)??0)%2===0&&!x||x.length%2!==0)&&(c=c.replace(/'/g,'"'));try{s=JSON.parse(c),u&&(s={...u,...s})}catch(h){return this.logger.warn(`failed parsing options string in nesting for key ${l}`,h),`${l}${d}${c}`}return s.defaultValue&&s.defaultValue.indexOf(this.prefix)>-1&&delete s.defaultValue,l};for(;o=this.nestingRegexp.exec(t);){let l=[];s={...r},s=s.replace&&!H(s.replace)?s.replace:s,s.applyPostProcessor=!1,delete s.defaultValue;const u=/{.*}/.test(o[1])?o[1].lastIndexOf("}")+1:o[1].indexOf(this.formatSeparator);if(u!==-1&&(l=o[1].slice(u).split(this.formatSeparator).map(d=>d.trim()).filter(Boolean),o[1]=o[1].slice(0,u)),i=n(a.call(this,o[1].trim(),s),s),i&&o[0]===t&&!H(i))return i;H(i)||(i=Op(i)),i||(this.logger.warn(`missed to resolve ${o[1]} for nesting ${t}`),i=""),l.length&&(i=l.reduce((d,f)=>this.format(d,f,r.lng,{...r,interpolationkey:o[1].trim()}),i.trim())),t=t.replace(o[0],i),this.regexp.lastIndex=0}return t}}const Qk=e=>{let t=e.toLowerCase().trim();const n={};if(e.indexOf("(")>-1){const r=e.split("(");t=r[0].toLowerCase().trim();const o=r[1].substring(0,r[1].length-1);t==="currency"&&o.indexOf(":")<0?n.currency||(n.currency=o.trim()):t==="relativetime"&&o.indexOf(":")<0?n.range||(n.range=o.trim()):o.split(";").forEach(s=>{if(s){const[a,...l]=s.split(":"),u=l.join(":").trim().replace(/^'+|'+$/g,""),d=a.trim();n[d]||(n[d]=u),u==="false"&&(n[d]=!1),u==="true"&&(n[d]=!0),isNaN(u)||(n[d]=parseInt(u,10))}})}return{formatName:t,formatOptions:n}},Bp=e=>{const t={};return(n,r,o)=>{let i=o;o&&o.interpolationkey&&o.formatParams&&o.formatParams[o.interpolationkey]&&o[o.interpolationkey]&&(i={...i,[o.interpolationkey]:void 0});const s=r+JSON.stringify(i);let a=t[s];return a||(a=e(yi(r),o),t[s]=a),a(n)}},Gk=e=>(t,n,r)=>e(yi(n),r)(t);class Yk{constructor(t={}){this.logger=_t.create("formatter"),this.options=t,this.init(t)}init(t,n={interpolation:{}}){this.formatSeparator=n.interpolation.formatSeparator||",";const r=n.cacheInBuiltFormats?Bp:Gk;this.formats={number:r((o,i)=>{const s=new Intl.NumberFormat(o,{...i});return a=>s.format(a)}),currency:r((o,i)=>{const s=new Intl.NumberFormat(o,{...i,style:"currency"});return a=>s.format(a)}),datetime:r((o,i)=>{const s=new Intl.DateTimeFormat(o,{...i});return a=>s.format(a)}),relativetime:r((o,i)=>{const s=new Intl.RelativeTimeFormat(o,{...i});return a=>s.format(a,i.range||"day")}),list:r((o,i)=>{const s=new Intl.ListFormat(o,{...i});return a=>s.format(a)})}}add(t,n){this.formats[t.toLowerCase().trim()]=n}addCached(t,n){this.formats[t.toLowerCase().trim()]=Bp(n)}format(t,n,r,o={}){const i=n.split(this.formatSeparator);if(i.length>1&&i[0].indexOf("(")>1&&i[0].indexOf(")")<0&&i.find(a=>a.indexOf(")")>-1)){const a=i.findIndex(l=>l.indexOf(")")>-1);i[0]=[i[0],...i.splice(1,a)].join(this.formatSeparator)}return i.reduce((a,l)=>{var f;const{formatName:u,formatOptions:d}=Qk(l);if(this.formats[u]){let c=a;try{const v=((f=o==null?void 0:o.formatParams)==null?void 0:f[o.interpolationkey])||{},x=v.locale||v.lng||o.locale||o.lng||r;c=this.formats[u](a,x,{...d,...o,...v})}catch(v){this.logger.warn(v)}return c}else this.logger.warn(`there was no format function for ${u}`);return a},t)}}const qk=(e,t)=>{e.pending[t]!==void 0&&(delete e.pending[t],e.pendingCount--)};class Xk extends Ia{constructor(t,n,r,o={}){var i,s;super(),this.backend=t,this.store=n,this.services=r,this.languageUtils=r.languageUtils,this.options=o,this.logger=_t.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=o.maxParallelReads||10,this.readingCalls=0,this.maxRetries=o.maxRetries>=0?o.maxRetries:5,this.retryTimeout=o.retryTimeout>=1?o.retryTimeout:350,this.state={},this.queue=[],(s=(i=this.backend)==null?void 0:i.init)==null||s.call(i,r,o.backend,o)}queueLoad(t,n,r,o){const i={},s={},a={},l={};return t.forEach(u=>{let d=!0;n.forEach(f=>{const c=`${u}|${f}`;!r.reload&&this.store.hasResourceBundle(u,f)?this.state[c]=2:this.state[c]<0||(this.state[c]===1?s[c]===void 0&&(s[c]=!0):(this.state[c]=1,d=!1,s[c]===void 0&&(s[c]=!0),i[c]===void 0&&(i[c]=!0),l[f]===void 0&&(l[f]=!0)))}),d||(a[u]=!0)}),(Object.keys(i).length||Object.keys(s).length)&&this.queue.push({pending:s,pendingCount:Object.keys(s).length,loaded:{},errors:[],callback:o}),{toLoad:Object.keys(i),pending:Object.keys(s),toLoadLanguages:Object.keys(a),toLoadNamespaces:Object.keys(l)}}loaded(t,n,r){const o=t.split("|"),i=o[0],s=o[1];n&&this.emit("failedLoading",i,s,n),!n&&r&&this.store.addResourceBundle(i,s,r,void 0,void 0,{skipCopy:!0}),this.state[t]=n?-1:2,n&&r&&(this.state[t]=0);const a={};this.queue.forEach(l=>{_k(l.loaded,[i],s),qk(l,t),n&&l.errors.push(n),l.pendingCount===0&&!l.done&&(Object.keys(l.loaded).forEach(u=>{a[u]||(a[u]={});const d=l.loaded[u];d.length&&d.forEach(f=>{a[u][f]===void 0&&(a[u][f]=!0)})}),l.done=!0,l.errors.length?l.callback(l.errors):l.callback())}),this.emit("loaded",a),this.queue=this.queue.filter(l=>!l.done)}read(t,n,r,o=0,i=this.retryTimeout,s){if(!t.length)return s(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:t,ns:n,fcName:r,tried:o,wait:i,callback:s});return}this.readingCalls++;const a=(u,d)=>{if(this.readingCalls--,this.waitingReads.length>0){const f=this.waitingReads.shift();this.read(f.lng,f.ns,f.fcName,f.tried,f.wait,f.callback)}if(u&&d&&o<this.maxRetries){setTimeout(()=>{this.read.call(this,t,n,r,o+1,i*2,s)},i);return}s(u,d)},l=this.backend[r].bind(this.backend);if(l.length===2){try{const u=l(t,n);u&&typeof u.then=="function"?u.then(d=>a(null,d)).catch(a):a(null,u)}catch(u){a(u)}return}return l(t,n,a)}prepareLoading(t,n,r={},o){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),o&&o();H(t)&&(t=this.languageUtils.toResolveHierarchy(t)),H(n)&&(n=[n]);const i=this.queueLoad(t,n,r,o);if(!i.toLoad.length)return i.pending.length||o(),null;i.toLoad.forEach(s=>{this.loadOne(s)})}load(t,n,r){this.prepareLoading(t,n,{},r)}reload(t,n,r){this.prepareLoading(t,n,{reload:!0},r)}loadOne(t,n=""){const r=t.split("|"),o=r[0],i=r[1];this.read(o,i,"read",void 0,void 0,(s,a)=>{s&&this.logger.warn(`${n}loading namespace ${i} for language ${o} failed`,s),!s&&a&&this.logger.log(`${n}loaded namespace ${i} for language ${o}`,a),this.loaded(t,s,a)})}saveMissing(t,n,r,o,i,s={},a=()=>{}){var l,u,d,f,c;if((u=(l=this.services)==null?void 0:l.utils)!=null&&u.hasLoadedNamespace&&!((f=(d=this.services)==null?void 0:d.utils)!=null&&f.hasLoadedNamespace(n))){this.logger.warn(`did not save key "${r}" as the namespace "${n}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(r==null||r==="")){if((c=this.backend)!=null&&c.create){const v={...s,isUpdate:i},x=this.backend.create.bind(this.backend);if(x.length<6)try{let h;x.length===5?h=x(t,n,r,o,v):h=x(t,n,r,o),h&&typeof h.then=="function"?h.then(S=>a(null,S)).catch(a):a(null,h)}catch(h){a(h)}else x(t,n,r,o,a,v)}!t||!t[0]||this.store.addResource(t[0],n,r,o)}}}const Vp=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if(typeof e[1]=="object"&&(t=e[1]),H(e[1])&&(t.defaultValue=e[1]),H(e[2])&&(t.tDescription=e[2]),typeof e[2]=="object"||typeof e[3]=="object"){const n=e[3]||e[2];Object.keys(n).forEach(r=>{t[r]=n[r]})}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),Hp=e=>{var t,n;return H(e.ns)&&(e.ns=[e.ns]),H(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),H(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),((n=(t=e.supportedLngs)==null?void 0:t.indexOf)==null?void 0:n.call(t,"cimode"))<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),typeof e.initImmediate=="boolean"&&(e.initAsync=e.initImmediate),e},us=()=>{},Zk=e=>{Object.getOwnPropertyNames(Object.getPrototypeOf(e)).forEach(n=>{typeof e[n]=="function"&&(e[n]=e[n].bind(e))})};class xi extends Ia{constructor(t={},n){if(super(),this.options=Hp(t),this.services={},this.logger=_t,this.modules={external:[]},Zk(this),n&&!this.isInitialized&&!t.isClone){if(!this.options.initAsync)return this.init(t,n),this;setTimeout(()=>{this.init(t,n)},0)}}init(t={},n){this.isInitializing=!0,typeof t=="function"&&(n=t,t={}),t.defaultNS==null&&t.ns&&(H(t.ns)?t.defaultNS=t.ns:t.ns.indexOf("translation")<0&&(t.defaultNS=t.ns[0]));const r=Vp();this.options={...r,...this.options,...Hp(t)},this.options.interpolation={...r.interpolation,...this.options.interpolation},t.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=t.keySeparator),t.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=t.nsSeparator);const o=u=>u?typeof u=="function"?new u:u:null;if(!this.options.isClone){this.modules.logger?_t.init(o(this.modules.logger),this.options):_t.init(null,this.options);let u;this.modules.formatter?u=this.modules.formatter:u=Yk;const d=new Fp(this.options);this.store=new Dp(this.options.resources,this.options);const f=this.services;f.logger=_t,f.resourceStore=this.store,f.languageUtils=d,f.pluralResolver=new Wk(d,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),this.options.interpolation.format&&this.options.interpolation.format!==r.interpolation.format&&this.logger.warn("init: you are still using the legacy format function, please use the new approach: https://www.i18next.com/translation-function/formatting"),u&&(!this.options.interpolation.format||this.options.interpolation.format===r.interpolation.format)&&(f.formatter=o(u),f.formatter.init&&f.formatter.init(f,this.options),this.options.interpolation.format=f.formatter.format.bind(f.formatter)),f.interpolator=new Kk(this.options),f.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},f.backendConnector=new Xk(o(this.modules.backend),f.resourceStore,f,this.options),f.backendConnector.on("*",(v,...x)=>{this.emit(v,...x)}),this.modules.languageDetector&&(f.languageDetector=o(this.modules.languageDetector),f.languageDetector.init&&f.languageDetector.init(f,this.options.detection,this.options)),this.modules.i18nFormat&&(f.i18nFormat=o(this.modules.i18nFormat),f.i18nFormat.init&&f.i18nFormat.init(this)),this.translator=new aa(this.services,this.options),this.translator.on("*",(v,...x)=>{this.emit(v,...x)}),this.modules.external.forEach(v=>{v.init&&v.init(this)})}if(this.format=this.options.interpolation.format,n||(n=us),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const u=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);u.length>0&&u[0]!=="dev"&&(this.options.lng=u[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(u=>{this[u]=(...d)=>this.store[u](...d)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(u=>{this[u]=(...d)=>(this.store[u](...d),this)});const a=Lo(),l=()=>{const u=(d,f)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),a.resolve(f),n(d,f)};if(this.languages&&!this.isInitialized)return u(null,this.t.bind(this));this.changeLanguage(this.options.lng,u)};return this.options.resources||!this.options.initAsync?l():setTimeout(l,0),a}loadResources(t,n=us){var i,s;let r=n;const o=H(t)?t:this.language;if(typeof t=="function"&&(r=t),!this.options.resources||this.options.partialBundledLanguages){if((o==null?void 0:o.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return r();const a=[],l=u=>{if(!u||u==="cimode")return;this.services.languageUtils.toResolveHierarchy(u).forEach(f=>{f!=="cimode"&&a.indexOf(f)<0&&a.push(f)})};o?l(o):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(d=>l(d)),(s=(i=this.options.preload)==null?void 0:i.forEach)==null||s.call(i,u=>l(u)),this.services.backendConnector.load(a,this.options.ns,u=>{!u&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),r(u)})}else r(null)}reloadResources(t,n,r){const o=Lo();return typeof t=="function"&&(r=t,t=void 0),typeof n=="function"&&(r=n,n=void 0),t||(t=this.languages),n||(n=this.options.ns),r||(r=us),this.services.backendConnector.reload(t,n,i=>{o.resolve(),r(i)}),o}use(t){if(!t)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!t.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return t.type==="backend"&&(this.modules.backend=t),(t.type==="logger"||t.log&&t.warn&&t.error)&&(this.modules.logger=t),t.type==="languageDetector"&&(this.modules.languageDetector=t),t.type==="i18nFormat"&&(this.modules.i18nFormat=t),t.type==="postProcessor"&&$y.addPostProcessor(t),t.type==="formatter"&&(this.modules.formatter=t),t.type==="3rdParty"&&this.modules.external.push(t),this}setResolvedLanguage(t){if(!(!t||!this.languages)&&!(["cimode","dev"].indexOf(t)>-1)){for(let n=0;n<this.languages.length;n++){const r=this.languages[n];if(!(["cimode","dev"].indexOf(r)>-1)&&this.store.hasLanguageSomeTranslations(r)){this.resolvedLanguage=r;break}}!this.resolvedLanguage&&this.languages.indexOf(t)<0&&this.store.hasLanguageSomeTranslations(t)&&(this.resolvedLanguage=t,this.languages.unshift(t))}}changeLanguage(t,n){this.isLanguageChangingTo=t;const r=Lo();this.emit("languageChanging",t);const o=a=>{this.language=a,this.languages=this.services.languageUtils.toResolveHierarchy(a),this.resolvedLanguage=void 0,this.setResolvedLanguage(a)},i=(a,l)=>{l?this.isLanguageChangingTo===t&&(o(l),this.translator.changeLanguage(l),this.isLanguageChangingTo=void 0,this.emit("languageChanged",l),this.logger.log("languageChanged",l)):this.isLanguageChangingTo=void 0,r.resolve((...u)=>this.t(...u)),n&&n(a,(...u)=>this.t(...u))},s=a=>{var d,f;!t&&!a&&this.services.languageDetector&&(a=[]);const l=H(a)?a:a&&a[0],u=this.store.hasLanguageSomeTranslations(l)?l:this.services.languageUtils.getBestMatchFromCodes(H(a)?[a]:a);u&&(this.language||o(u),this.translator.language||this.translator.changeLanguage(u),(f=(d=this.services.languageDetector)==null?void 0:d.cacheUserLanguage)==null||f.call(d,u)),this.loadResources(u,c=>{i(c,u)})};return!t&&this.services.languageDetector&&!this.services.languageDetector.async?s(this.services.languageDetector.detect()):!t&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(s):this.services.languageDetector.detect(s):s(t),r}getFixedT(t,n,r){const o=(i,s,...a)=>{let l;typeof s!="object"?l=this.options.overloadTranslationOptionHandler([i,s].concat(a)):l={...s},l.lng=l.lng||o.lng,l.lngs=l.lngs||o.lngs,l.ns=l.ns||o.ns,l.keyPrefix!==""&&(l.keyPrefix=l.keyPrefix||r||o.keyPrefix);const u=this.options.keySeparator||".";let d;return l.keyPrefix&&Array.isArray(i)?d=i.map(f=>`${l.keyPrefix}${u}${f}`):d=l.keyPrefix?`${l.keyPrefix}${u}${i}`:i,this.t(d,l)};return H(t)?o.lng=t:o.lngs=t,o.ns=n,o.keyPrefix=r,o}t(...t){var n;return(n=this.translator)==null?void 0:n.translate(...t)}exists(...t){var n;return(n=this.translator)==null?void 0:n.exists(...t)}setDefaultNamespace(t){this.options.defaultNS=t}hasLoadedNamespace(t,n={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const r=n.lng||this.resolvedLanguage||this.languages[0],o=this.options?this.options.fallbackLng:!1,i=this.languages[this.languages.length-1];if(r.toLowerCase()==="cimode")return!0;const s=(a,l)=>{const u=this.services.backendConnector.state[`${a}|${l}`];return u===-1||u===0||u===2};if(n.precheck){const a=n.precheck(this,s);if(a!==void 0)return a}return!!(this.hasResourceBundle(r,t)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||s(r,t)&&(!o||s(i,t)))}loadNamespaces(t,n){const r=Lo();return this.options.ns?(H(t)&&(t=[t]),t.forEach(o=>{this.options.ns.indexOf(o)<0&&this.options.ns.push(o)}),this.loadResources(o=>{r.resolve(),n&&n(o)}),r):(n&&n(),Promise.resolve())}loadLanguages(t,n){const r=Lo();H(t)&&(t=[t]);const o=this.options.preload||[],i=t.filter(s=>o.indexOf(s)<0&&this.services.languageUtils.isSupportedCode(s));return i.length?(this.options.preload=o.concat(i),this.loadResources(s=>{r.resolve(),n&&n(s)}),r):(n&&n(),Promise.resolve())}dir(t){var o,i;if(t||(t=this.resolvedLanguage||(((o=this.languages)==null?void 0:o.length)>0?this.languages[0]:this.language)),!t)return"rtl";if(Intl.Locale){const s=new Intl.Locale(t);if(s&&s.getTextInfo){const a=s.getTextInfo();if(a&&a.direction)return a.direction}}const n=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],r=((i=this.services)==null?void 0:i.languageUtils)||new Fp(Vp());return t.toLowerCase().indexOf("-latn")>1?"ltr":n.indexOf(r.getLanguagePartFromCode(t))>-1||t.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(t={},n){return new xi(t,n)}cloneInstance(t={},n=us){const r=t.forkResourceStore;r&&delete t.forkResourceStore;const o={...this.options,...t,isClone:!0},i=new xi(o);if((t.debug!==void 0||t.prefix!==void 0)&&(i.logger=i.logger.clone(t)),["store","services","language"].forEach(a=>{i[a]=this[a]}),i.services={...this.services},i.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},r){const a=Object.keys(this.store.data).reduce((l,u)=>(l[u]={...this.store.data[u]},l[u]=Object.keys(l[u]).reduce((d,f)=>(d[f]={...l[u][f]},d),l[u]),l),{});i.store=new Dp(a,o),i.services.resourceStore=i.store}return i.translator=new aa(i.services,o),i.translator.on("*",(a,...l)=>{i.emit(a,...l)}),i.init(o,n),i.translator.options=o,i.translator.backendConnector.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},i}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const Ie=xi.createInstance();Ie.createInstance=xi.createInstance;Ie.createInstance;Ie.dir;Ie.init;Ie.loadResources;Ie.reloadResources;Ie.use;Ie.changeLanguage;Ie.getFixedT;Ie.t;Ie.exists;Ie.setDefaultNamespace;Ie.hasLoadedNamespace;Ie.loadNamespaces;Ie.loadLanguages;const{slice:Jk,forEach:eN}=[];function tN(e){return eN.call(Jk.call(arguments,1),t=>{if(t)for(const n in t)e[n]===void 0&&(e[n]=t[n])}),e}function nN(e){return typeof e!="string"?!1:[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(n=>n.test(e))}const Wp=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,rN=function(e,t){const r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{path:"/"},o=encodeURIComponent(t);let i=`${e}=${o}`;if(r.maxAge>0){const s=r.maxAge-0;if(Number.isNaN(s))throw new Error("maxAge should be a Number");i+=`; Max-Age=${Math.floor(s)}`}if(r.domain){if(!Wp.test(r.domain))throw new TypeError("option domain is invalid");i+=`; Domain=${r.domain}`}if(r.path){if(!Wp.test(r.path))throw new TypeError("option path is invalid");i+=`; Path=${r.path}`}if(r.expires){if(typeof r.expires.toUTCString!="function")throw new TypeError("option expires is invalid");i+=`; Expires=${r.expires.toUTCString()}`}if(r.httpOnly&&(i+="; HttpOnly"),r.secure&&(i+="; Secure"),r.sameSite)switch(typeof r.sameSite=="string"?r.sameSite.toLowerCase():r.sameSite){case!0:i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"strict":i+="; SameSite=Strict";break;case"none":i+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return r.partitioned&&(i+="; Partitioned"),i},Kp={create(e,t,n,r){let o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};n&&(o.expires=new Date,o.expires.setTime(o.expires.getTime()+n*60*1e3)),r&&(o.domain=r),document.cookie=rN(e,t,o)},read(e){const t=`${e}=`,n=document.cookie.split(";");for(let r=0;r<n.length;r++){let o=n[r];for(;o.charAt(0)===" ";)o=o.substring(1,o.length);if(o.indexOf(t)===0)return o.substring(t.length,o.length)}return null},remove(e,t){this.create(e,"",-1,t)}};var oN={name:"cookie",lookup(e){let{lookupCookie:t}=e;if(t&&typeof document<"u")return Kp.read(t)||void 0},cacheUserLanguage(e,t){let{lookupCookie:n,cookieMinutes:r,cookieDomain:o,cookieOptions:i}=t;n&&typeof document<"u"&&Kp.create(n,e,r,o,i)}},iN={name:"querystring",lookup(e){var r;let{lookupQuerystring:t}=e,n;if(typeof window<"u"){let{search:o}=window.location;!window.location.search&&((r=window.location.hash)==null?void 0:r.indexOf("?"))>-1&&(o=window.location.hash.substring(window.location.hash.indexOf("?")));const s=o.substring(1).split("&");for(let a=0;a<s.length;a++){const l=s[a].indexOf("=");l>0&&s[a].substring(0,l)===t&&(n=s[a].substring(l+1))}}return n}},sN={name:"hash",lookup(e){var o;let{lookupHash:t,lookupFromHashIndex:n}=e,r;if(typeof window<"u"){const{hash:i}=window.location;if(i&&i.length>2){const s=i.substring(1);if(t){const a=s.split("&");for(let l=0;l<a.length;l++){const u=a[l].indexOf("=");u>0&&a[l].substring(0,u)===t&&(r=a[l].substring(u+1))}}if(r)return r;if(!r&&n>-1){const a=i.match(/\/([a-zA-Z-]*)/g);return Array.isArray(a)?(o=a[typeof n=="number"?n:0])==null?void 0:o.replace("/",""):void 0}}}return r}};let Cr=null;const Qp=()=>{if(Cr!==null)return Cr;try{if(Cr=typeof window<"u"&&window.localStorage!==null,!Cr)return!1;const e="i18next.translate.boo";window.localStorage.setItem(e,"foo"),window.localStorage.removeItem(e)}catch{Cr=!1}return Cr};var aN={name:"localStorage",lookup(e){let{lookupLocalStorage:t}=e;if(t&&Qp())return window.localStorage.getItem(t)||void 0},cacheUserLanguage(e,t){let{lookupLocalStorage:n}=t;n&&Qp()&&window.localStorage.setItem(n,e)}};let Er=null;const Gp=()=>{if(Er!==null)return Er;try{if(Er=typeof window<"u"&&window.sessionStorage!==null,!Er)return!1;const e="i18next.translate.boo";window.sessionStorage.setItem(e,"foo"),window.sessionStorage.removeItem(e)}catch{Er=!1}return Er};var lN={name:"sessionStorage",lookup(e){let{lookupSessionStorage:t}=e;if(t&&Gp())return window.sessionStorage.getItem(t)||void 0},cacheUserLanguage(e,t){let{lookupSessionStorage:n}=t;n&&Gp()&&window.sessionStorage.setItem(n,e)}},uN={name:"navigator",lookup(e){const t=[];if(typeof navigator<"u"){const{languages:n,userLanguage:r,language:o}=navigator;if(n)for(let i=0;i<n.length;i++)t.push(n[i]);r&&t.push(r),o&&t.push(o)}return t.length>0?t:void 0}},cN={name:"htmlTag",lookup(e){let{htmlTag:t}=e,n;const r=t||(typeof document<"u"?document.documentElement:null);return r&&typeof r.getAttribute=="function"&&(n=r.getAttribute("lang")),n}},dN={name:"path",lookup(e){var o;let{lookupFromPathIndex:t}=e;if(typeof window>"u")return;const n=window.location.pathname.match(/\/([a-zA-Z-]*)/g);return Array.isArray(n)?(o=n[typeof t=="number"?t:0])==null?void 0:o.replace("/",""):void 0}},fN={name:"subdomain",lookup(e){var o,i;let{lookupFromSubdomainIndex:t}=e;const n=typeof t=="number"?t+1:1,r=typeof window<"u"&&((i=(o=window.location)==null?void 0:o.hostname)==null?void 0:i.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i));if(r)return r[n]}};let zy=!1;try{document.cookie,zy=!0}catch{}const Uy=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];zy||Uy.splice(1,1);const pN=()=>({order:Uy,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:e=>e});class By{constructor(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(t,n)}init(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{languageUtils:{}},n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=t,this.options=tN(n,this.options||{},pN()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=o=>o.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=r,this.addDetector(oN),this.addDetector(iN),this.addDetector(aN),this.addDetector(lN),this.addDetector(uN),this.addDetector(cN),this.addDetector(dN),this.addDetector(fN),this.addDetector(sN)}addDetector(t){return this.detectors[t.name]=t,this}detect(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.order,n=[];return t.forEach(r=>{if(this.detectors[r]){let o=this.detectors[r].lookup(this.options);o&&typeof o=="string"&&(o=[o]),o&&(n=n.concat(o))}}),n=n.filter(r=>r!=null&&!nN(r)).map(r=>this.options.convertDetectedLanguage(r)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?n:n.length>0?n[0]:null}cacheUserLanguage(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.options.caches;n&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(t)>-1||n.forEach(r=>{this.detectors[r]&&this.detectors[r].cacheUserLanguage(t,this.options)}))}}By.type="languageDetector";const hN={home:"Home",services:"Services",tools:"Tools",contact:"Contact"},mN={badge:"Professional Technical Services",title:"Drama Code",subtitle:"Professional Custom Tech Services",description1:"We specialize in providing high-quality website development, script automation, mini-program creation",description2:"and other technical services to bring your creative ideas to life",cta:"Free Consultation",contact:"Contact Now",features:"✨ Professional · Efficient · Reliable ✨"},gN={badge:"Services",title:"Our",titleHighlight:"Services",description1:"Professional customized technical services, providing complete solutions for your creative ideas",description2:"From website development to script automation, we use technology to bring your ideas to life",web:{title:"Website Development",description:"From corporate websites to e-commerce platforms, we provide full-stack development services including responsive design, SEO optimization, and performance tuning. Using modern tech stacks to ensure fast, secure, and maintainable websites.",features:["Responsive design for perfect device compatibility","SEO optimization to improve search rankings","High-performance architecture for fast loading","Security protection for data safety","Ongoing maintenance and technical support"]},script:{title:"Script Development",description:"Automation script development to boost work efficiency. Supporting data collection, batch processing, system integration, and various automation needs using Python, Node.js, and other technologies.",features:["Data collection and processing automation","Custom batch operation scripts","API integration development","Scheduled task systems","Error handling and logging"]},miniprogram:{title:"Mini-Program Development",description:"Multi-platform development for WeChat Mini Programs, Alipay Mini Programs, and more. From UI design to feature implementation, we provide one-stop mini-program solutions to help your business launch quickly.",features:["Multi-platform mini-program development","Native performance optimization","User experience design","Payment system integration","Data analytics and statistics"]},mobile:{title:"Mobile App Development",description:"Professional iOS and Android native application development services. From product design to app store deployment, we provide comprehensive mobile application solutions.",features:["iOS/Android native development","Cross-platform Flutter/React Native","UI/UX design and optimization","App store submission guidance","Performance optimization and security"]}},vN={title:"Service Process",subtitle:"Professional service workflow ensuring high-quality project delivery",steps:[{title:"Requirements Analysis",description:"Deep understanding of your needs, feasibility analysis, and initial solution planning"},{title:"Solution Design",description:"Detailed technical solution and project planning, defining development timeline and milestones"},{title:"Development Implementation",description:"Development according to the plan, regular progress reports, and timely communication for adjustments"},{title:"Testing & Acceptance",description:"Comprehensive functionality testing, issue resolution, ensuring project quality meets expectations"},{title:"Deployment & Launch",description:"Assistance with project deployment and launch, providing user training and technical documentation"},{title:"Ongoing Maintenance",description:"Technical support and maintenance services to ensure stable system operation"}]},yN={title:"Technical Tools",subtitle:"We use the latest tech stacks and tools to ensure project modernization and maintainability",badge:"Tech Showcase",categories:{all:"All Tools",development:"Development",data:"Data Processing",automation:"Automation",utilities:"Utilities"}},xN={title:"Contact Us",telegram:"Contact via Telegram",wechat:"WeChat Consultation",email:"Email Contact"},wN={copyright:"© 2024 Drama Code. All rights reserved.",description:"Professional technical service provider"},SN={learnMore:"Learn More",getStarted:"Get Started",viewDetails:"View Details",close:"Close"},bN={nav:hN,hero:mN,services:gN,process:vN,tools:yN,contact:xN,footer:wN,common:SN},CN={home:"首页",services:"服务",tools:"工具",contact:"联系我们"},EN={badge:"专业技术服务",title:"Drama Code | 抓马代码",subtitle:"专业定制化技术服务",description1:"我们专注于为您提供高质量的网站搭建、脚本开发、小程序制作等技术服务",description2:"让您的创意想法变成现实，用技术驱动您的业务增长",cta:"免费咨询",contact:"立即联系",features:"✨ 专业 · 高效 · 可靠 ✨"},kN={badge:"服务",title:"我们的",titleHighlight:"服务",description1:"专业定制化技术服务，为您的创意想法提供完整解决方案",description2:"从网站搭建到脚本开发，我们用技术让您的想法变成现实",web:{title:"网站搭建",description:"从企业官网到电商平台，我们提供全栈开发服务，包括响应式设计、SEO优化、性能调优等。使用现代化技术栈，确保网站快速、安全、易维护。",features:["响应式设计，完美适配各种设备","SEO优化，提升搜索引擎排名","高性能架构，确保快速加载","安全防护，保障数据安全","后期维护，持续技术支持"]},script:{title:"脚本开发",description:"自动化脚本开发，提升工作效率。支持数据采集、批量处理、系统集成等各种自动化需求。使用Python、Node.js等技术实现。",features:["数据采集与处理自动化","批量操作脚本定制","API接口集成开发","定时任务调度系统","错误处理与日志记录"]},miniprogram:{title:"小程序制作",description:"微信小程序、支付宝小程序等多平台开发。从UI设计到功能实现，提供一站式小程序解决方案，助力您的业务快速上线。",features:["多平台小程序开发","原生性能优化","用户体验设计","支付功能集成","数据统计分析"]},mobile:{title:"移动端APP开发",description:"专业的iOS和Android原生应用开发服务，从产品设计到应用上架，提供全流程移动应用解决方案。",features:["iOS/Android原生开发","跨平台Flutter/React Native","UI/UX设计与优化","应用商店上架指导","性能优化与安全防护"]}},NN={title:"服务流程",subtitle:"专业的服务流程，确保项目高质量交付",steps:[{title:"需求沟通",description:"深入了解您的需求，分析项目可行性，制定初步方案"},{title:"方案设计",description:"制定详细的技术方案和项目计划，确定开发周期和里程碑"},{title:"开发实施",description:"按照方案进行开发，定期汇报进度，及时沟通调整"},{title:"测试验收",description:"全面测试功能，修复问题，确保项目质量达到预期"},{title:"部署上线",description:"协助项目部署上线，提供使用培训和技术文档"},{title:"后期维护",description:"提供技术支持和维护服务，确保系统稳定运行"}]},PN={title:"技术工具",subtitle:"我们使用最新的技术栈和工具，确保项目的现代化和可维护性",badge:"技术展示",categories:{all:"全部工具",development:"开发工具",data:"数据处理",automation:"自动化脚本",utilities:"实用工具"}},TN={title:"联系我们",telegram:"Telegram 联系",wechat:"微信咨询",email:"邮件联系"},RN={copyright:"© 2024 Drama Code. 保留所有权利。",description:"专业的技术服务提供商"},jN={learnMore:"了解更多",getStarted:"开始使用",viewDetails:"查看详情",close:"关闭"},ON={nav:CN,hero:EN,services:kN,process:NN,tools:PN,contact:TN,footer:RN,common:jN},LN={en:{translation:bN},zh:{translation:ON}};Ie.use(By).use(rE).init({resources:LN,fallbackLng:"zh",debug:!1,interpolation:{escapeValue:!1},detection:{order:["localStorage","navigator","htmlTag"],caches:["localStorage"]}});yg(document.getElementById("root")).render(p.jsx(Ak,{}));
