@tailwind base;
@tailwind components;
@tailwind utilities;

/* Drama Code Design System - 科技感深色主题 */

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 263 70% 50.4%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 263 85% 70%;

    --secondary: 240 5.9% 10%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 5.9% 10%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 197 71% 52%;
    --accent-foreground: 240 10% 3.9%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 10%;
    --input: 240 5.9% 10%;
    --ring: 263 70% 50.4%;

    /* 渐变配色 */
    --gradient-primary: linear-gradient(135deg, hsl(263 70% 50.4%) 0%, hsl(197 71% 52%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(240 5.9% 10%) 0%, hsl(240 10% 3.9%) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(263 85% 70% / 0.1) 0%, hsl(197 71% 52% / 0.1) 100%);
    --gradient-shimmer: linear-gradient(90deg, transparent, hsl(263 70% 50.4% / 0.4), transparent);

    /* 阴影效果 */
    --shadow-primary: 0 20px 25px -5px hsl(263 70% 50.4% / 0.3);
    --shadow-glow: 0 0 40px hsl(263 85% 70% / 0.4);
    --shadow-card: 0 10px 15px -3px hsl(240 10% 3.9% / 0.3);
    --shadow-neon: 0 0 20px hsl(263 70% 50.4% / 0.6), 0 0 40px hsl(263 70% 50.4% / 0.4);

    /* 动画 */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-elastic: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* 可访问性样式 */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .focus\:not-sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }

  /* 改善焦点可见性 */
  *:focus {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }

  /* 减少动画对于偏好减少动画的用户 */
  @media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
}

/* 科技感几何图案样式 */
@layer components {
  .web-pattern {
    background-image:
      linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
      linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .code-pattern {
    background-image: repeating-linear-gradient(45deg,
        rgba(34, 197, 94, 0.1),
        rgba(34, 197, 94, 0.1) 2px,
        transparent 2px,
        transparent 12px);
  }

  .hex-pattern {
    background-image: radial-gradient(circle at 50% 50%, rgba(251, 146, 60, 0.1) 2px, transparent 2px);
    background-size: 24px 24px;
    background-position: 0 0, 12px 12px;
  }

  .circle-pattern {
    background-image: radial-gradient(circle, rgba(239, 68, 68, 0.1) 1px, transparent 1px);
    background-size: 16px 16px;
  }

  .default-pattern {
    background-image:
      radial-gradient(circle at 25% 25%, rgba(147, 51, 234, 0.1) 2px, transparent 2px),
      radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.1) 2px, transparent 2px);
    background-size: 20px 20px;
  }

  /* 增强的动画效果 */
  .animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out forwards;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes glow {
    from {
      box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);
    }

    to {
      box-shadow: 0 0 30px rgba(147, 51, 234, 0.6), 0 0 40px rgba(6, 182, 212, 0.3);
    }
  }

  @keyframes float {

    0%,
    100% {
      transform: translateY(0px);
    }

    50% {
      transform: translateY(-10px);
    }
  }

  /* 科技感光效 */
  .tech-glow {
    position: relative;
  }

  .tech-glow::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, transparent, rgba(147, 51, 234, 0.4), transparent);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  .tech-glow:hover::before {
    opacity: 1;
  }
}
