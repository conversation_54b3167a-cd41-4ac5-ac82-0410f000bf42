@tailwind base;
@tailwind components;
@tailwind utilities;

/* Drama Code Design System - 科技感深色主题 */

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 263 70% 50.4%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 263 85% 70%;

    --secondary: 240 5.9% 10%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 5.9% 10%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 197 71% 52%;
    --accent-foreground: 240 10% 3.9%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 10%;
    --input: 240 5.9% 10%;
    --ring: 263 70% 50.4%;

    /* 渐变配色 */
    --gradient-primary: linear-gradient(135deg, hsl(263 70% 50.4%) 0%, hsl(197 71% 52%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(240 5.9% 10%) 0%, hsl(240 10% 3.9%) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(263 85% 70% / 0.1) 0%, hsl(197 71% 52% / 0.1) 100%);
    --gradient-shimmer: linear-gradient(90deg, transparent, hsl(263 70% 50.4% / 0.4), transparent);
    
    /* 阴影效果 */
    --shadow-primary: 0 20px 25px -5px hsl(263 70% 50.4% / 0.3);
    --shadow-glow: 0 0 40px hsl(263 85% 70% / 0.4);
    --shadow-card: 0 10px 15px -3px hsl(240 10% 3.9% / 0.3);
    --shadow-neon: 0 0 20px hsl(263 70% 50.4% / 0.6), 0 0 40px hsl(263 70% 50.4% / 0.4);

    /* 动画 */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-elastic: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}