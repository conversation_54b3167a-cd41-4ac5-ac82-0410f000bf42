import { Button } from "@/components/ui/button";
import { ArrowLeft, Home } from "lucide-react";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router-dom";

const NotFound = () => {
  const location = useLocation();
  const { t } = useTranslation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );

    // 记录404错误到分析服务（如果有的话）
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'page_not_found', {
        page_path: location.pathname,
        page_title: '404 - Page Not Found'
      });
    }
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="max-w-md w-full text-center space-y-8">
        {/* 404动画数字 */}
        <div className="space-y-4">
          <div className="text-8xl font-bold bg-gradient-primary bg-clip-text text-transparent animate-pulse">
            404
          </div>
          <div className="w-24 h-1 bg-gradient-primary mx-auto rounded-full"></div>
        </div>

        {/* 错误信息 */}
        <div className="space-y-3">
          <h1 className="text-2xl font-bold text-foreground">
            页面未找到
          </h1>
          <p className="text-muted-foreground leading-relaxed">
            抱歉，您访问的页面不存在或已被移动。
            <br />
            请检查URL是否正确，或返回首页继续浏览。
          </p>

          {/* 显示尝试访问的路径 */}
          <div className="bg-muted/50 p-3 rounded-lg border text-sm">
            <span className="text-muted-foreground">尝试访问：</span>
            <code className="text-primary font-mono ml-2">
              {location.pathname}
            </code>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button
            asChild
            className="flex items-center gap-2 bg-gradient-primary hover:shadow-glow"
          >
            <Link to="/">
              <Home className="w-4 h-4" />
              返回首页
            </Link>
          </Button>

          <Button
            variant="outline"
            onClick={() => window.history.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            返回上页
          </Button>
        </div>

        {/* 搜索建议 */}
        <div className="pt-4 border-t border-border">
          <p className="text-sm text-muted-foreground mb-3">
            或者您可以：
          </p>
          <div className="flex flex-col gap-2 text-sm">
            <Link
              to="/#services"
              className="text-primary hover:text-primary/80 transition-colors"
            >
              • 查看我们的服务
            </Link>
            <Link
              to="/#tools"
              className="text-primary hover:text-primary/80 transition-colors"
            >
              • 浏览技术工具
            </Link>
            <a
              href="mailto:<EMAIL>"
              className="text-primary hover:text-primary/80 transition-colors"
            >
              • 联系我们获取帮助
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
