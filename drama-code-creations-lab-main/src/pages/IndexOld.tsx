import Hero from '@/components/Hero';
import Navbar from '@/components/Navbar';
import ServiceProcess from '@/components/ServiceProcess';
import Services from '@/components/Services';
import Tools from '@/components/Tools';
import { useTranslation } from 'react-i18next';

const Index = () => {
  const { t } = useTranslation();
  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <Hero />
      <Services />
      <ServiceProcess />
      <Tools />

      {/* Footer */}
      <footer className="bg-background border-t border-border py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-muted-foreground">
              {t('footer.copyright')}
            </p>
            <p className="text-sm text-muted-foreground mt-2">
              {t('footer.description')}
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
