import Hero from '@/components/Hero';
import Navbar from '@/components/Navbar';
import ServiceProcess from '@/components/ServiceProcess';
import Services from '@/components/Services';
import Tools from '@/components/Tools';
import { SkipToContent } from '@/lib/accessibility';
import { useTranslation } from 'react-i18next';

const Index = () => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-background">
      <SEOHead
        title="Drama Code - 专业定制化技术服务"
        description="Drama Code提供专业的网站开发、移动应用开发、系统集成等定制化技术服务。我们致力于为客户提供高质量、创新的技术解决方案。"
        keywords="网站开发,移动应用开发,系统集成,技术服务,定制开发,前端开发,后端开发,全栈开发,React,TypeScript,Node.js"
        type="website"
      />
      <SkipToContent />
      <Navbar />
      <main id="main-content" role="main" aria-label="主要内容">
        <Hero />
        <Services />
        <ServiceProcess />
        <Tools />
      </main>

      {/* 科技感Footer */}
      <footer className="relative bg-background border-t border-primary/20 py-12 overflow-hidden">
        {/* 背景效果 */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-t from-primary/5 to-transparent"></div>
          <div className="absolute inset-0 opacity-10">
            <div className="w-full h-full default-pattern"></div>
          </div>
          {/* 光效线条 */}
          <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary/50 to-transparent"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            {/* Logo区域 */}
            <div className="mb-6">
              <h3 className="text-2xl font-bold bg-gradient-primary bg-clip-text text-transparent">
                Drama Code
              </h3>
              <div className="w-20 h-px bg-gradient-primary mx-auto mt-2"></div>
            </div>

            {/* 版权信息 */}
            <p className="text-muted-foreground mb-2 animate-fade-in">
              {t('footer.copyright')}
            </p>
            <p className="text-sm text-muted-foreground/80 animate-fade-in" style={{ animationDelay: '0.2s' }}>
              {t('footer.description')}
            </p>

            {/* 装饰元素 */}
            <div className="flex justify-center items-center mt-6 space-x-4">
              <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
              <div className="w-1 h-1 bg-accent rounded-full animate-ping"></div>
              <div className="w-2 h-2 bg-primary rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
