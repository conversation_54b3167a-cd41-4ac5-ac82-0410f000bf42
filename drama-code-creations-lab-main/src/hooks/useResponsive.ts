import {
  Breakpoint,
  DeviceInfo,
  getDeviceInfo,
  performance,
  viewport
} from '@/lib/responsive';
import { useCallback, useEffect, useState } from 'react';

/**
 * 响应式设计Hook
 * 提供设备信息、断点检测和响应式状态管理
 */
export const useResponsive = () => {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>(() => getDeviceInfo());
  const [isClient, setIsClient] = useState(false);

  // 更新设备信息的防抖函数
  const updateDeviceInfo = useCallback(() => {
    const debouncedUpdate = performance.debounce(() => {
      setDeviceInfo(getDeviceInfo());
    }, 150);
    debouncedUpdate();
  }, []);

  useEffect(() => {
    setIsClient(true);

    // 设置视口高度CSS变量
    viewport.setViewportHeight();

    // 监听窗口大小变化
    window.addEventListener('resize', updateDeviceInfo);
    window.addEventListener('orientationchange', updateDeviceInfo);

    // 初始更新
    updateDeviceInfo();

    return () => {
      window.removeEventListener('resize', updateDeviceInfo);
      window.removeEventListener('orientationchange', updateDeviceInfo);
    };
  }, [updateDeviceInfo]);

  // 断点检测函数
  const isBreakpoint = useCallback((breakpoint: Breakpoint) => {
    return deviceInfo.breakpoint === breakpoint;
  }, [deviceInfo.breakpoint]);

  const isBreakpointUp = useCallback((breakpoint: Breakpoint) => {
    const breakpoints: Breakpoint[] = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];
    const currentIndex = breakpoints.indexOf(deviceInfo.breakpoint);
    const targetIndex = breakpoints.indexOf(breakpoint);
    return currentIndex >= targetIndex;
  }, [deviceInfo.breakpoint]);

  const isBreakpointDown = useCallback((breakpoint: Breakpoint) => {
    const breakpoints: Breakpoint[] = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];
    const currentIndex = breakpoints.indexOf(deviceInfo.breakpoint);
    const targetIndex = breakpoints.indexOf(breakpoint);
    return currentIndex <= targetIndex;
  }, [deviceInfo.breakpoint]);

  // 设备类型检测
  const isMobile = deviceInfo.type === 'mobile';
  const isTablet = deviceInfo.type === 'tablet';
  const isDesktop = deviceInfo.type === 'desktop';

  // 屏幕方向检测
  const isPortrait = deviceInfo.orientation === 'portrait';
  const isLandscape = deviceInfo.orientation === 'landscape';

  // 特殊设备检测
  const isTouchDevice = deviceInfo.isTouchDevice;
  const isRetina = deviceInfo.isRetina;

  return {
    // 设备信息
    deviceInfo,
    isClient,

    // 设备类型
    isMobile,
    isTablet,
    isDesktop,

    // 屏幕方向
    isPortrait,
    isLandscape,

    // 特殊属性
    isTouchDevice,
    isRetina,

    // 断点检测
    isBreakpoint,
    isBreakpointUp,
    isBreakpointDown,

    // 常用断点快捷方式
    isXs: isBreakpoint('xs'),
    isSm: isBreakpoint('sm'),
    isMd: isBreakpoint('md'),
    isLg: isBreakpoint('lg'),
    isXl: isBreakpoint('xl'),
    is2Xl: isBreakpoint('2xl'),

    // 范围检测
    isSmUp: isBreakpointUp('sm'),
    isMdUp: isBreakpointUp('md'),
    isLgUp: isBreakpointUp('lg'),
    isXlUp: isBreakpointUp('xl'),

    isSmDown: isBreakpointDown('sm'),
    isMdDown: isBreakpointDown('md'),
    isLgDown: isBreakpointDown('lg'),
    isXlDown: isBreakpointDown('xl'),
  };
};

/**
 * 媒体查询Hook
 */
export const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);

    const handler = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    mediaQuery.addEventListener('change', handler);
    return () => mediaQuery.removeEventListener('change', handler);
  }, [query]);

  return matches;
};

/**
 * 视口尺寸Hook
 */
export const useViewport = () => {
  const [viewport, setViewport] = useState(() => ({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  }));

  useEffect(() => {
    const updateViewport = performance.throttle(() => {
      setViewport({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }, 100);

    window.addEventListener('resize', updateViewport);
    window.addEventListener('orientationchange', updateViewport);

    return () => {
      window.removeEventListener('resize', updateViewport);
      window.removeEventListener('orientationchange', updateViewport);
    };
  }, []);

  return viewport;
};

/**
 * 触摸设备优化Hook
 */
export const useTouchOptimization = () => {
  const { isTouchDevice } = useResponsive();

  useEffect(() => {
    if (!isTouchDevice) return;

    // 添加触摸设备专用样式
    document.documentElement.classList.add('touch-device');

    // 优化触摸滚动
    const style = document.createElement('style');
    style.textContent = `
      .touch-device * {
        -webkit-tap-highlight-color: transparent;
      }

      .touch-device .scrollable {
        -webkit-overflow-scrolling: touch;
        overflow-scrolling: touch;
      }

      .touch-active {
        opacity: 0.7;
        transform: scale(0.98);
        transition: all 0.15s ease;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.documentElement.classList.remove('touch-device');
      document.head.removeChild(style);
    };
  }, [isTouchDevice]);

  return { isTouchDevice };
};

/**
 * 响应式字体大小Hook
 */
export const useResponsiveFontSize = () => {
  const { deviceInfo } = useResponsive();

  const getFontSize = useCallback((
    mobile: string,
    tablet: string,
    desktop: string
  ) => {
    switch (deviceInfo.type) {
      case 'mobile':
        return mobile;
      case 'tablet':
        return tablet;
      case 'desktop':
        return desktop;
      default:
        return desktop;
    }
  }, [deviceInfo.type]);

  return { getFontSize };
};

/**
 * 响应式间距Hook
 */
export const useResponsiveSpacing = () => {
  const { deviceInfo } = useResponsive();

  const getSpacing = useCallback((
    mobile: string,
    tablet: string,
    desktop: string
  ) => {
    switch (deviceInfo.type) {
      case 'mobile':
        return mobile;
      case 'tablet':
        return tablet;
      case 'desktop':
        return desktop;
      default:
        return desktop;
    }
  }, [deviceInfo.type]);

  return { getSpacing };
};

/**
 * 响应式网格Hook
 */
export const useResponsiveGrid = () => {
  const { deviceInfo } = useResponsive();

  const getColumns = useCallback((
    mobile: number,
    tablet: number,
    desktop: number
  ) => {
    switch (deviceInfo.type) {
      case 'mobile':
        return mobile;
      case 'tablet':
        return tablet;
      case 'desktop':
        return desktop;
      default:
        return desktop;
    }
  }, [deviceInfo.type]);

  return { getColumns };
};
