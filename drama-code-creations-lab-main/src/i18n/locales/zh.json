{"nav": {"home": "首页", "services": "服务", "tools": "工具", "contact": "联系我们"}, "hero": {"title": "Drama Code | 抓马代码", "subtitle": "专业定制化技术服务", "description": "我们专注于为您提供高质量的网站搭建、脚本开发、小程序制作等技术服务，让您的创意想法变成现实。", "cta": "免费咨询", "contact": "立即联系"}, "services": {"title": "我们的服务", "subtitle": "专业的技术解决方案，满足您的各种需求", "website": {"title": "网站搭建", "description": "从企业官网到电商平台，我们提供全栈开发服务，包括响应式设计、SEO优化、性能调优等。使用现代化技术栈，确保网站快速、安全、易维护。", "features": ["响应式设计，完美适配各种设备", "SEO优化，提升搜索引擎排名", "高性能架构，确保快速加载", "安全防护，保障数据安全", "后期维护，持续技术支持"]}, "script": {"title": "脚本开发", "description": "自动化脚本开发，提升工作效率。支持数据采集、批量处理、系统集成等各种自动化需求。使用Python、Node.js等技术实现。", "features": ["数据采集与处理自动化", "批量操作脚本定制", "API接口集成开发", "定时任务调度系统", "错误处理与日志记录"]}, "miniprogram": {"title": "小程序制作", "description": "微信小程序、支付宝小程序等多平台开发。从UI设计到功能实现，提供一站式小程序解决方案，助力您的业务快速上线。", "features": ["多平台小程序开发", "原生性能优化", "用户体验设计", "支付功能集成", "数据统计分析"]}}, "process": {"title": "服务流程", "subtitle": "专业的服务流程，确保项目高质量交付", "steps": [{"title": "需求沟通", "description": "深入了解您的需求，分析项目可行性，制定初步方案"}, {"title": "方案设计", "description": "制定详细的技术方案和项目计划，确定开发周期和里程碑"}, {"title": "开发实施", "description": "按照方案进行开发，定期汇报进度，及时沟通调整"}, {"title": "测试验收", "description": "全面测试功能，修复问题，确保项目质量达到预期"}, {"title": "部署上线", "description": "协助项目部署上线，提供使用培训和技术文档"}, {"title": "后期维护", "description": "提供技术支持和维护服务，确保系统稳定运行"}]}, "tools": {"title": "开发工具", "subtitle": "我们使用最新的技术栈和工具，确保项目的现代化和可维护性"}, "contact": {"title": "联系我们", "telegram": "Telegram 联系", "wechat": "微信咨询", "email": "邮件联系"}, "footer": {"copyright": "© 2024 Drama Code. 保留所有权利。", "description": "专业的技术服务提供商"}, "common": {"learnMore": "了解更多", "getStarted": "开始使用", "viewDetails": "查看详情", "close": "关闭"}}