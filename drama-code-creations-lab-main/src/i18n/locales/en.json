{"nav": {"home": "Home", "services": "Services", "tools": "Tools", "contact": "Contact"}, "hero": {"title": "Drama Code", "subtitle": "Professional Custom Tech Services", "description": "We specialize in providing high-quality website development, script automation, mini-program creation, and other technical services to bring your creative ideas to life.", "cta": "Free Consultation", "contact": "Contact Now"}, "services": {"title": "Our Services", "subtitle": "Professional technical solutions to meet all your needs", "website": {"title": "Website Development", "description": "From corporate websites to e-commerce platforms, we provide full-stack development services including responsive design, SEO optimization, and performance tuning. Using modern tech stacks to ensure fast, secure, and maintainable websites.", "features": ["Responsive design for perfect device compatibility", "SEO optimization to improve search rankings", "High-performance architecture for fast loading", "Security protection for data safety", "Ongoing maintenance and technical support"]}, "script": {"title": "Script Development", "description": "Automation script development to boost work efficiency. Supporting data collection, batch processing, system integration, and various automation needs using Python, Node.js, and other technologies.", "features": ["Data collection and processing automation", "Custom batch operation scripts", "API integration development", "Scheduled task systems", "Error handling and logging"]}, "miniprogram": {"title": "Mini-Program Development", "description": "Multi-platform development for WeChat Mini Programs, Alipay Mini Programs, and more. From UI design to feature implementation, we provide one-stop mini-program solutions to help your business launch quickly.", "features": ["Multi-platform mini-program development", "Native performance optimization", "User experience design", "Payment system integration", "Data analytics and statistics"]}, "mobile": {"title": "Mobile App Development", "description": "Professional iOS and Android native application development services. From product design to app store deployment, we provide comprehensive mobile application solutions.", "features": ["iOS/Android native development", "Cross-platform Flutter/React Native", "UI/UX design and optimization", "App store submission guidance", "Performance optimization and security"]}}, "process": {"title": "Service Process", "subtitle": "Professional service workflow ensuring high-quality project delivery", "steps": [{"title": "Requirements Analysis", "description": "Deep understanding of your needs, feasibility analysis, and initial solution planning"}, {"title": "Solution Design", "description": "Detailed technical solution and project planning, defining development timeline and milestones"}, {"title": "Development Implementation", "description": "Development according to the plan, regular progress reports, and timely communication for adjustments"}, {"title": "Testing & Acceptance", "description": "Comprehensive functionality testing, issue resolution, ensuring project quality meets expectations"}, {"title": "Deployment & Launch", "description": "Assistance with project deployment and launch, providing user training and technical documentation"}, {"title": "Ongoing Maintenance", "description": "Technical support and maintenance services to ensure stable system operation"}]}, "tools": {"title": "Technical Tools", "subtitle": "We use the latest tech stacks and tools to ensure project modernization and maintainability", "badge": "Tech Showcase", "categories": {"all": "All Tools", "development": "Development", "data": "Data Processing", "automation": "Automation", "utilities": "Utilities"}}, "contact": {"title": "Contact Us", "telegram": "Contact via Telegram", "wechat": "WeChat Consultation", "email": "Email Contact"}, "footer": {"copyright": "© 2024 Drama Code. All rights reserved.", "description": "Professional technical service provider"}, "common": {"learnMore": "Learn More", "getStarted": "Get Started", "viewDetails": "View Details", "close": "Close"}}