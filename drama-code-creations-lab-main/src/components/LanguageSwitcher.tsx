import { Globe } from 'lucide-react';
import React from 'react';
import { useTranslation } from 'react-i18next';

const LanguageSwitcher: React.FC = () => {
  const { i18n } = useTranslation();

  const toggleLanguage = () => {
    const newLang = i18n.language === 'zh' ? 'en' : 'zh';
    i18n.changeLanguage(newLang);
  };

  return (
    <button
      onClick={toggleLanguage}
      className="relative flex items-center space-x-2 px-4 py-2 rounded-lg bg-background/50 border border-primary/20 hover:border-primary/50 backdrop-blur-sm transition-all duration-300 group tech-glow"
      aria-label="Switch Language"
    >
      <Globe className="h-4 w-4 text-primary group-hover:rotate-12 transition-transform duration-300" />
      <span className="text-sm font-medium text-foreground group-hover:text-primary transition-colors duration-300">
        {i18n.language === 'zh' ? 'EN' : '中文'}
      </span>
      {/* 科技感光效 */}
      <div className="absolute inset-0 bg-gradient-primary/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
    </button>
  );
};

export default LanguageSwitcher;
