import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ExternalLink, 
  Github, 
  Calendar, 
  Users, 
  Zap,
  Globe,
  Smartphone,
  Code2,
  Database,
  Palette,
  TrendingUp
} from 'lucide-react';
import TelegramModal from './TelegramModal';

interface Project {
  id: string;
  title: string;
  description: string;
  category: string;
  image: string;
  technologies: string[];
  features: string[];
  duration: string;
  client: string;
  status: 'completed' | 'ongoing' | 'maintenance';
  results?: string[];
}

const Portfolio = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showTelegramModal, setShowTelegramModal] = useState(false);

  const categories = [
    { id: 'all', name: '全部项目', icon: <Globe className="w-4 h-4" /> },
    { id: 'website', name: '网站开发', icon: <Globe className="w-4 h-4" /> },
    { id: 'miniprogram', name: '小程序', icon: <Smartphone className="w-4 h-4" /> },
    { id: 'script', name: '脚本开发', icon: <Code2 className="w-4 h-4" /> },
    { id: 'system', name: '系统开发', icon: <Database className="w-4 h-4" /> },
  ];

  const projects: Project[] = [
    {
      id: 'ecommerce-platform',
      title: '智能电商平台',
      description: '为中小企业打造的全功能电商解决方案，集成支付、物流、客服等完整生态',
      category: 'website',
      image: '/placeholder.svg',
      technologies: ['React', 'Node.js', 'MongoDB', 'Redis', 'Docker'],
      features: ['用户管理', '商品管理', '订单系统', '支付集成', '数据分析'],
      duration: '3个月',
      client: '某科技公司',
      status: 'completed',
      results: ['销售额提升300%', '用户转化率提升45%', '运营效率提升60%']
    },
    {
      id: 'restaurant-miniprogram',
      title: '餐厅点餐小程序',
      description: '智能餐厅点餐系统，支持扫码点餐、在线支付、会员管理等功能',
      category: 'miniprogram',
      image: '/placeholder.svg',
      technologies: ['微信小程序', 'Node.js', 'MySQL', '微信支付'],
      features: ['扫码点餐', '在线支付', '会员系统', '营销活动', '数据统计'],
      duration: '6周',
      client: '连锁餐厅',
      status: 'completed',
      results: ['点餐效率提升80%', '客户满意度95%', '营业额增长25%']
    },
    {
      id: 'data-crawler',
      title: '智能数据采集系统',
      description: '高效的网络数据采集和处理系统，支持多源数据整合和实时分析',
      category: 'script',
      image: '/placeholder.svg',
      technologies: ['Python', 'Scrapy', 'Redis', 'PostgreSQL', 'Docker'],
      features: ['多源采集', '数据清洗', '实时处理', '智能去重', '可视化分析'],
      duration: '2个月',
      client: '数据分析公司',
      status: 'ongoing',
      results: ['数据准确率99.5%', '处理速度提升10倍']
    },
    {
      id: 'crm-system',
      title: '客户关系管理系统',
      description: '企业级CRM系统，帮助企业更好地管理客户关系，提升销售效率',
      category: 'system',
      image: '/placeholder.svg',
      technologies: ['Vue.js', 'Spring Boot', 'MySQL', 'Redis', 'ElasticSearch'],
      features: ['客户管理', '销售跟进', '数据分析', '报表生成', '权限管理'],
      duration: '4个月',
      client: '销售公司',
      status: 'maintenance',
      results: ['销售效率提升50%', '客户满意度提升30%']
    },
    {
      id: 'portfolio-website',
      title: '创意设计师作品集',
      description: '为设计师打造的个人作品展示网站，响应式设计，SEO优化',
      category: 'website',
      image: '/placeholder.svg',
      technologies: ['Next.js', 'TypeScript', 'Tailwind CSS', 'Framer Motion'],
      features: ['作品展示', '响应式设计', 'SEO优化', '动画效果', '联系表单'],
      duration: '4周',
      client: '自由设计师',
      status: 'completed',
      results: ['访问量提升200%', '客户询盘增长150%']
    },
    {
      id: 'automation-script',
      title: '办公自动化脚本集',
      description: '企业办公自动化解决方案，包含文档处理、数据统计、报告生成等功能',
      category: 'script',
      image: '/placeholder.svg',
      technologies: ['Python', 'Pandas', 'Openpyxl', 'Selenium', 'Schedule'],
      features: ['文档自动化', '数据统计', '报告生成', '邮件发送', '定时任务'],
      duration: '6周',
      client: '制造企业',
      status: 'completed',
      results: ['工作效率提升70%', '错误率降低90%']
    }
  ];

  const filteredProjects = selectedCategory === 'all' 
    ? projects 
    : projects.filter(project => project.category === selectedCategory);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/50">已完成</Badge>;
      case 'ongoing':
        return <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/50">进行中</Badge>;
      case 'maintenance':
        return <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/50">维护中</Badge>;
      default:
        return null;
    }
  };

  return (
    <section id="portfolio" className="py-20 bg-gradient-to-b from-background to-muted/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <div className="inline-flex items-center px-4 py-2 bg-primary/10 border border-primary/20 rounded-full text-primary text-sm font-medium mb-6">
            <Palette className="w-4 h-4 mr-2" />
            项目案例
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            成功<span className="bg-gradient-primary bg-clip-text text-transparent">项目案例</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            我们为各行各业的客户提供专业的技术解决方案
            <br />
            每个项目都是我们技术实力和服务质量的体现
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              onClick={() => setSelectedCategory(category.id)}
              className={`transition-all duration-300 ${
                selectedCategory === category.id 
                  ? 'bg-gradient-primary shadow-glow' 
                  : 'hover:border-primary/50'
              }`}
            >
              {category.icon}
              <span className="ml-2">{category.name}</span>
            </Button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map((project) => (
            <Card key={project.id} className="group relative overflow-hidden bg-card/50 backdrop-blur-sm border-border hover:border-primary/50 transition-all duration-500 hover:shadow-glow hover:scale-105">
              <CardContent className="p-0">
                {/* Project Image */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={project.image}
                    alt={project.title}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent" />
                  <div className="absolute top-4 right-4">
                    {getStatusBadge(project.status)}
                  </div>
                  <div className="absolute bottom-4 left-4">
                    <div className="flex items-center space-x-2 text-white">
                      <Calendar className="w-4 h-4" />
                      <span className="text-sm">{project.duration}</span>
                    </div>
                  </div>
                </div>

                {/* Project Content */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-foreground mb-2 group-hover:text-primary transition-colors duration-300">
                    {project.title}
                  </h3>
                  <p className="text-muted-foreground mb-4 leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">
                    {project.description}
                  </p>

                  {/* Technologies */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {project.technologies.slice(0, 3).map((tech, index) => (
                      <Badge key={index} variant="outline" className="text-xs border-primary/30 text-primary/80">
                        {tech}
                      </Badge>
                    ))}
                    {project.technologies.length > 3 && (
                      <Badge variant="outline" className="text-xs border-primary/30 text-primary/80">
                        +{project.technologies.length - 3}
                      </Badge>
                    )}
                  </div>

                  {/* Results */}
                  {project.results && (
                    <div className="space-y-2 mb-4">
                      {project.results.slice(0, 2).map((result, index) => (
                        <div key={index} className="flex items-center text-sm text-green-400">
                          <TrendingUp className="w-3 h-3 mr-2 flex-shrink-0" />
                          {result}
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Client Info */}
                  <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                    <div className="flex items-center">
                      <Users className="w-4 h-4 mr-1" />
                      {project.client}
                    </div>
                  </div>

                  {/* Action Button */}
                  <Button
                    onClick={() => setShowTelegramModal(true)}
                    variant="outline"
                    className="w-full border-primary/30 hover:bg-gradient-primary/10 hover:border-primary transition-all duration-300 group/btn"
                  >
                    <ExternalLink className="w-4 h-4 mr-2 group-hover/btn:translate-x-1 transition-transform duration-300" />
                    了解详情
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16 p-8 bg-gradient-to-r from-primary/10 to-accent/10 rounded-2xl border border-primary/20">
          <h3 className="text-2xl font-bold text-foreground mb-4">
            让我们为您创造下一个成功案例
          </h3>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            无论您的项目规模大小，我们都能提供专业的技术解决方案
          </p>
          <Button
            onClick={() => setShowTelegramModal(true)}
            size="lg"
            className="bg-gradient-primary hover:shadow-glow hover:scale-105 transition-all duration-300"
          >
            <Zap className="w-5 h-5 mr-2" />
            开始您的项目
          </Button>
        </div>
      </div>

      {/* Telegram Modal */}
      <TelegramModal 
        isOpen={showTelegramModal} 
        onClose={() => setShowTelegramModal(false)} 
      />
    </section>
  );
};

export default Portfolio;
