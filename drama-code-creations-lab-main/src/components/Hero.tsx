import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, <PERSON>, Zap, Spark<PERSON>, <PERSON>, Heart } from 'lucide-react';
import heroBg from '@/assets/hero-bg.jpg';
import TelegramModal from './TelegramModal';

const Hero = () => {
  const [showTelegramModal, setShowTelegramModal] = useState(false);

  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
      style={{
        backgroundImage: `url(${heroBg})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed'
      }}
    >
      {/* Enhanced Overlay with pulsing effect */}
      <div className="absolute inset-0 bg-background/90 animate-pulse-slow" />
      
      {/* More Dynamic Floating Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-20 h-20 bg-primary/30 rounded-full animate-float blur-sm" />
        <div className="absolute top-40 right-20 w-16 h-16 bg-accent/30 rounded-full animate-float blur-sm" style={{ animationDelay: '1s' }} />
        <div className="absolute bottom-40 left-20 w-12 h-12 bg-primary-glow/30 rounded-full animate-float blur-sm" style={{ animationDelay: '2s' }} />
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-accent/20 rounded-full animate-float blur-sm" style={{ animationDelay: '0.5s' }} />
        <div className="absolute top-1/2 left-1/4 w-8 h-8 bg-primary/40 rounded-full animate-float" style={{ animationDelay: '3s' }} />
        <div className="absolute top-1/3 right-1/3 w-6 h-6 bg-accent/40 rounded-full animate-float" style={{ animationDelay: '1.5s' }} />
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="animate-fade-in">
          {/* Enhanced Badge with pulsing icon */}
          <div className="inline-flex items-center px-6 py-3 bg-gradient-primary/20 border-2 border-primary/30 rounded-full text-primary text-sm font-medium mb-8 hover:bg-gradient-primary/30 transition-all duration-300 group">
            <Sparkles className="w-4 h-4 mr-2 animate-pulse group-hover:animate-bounce" />
            专业定制化开发服务
            <Heart className="w-4 h-4 ml-2 text-red-400 animate-pulse" />
          </div>

          {/* Enhanced Main Heading with more dynamic styling */}
          <h1 className="text-4xl md:text-6xl lg:text-8xl font-bold mb-8 leading-tight">
            <span className="block text-foreground hover:scale-105 transition-transform duration-300 cursor-default">
              Drama Code
            </span>
            <span className="block bg-gradient-primary bg-clip-text text-transparent hover:animate-glow transition-all duration-300 cursor-default">
              抓马代码
            </span>
          </h1>

          {/* Enhanced Subtitle */}
          <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-4xl mx-auto leading-relaxed">
            <span className="inline-block hover:text-primary transition-colors duration-300">专注于网站搭建、脚本开发、小程序制作等定制化技术服务</span>
            <br />
            <span className="inline-block hover:text-accent transition-colors duration-300">让您的想法变成现实，让技术为您的业务赋能</span>
          </p>

          {/* Enhanced CTA Buttons with more dramatic effects */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <Button
              size="lg"
              onClick={() => setShowTelegramModal(true)}
              className="bg-gradient-primary hover:shadow-glow hover:scale-110 transition-all duration-500 text-lg px-10 py-4 group relative overflow-hidden"
            >
              <div className="absolute inset-0 bg-white/20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
              <Rocket className="mr-3 h-6 w-6 transition-transform duration-300 group-hover:translate-x-1 group-hover:rotate-12" />
              开始合作
              <ArrowRight className="ml-3 h-6 w-6 transition-transform duration-300 group-hover:translate-x-2" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() => setShowTelegramModal(true)}
              className="border-2 border-primary/50 hover:border-primary hover:bg-gradient-primary/10 hover:scale-105 text-lg px-10 py-4 transition-all duration-500 group relative overflow-hidden"
            >
              <div className="absolute inset-0 bg-primary/10 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
              <Code className="mr-3 h-6 w-6 transition-transform duration-300 group-hover:rotate-180" />
              联系我们
            </Button>
          </div>

          {/* Enhanced floating call to action */}
          <div className="mt-16 animate-bounce">
            <p className="text-primary font-medium text-lg animate-pulse">
              💫 专业 · 高效 · 创新 💫
            </p>
          </div>
        </div>
      </div>

      {/* Enhanced Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-primary/70 rounded-full flex justify-center hover:border-primary transition-colors duration-300">
          <div className="w-1 h-3 bg-gradient-primary rounded-full mt-2 animate-pulse-slow" />
        </div>
      </div>

      {/* Telegram Modal */}
      <TelegramModal 
        isOpen={showTelegramModal} 
        onClose={() => setShowTelegramModal(false)} 
      />
    </section>
  );
};

export default Hero;