import { Button } from '@/components/ui/button';
import { ArrowR<PERSON>, Code, Rocket } from 'lucide-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import TelegramModal from './TelegramModal';

const Hero = () => {
  const { t } = useTranslation();
  const [showTelegramModal, setShowTelegramModal] = useState(false);

  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-background"
    >
      {/* 简洁的科技感背景 */}
      <div className="absolute inset-0">
        {/* 主背景渐变 */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-primary/5 to-accent/5"></div>

        {/* 简化的图案背景 */}
        <div className="absolute inset-0 opacity-5">
          <div className="w-full h-full grid-pattern"></div>
        </div>

        {/* 简洁的装饰元素 */}
        <div className="absolute top-20 left-20 w-2 h-2 bg-primary rounded-full animate-pulse"></div>
        <div className="absolute top-40 right-32 w-1.5 h-1.5 bg-accent rounded-full animate-ping"></div>
        <div className="absolute bottom-32 left-32 w-1 h-1 bg-primary rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-20 right-20 w-2.5 h-2.5 bg-accent/50 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="animate-fade-in">
          {/* 简洁的标识徽章 */}
          <div className="inline-flex items-center px-4 py-2 bg-primary/10 border border-primary/20 rounded-full text-primary text-sm font-medium mb-8 hover:bg-primary/20 transition-all duration-300">
            <Code className="w-4 h-4 mr-2" />
            {t('hero.badge')}
          </div>

          {/* 清晰的主标题 */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
            <span className="block text-foreground mb-2">
              {t('hero.title')}
            </span>
            <span className="block bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              {t('hero.subtitle')}
            </span>
          </h1>

          {/* 简洁的描述 */}
          <p className="text-lg md:text-xl text-muted-foreground mb-10 max-w-3xl mx-auto leading-relaxed">
            {t('hero.description1')}
            <br />
            {t('hero.description2')}
          </p>

          {/* 简洁的行动按钮 */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              size="lg"
              onClick={() => setShowTelegramModal(true)}
              className="bg-gradient-primary hover:shadow-lg hover:scale-105 transition-all duration-300 text-lg px-8 py-3 group"
            >
              <Rocket className="mr-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
              {t('hero.cta')}
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() => setShowTelegramModal(true)}
              className="border-2 border-primary/50 hover:border-primary hover:bg-primary/10 hover:scale-105 text-lg px-8 py-3 transition-all duration-300"
            >
              <Code className="mr-2 h-5 w-5" />
              {t('hero.contact')}
            </Button>
          </div>
        </div>
      </div>



      {/* Telegram Modal */}
      <TelegramModal
        isOpen={showTelegramModal}
        onClose={() => setShowTelegramModal(false)}
      />
    </section>
  );
};

export default Hero;
