import {
    Code,
    FileText,
    MessageCircle,
    Rocket,
    Settings,
    TestTube
} from 'lucide-react';
import React from 'react';
import { useTranslation } from 'react-i18next';

const ServiceProcess: React.FC = () => {
  const { t } = useTranslation();

  const icons = [
    MessageCircle,
    FileText,
    Code,
    TestTube,
    Rocket,
    Settings
  ];

  const steps = t('process.steps', { returnObjects: true }) as Array<{
    title: string;
    description: string;
  }>;

  return (
    <section className="relative py-20 bg-background overflow-hidden">
      {/* 科技感背景 */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-accent/5 to-background"></div>
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full hex-pattern"></div>
        </div>
        {/* 光效线条 */}
        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-accent/30 to-transparent"></div>
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary/30 to-transparent"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-fade-in">
          <div className="inline-flex items-center px-6 py-3 bg-accent/10 border-2 border-accent/20 rounded-full text-accent text-sm font-medium mb-6 hover:bg-accent/20 transition-all duration-300 group">
            <Settings className="w-4 h-4 mr-2 group-hover:rotate-12 transition-transform duration-300" />
            专业流程
          </div>
          <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-foreground to-accent bg-clip-text text-transparent">
            {t('process.title')}
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            {t('process.subtitle')}
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {steps.map((step, index) => {
              const IconComponent = icons[index];
              return (
                <div
                  key={index}
                  className="group relative bg-card/50 backdrop-blur-sm border border-border hover:border-accent/50 rounded-2xl p-8 transition-all duration-500 hover:shadow-glow hover:scale-105 animate-fade-in tech-glow"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {/* 科技感步骤编号 */}
                  <div className="absolute -top-4 -left-4 w-12 h-12 bg-gradient-to-r from-accent to-primary rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg group-hover:animate-pulse">
                    {index + 1}
                  </div>

                  {/* 科技感图标 */}
                  <div className="mb-6 pt-4">
                    <div className="w-16 h-16 bg-gradient-to-r from-accent/20 to-primary/20 rounded-2xl flex items-center justify-center group-hover:bg-gradient-to-r group-hover:from-accent/30 group-hover:to-primary/30 transition-all duration-300">
                      <IconComponent className="h-8 w-8 text-accent group-hover:text-primary transition-colors duration-300 group-hover:scale-110" />
                    </div>
                  </div>

                  {/* 内容 */}
                  <h3 className="text-xl font-bold text-foreground mb-4 group-hover:text-accent transition-colors duration-300">
                    {step.title}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">
                    {step.description}
                  </p>

                  {/* 科技感连接线 */}
                  {index < steps.length - 1 && (
                    <div className="hidden lg:block absolute top-1/2 -right-4 w-8 h-px bg-gradient-to-r from-accent/50 to-primary/50 transform -translate-y-1/2 animate-pulse"></div>
                  )}

                  {/* 装饰性粒子 */}
                  <div className="absolute top-4 right-4 w-2 h-2 bg-accent rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-ping transition-opacity duration-300"></div>
                </div>
              );
            })}
          </div>
        </div>

        {/* 科技感CTA区域 */}
        <div className="text-center mt-16 animate-fade-in" style={{ animationDelay: '0.6s' }}>
          <div className="relative bg-card/50 backdrop-blur-sm border border-primary/20 rounded-2xl p-8 max-w-2xl mx-auto tech-glow group hover:border-primary/50 transition-all duration-500">
            {/* 背景效果 */}
            <div className="absolute inset-0 bg-gradient-primary/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

            <div className="relative z-10">
              <h3 className="text-2xl font-bold text-foreground mb-4 group-hover:text-primary transition-colors duration-300">
                准备开始您的项目了吗？
              </h3>
              <p className="text-muted-foreground mb-6 group-hover:text-foreground/80 transition-colors duration-300">
                联系我们获取免费咨询和项目报价
              </p>
              <button className="relative bg-gradient-primary text-primary-foreground px-8 py-3 rounded-full font-semibold hover:shadow-glow transition-all duration-300 transform hover:scale-105 overflow-hidden group/btn">
                <div className="absolute inset-0 bg-gradient-to-r from-primary to-accent opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>
                <span className="relative z-10">{t('hero.cta')}</span>
              </button>
            </div>

            {/* 装饰性元素 */}
            <div className="absolute top-4 left-4 w-2 h-2 bg-primary rounded-full animate-pulse"></div>
            <div className="absolute bottom-4 right-4 w-1.5 h-1.5 bg-accent rounded-full animate-ping"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServiceProcess;
