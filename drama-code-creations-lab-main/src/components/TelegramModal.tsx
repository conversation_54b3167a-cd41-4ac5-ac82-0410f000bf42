import React from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Send, Copy, ExternalLink } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface TelegramModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const TelegramModal: React.FC<TelegramModalProps> = ({ isOpen, onClose }) => {
  const { toast } = useToast();

  const telegramUrl = 'https://t.me/ZhuaMaCode';
  const telegramUsername = '@ZHUAMACODE';

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "已复制到剪贴板",
      description: "Telegram联系方式已复制",
    });
  };

  const openTelegram = () => {
    window.open(telegramUrl, '_blank');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md mx-auto bg-card/95 backdrop-blur-lg border-primary/20">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl font-bold bg-gradient-primary bg-clip-text text-transparent">
            联系我们
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          {/* QR Code */}
          <div className="flex justify-center">
            <div className="relative">
              <img
                src="/lovable-uploads/49b2daf0-5632-4ea8-9987-23f4f03c5173.png"
                alt="Telegram QR Code"
                className="w-48 h-48 rounded-2xl shadow-glow animate-glow"
              />
              <div className="absolute inset-0 bg-gradient-primary opacity-10 rounded-2xl"></div>
            </div>
          </div>

          {/* Contact Info */}
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center space-x-2">
              <Send className="w-5 h-5 text-primary animate-pulse" />
              <span className="text-lg font-semibold text-foreground">Telegram</span>
            </div>
            
            <div className="bg-muted/50 rounded-lg p-4 border border-primary/20">
              <p className="text-sm text-muted-foreground mb-2">用户名</p>
              <div className="flex items-center justify-between">
                <code className="text-primary font-mono text-lg">{telegramUsername}</code>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyToClipboard(telegramUsername)}
                  className="text-primary hover:bg-primary/10"
                >
                  <Copy className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <div className="bg-muted/50 rounded-lg p-4 border border-primary/20">
              <p className="text-sm text-muted-foreground mb-2">直接链接</p>
              <div className="flex items-center justify-between">
                <code className="text-primary font-mono text-sm break-all">{telegramUrl}</code>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyToClipboard(telegramUrl)}
                  className="text-primary hover:bg-primary/10 ml-2"
                >
                  <Copy className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Important Note */}
          <div className="bg-primary/10 border border-primary/30 rounded-lg p-4">
            <p className="text-center text-primary font-medium">
              💬 任何定制化需求请Telegram沟通
            </p>
            <p className="text-center text-sm text-muted-foreground mt-1">
              我们会在第一时间回复您的消息
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col space-y-3">
            <Button
              onClick={openTelegram}
              className="bg-gradient-primary hover:shadow-glow transition-all duration-300 group"
              size="lg"
            >
              <Send className="w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform duration-300" />
              打开 Telegram
              <ExternalLink className="w-4 h-4 ml-2" />
            </Button>
            
            <Button
              variant="outline"
              onClick={onClose}
              className="border-primary/30 hover:bg-primary/5"
            >
              稍后联系
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TelegramModal;