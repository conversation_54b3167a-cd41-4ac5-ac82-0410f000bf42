import { Button } from '@/components/ui/button';
import { Menu, X, Zap } from 'lucide-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from './LanguageSwitcher';
import TelegramModal from './TelegramModal';

const Navbar = () => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [showTelegramModal, setShowTelegramModal] = useState(false);

  const navItems = [
    { name: t('nav.home'), href: '#home' },
    { name: t('nav.services'), href: '#services' },
    { name: t('nav.tools'), href: '#tools' },
  ];

  // 键盘导航处理
  const handleKeyDown = (event: React.KeyboardEvent, action: () => void) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      action();
    }
  };

  // ESC键关闭移动菜单
  const handleEscapeKey = (event: React.KeyboardEvent) => {
    if (event.key === 'Escape' && isOpen) {
      setIsOpen(false);
    }
  };

  return (
    <>
      <nav className="fixed top-0 w-full z-50 bg-background/90 backdrop-blur-md border-b border-primary/20 tech-glow">
        {/* 科技感背景效果 */}
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-accent/5"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Enhanced Logo */}
            <div className="flex items-center space-x-3">
              <picture>
                <source srcSet="/logo-64.webp" media="(max-width: 768px)" />
                <source srcSet="/logo-128.webp" media="(max-width: 1024px)" />
                <img
                  src="/logo-128.webp"
                  alt="Drama Code Logo - 专业定制化技术服务"
                  className="h-10 w-10 object-contain hover:scale-110 transition-transform duration-300"
                  loading="eager"
                  width="40"
                  height="40"
                  decoding="async"
                />
              </picture>
              <div className="flex flex-col">
                <span
                  className="text-xl font-bold bg-gradient-primary bg-clip-text text-transparent hover:scale-105 transition-transform duration-300 cursor-default"
                  role="heading"
                  aria-level={1}
                >
                  Drama Code
                </span>
                <span className="text-xs text-muted-foreground" aria-label="中文名称">抓马代码</span>
              </div>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="text-foreground hover:text-primary transition-colors duration-300 relative group"
                >
                  {item.name}
                  <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-primary transition-all duration-300 group-hover:w-full"></span>
                </a>
              ))}

              <LanguageSwitcher />

              <Button
                variant="default"
                onClick={() => setShowTelegramModal(true)}
                className="bg-gradient-primary hover:shadow-glow hover:scale-105 transition-all duration-300 group relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-white/20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                <Zap className="w-4 h-4 mr-2 group-hover:rotate-12 transition-transform duration-300" />
                {t('hero.cta')}
              </Button>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden flex items-center space-x-2">
              <LanguageSwitcher />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(!isOpen)}
                onKeyDown={(e) => handleKeyDown(e, () => setIsOpen(!isOpen))}
                className="text-foreground hover:text-primary"
                aria-label={isOpen ? "关闭导航菜单" : "打开导航菜单"}
                aria-expanded={isOpen}
                aria-controls="mobile-menu"
              >
                {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </Button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isOpen && (
            <div
              className="md:hidden animate-fade-in"
              id="mobile-menu"
              role="navigation"
              aria-label="移动端导航菜单"
              onKeyDown={handleEscapeKey}
            >
              <div className="px-2 pt-2 pb-3 space-y-1 bg-card border border-border rounded-lg mt-2">
                {navItems.map((item) => (
                  <a
                    key={item.name}
                    href={item.href}
                    className="block px-3 py-2 text-foreground hover:text-primary hover:bg-muted/50 rounded-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                    onClick={() => setIsOpen(false)}
                    onKeyDown={(e) => handleKeyDown(e, () => setIsOpen(false))}
                    tabIndex={0}
                    role="menuitem"
                  >
                    {item.name}
                  </a>
                ))}
                <div className="px-3 pt-2">
                  <Button
                    className="w-full bg-gradient-primary hover:shadow-glow transition-all duration-300"
                    onClick={() => {
                      setShowTelegramModal(true);
                      setIsOpen(false);
                    }}
                  >
                    {t('hero.cta')}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Telegram Modal */}
      <TelegramModal
        isOpen={showTelegramModal}
        onClose={() => setShowTelegramModal(false)}
      />
    </>
  );
};

export default Navbar;
