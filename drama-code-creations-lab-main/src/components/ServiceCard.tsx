import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowRight, Zap } from 'lucide-react';
import React, { useState } from 'react';
import TelegramModal from './TelegramModal';

interface ServiceCardProps {
  title: string;
  description: string;
  features: string[];
  image: string;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ title, description, features, image }) => {
  const [showTelegramModal, setShowTelegramModal] = useState(false);

  return (
    <>
      <Card className="group relative overflow-hidden bg-card/50 backdrop-blur-sm border-border hover:border-primary/50 transition-all duration-500 hover:shadow-glow hover:scale-105">
        <CardContent className="p-0">
          {/* Enhanced Image with glow effect */}
          <div className="relative h-48 overflow-hidden">
            <img
              src={image}
              alt={title}
              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent" />
            <div className="absolute inset-0 bg-gradient-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

          </div>

          {/* Enhanced Content */}
          <div className="p-6">
            <h3 className="text-xl font-bold text-foreground mb-3 group-hover:text-primary transition-colors duration-300 flex items-center">
              {title}
              <Zap className="ml-2 w-5 h-5 opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-all duration-300" />
            </h3>
            <p className="text-muted-foreground mb-4 leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">
              {description}
            </p>

            {/* Enhanced Features */}
            <ul className="space-y-2 mb-6">
              {features.map((feature, index) => (
                <li key={index} className="flex items-center text-sm text-muted-foreground group-hover:text-foreground/70 transition-colors duration-300">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full mr-3 flex-shrink-0 group-hover:animate-pulse" />
                  {feature}
                </li>
              ))}
            </ul>

            {/* Enhanced CTA Button */}
            <Button
              onClick={() => setShowTelegramModal(true)}
              variant="outline"
              className="w-full group/btn hover:bg-gradient-primary hover:text-primary-foreground border-primary/30 hover:border-primary transition-all duration-500 relative overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-primary transform scale-x-0 group-hover/btn:scale-x-100 transition-transform duration-500 origin-left"></div>
              <span className="relative z-10 flex items-center justify-center">
                立即咨询
                <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover/btn:translate-x-1 group-hover/btn:scale-110" />
              </span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Telegram Modal */}
      <TelegramModal
        isOpen={showTelegramModal}
        onClose={() => setShowTelegramModal(false)}
      />
    </>
  );
};

export default ServiceCard;
