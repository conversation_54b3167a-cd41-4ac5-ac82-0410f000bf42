import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowRight, Code, Globe, Monitor, Smartphone, Zap } from 'lucide-react';
import React, { useState } from 'react';
import TelegramModal from './TelegramModal';

interface ServiceCardProps {
  title: string;
  description: string;
  features: string[];
  serviceType: 'web' | 'script' | 'miniprogram' | 'mobile';
}

const ServiceCard: React.FC<ServiceCardProps> = ({ title, description, features, serviceType }) => {
  const [showTelegramModal, setShowTelegramModal] = useState(false);

  // 根据服务类型获取对应的图标和样式
  const getServiceConfig = () => {
    switch (serviceType) {
      case 'web':
        return {
          icon: Globe,
          gradient: 'from-blue-500/20 via-purple-500/20 to-cyan-500/20',
          iconColor: 'text-blue-400',
          pattern: 'web-pattern'
        };
      case 'script':
        return {
          icon: Code,
          gradient: 'from-green-500/20 via-purple-500/20 to-emerald-500/20',
          iconColor: 'text-green-400',
          pattern: 'code-pattern'
        };
      case 'miniprogram':
        return {
          icon: Smartphone,
          gradient: 'from-orange-500/20 via-purple-500/20 to-yellow-500/20',
          iconColor: 'text-orange-400',
          pattern: 'hex-pattern'
        };
      case 'mobile':
        return {
          icon: Monitor,
          gradient: 'from-red-500/20 via-purple-500/20 to-pink-500/20',
          iconColor: 'text-red-400',
          pattern: 'circle-pattern'
        };
      default:
        return {
          icon: Zap,
          gradient: 'from-purple-500/20 to-cyan-500/20',
          iconColor: 'text-purple-400',
          pattern: 'default-pattern'
        };
    }
  };

  const config = getServiceConfig();
  const IconComponent = config.icon;

  return (
    <>
      <Card className="group relative overflow-hidden bg-card/50 backdrop-blur-sm border-border hover:border-primary/50 transition-all duration-500 hover:shadow-glow hover:scale-105">
        <CardContent className="p-0">
          {/* 科技感背景设计 */}
          <div className={`relative h-48 overflow-hidden bg-gradient-to-br ${config.gradient}`}>
            {/* 几何图案背景 */}
            <div className="absolute inset-0 opacity-10">
              <div className={`w-full h-full ${config.pattern}`}></div>
            </div>

            {/* 主图标 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <IconComponent className={`w-20 h-20 ${config.iconColor} group-hover:scale-110 transition-transform duration-500`} />
            </div>

            {/* 光效和渐变叠加 */}
            <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent" />
            <div className="absolute inset-0 bg-gradient-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

            {/* 粒子效果 */}
            <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700">
              <div className="absolute top-4 left-4 w-2 h-2 bg-primary rounded-full animate-pulse"></div>
              <div className="absolute top-8 right-8 w-1 h-1 bg-accent rounded-full animate-ping"></div>
              <div className="absolute bottom-6 left-8 w-1.5 h-1.5 bg-primary rounded-full animate-bounce"></div>
            </div>
          </div>

          {/* Enhanced Content */}
          <div className="p-6">
            <h3 className="text-xl font-bold text-foreground mb-3 group-hover:text-primary transition-colors duration-300 flex items-center">
              {title}
              <Zap className="ml-2 w-5 h-5 opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-all duration-300" />
            </h3>
            <p className="text-muted-foreground mb-4 leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">
              {description}
            </p>

            {/* Enhanced Features */}
            <ul className="space-y-2 mb-6">
              {features.map((feature, index) => (
                <li key={index} className="flex items-center text-sm text-muted-foreground group-hover:text-foreground/70 transition-colors duration-300">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full mr-3 flex-shrink-0 group-hover:animate-pulse" />
                  {feature}
                </li>
              ))}
            </ul>

            {/* Enhanced CTA Button */}
            <Button
              onClick={() => setShowTelegramModal(true)}
              variant="outline"
              className="w-full group/btn hover:bg-gradient-primary hover:text-primary-foreground border-primary/30 hover:border-primary transition-all duration-500 relative overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-primary transform scale-x-0 group-hover/btn:scale-x-100 transition-transform duration-500 origin-left"></div>
              <span className="relative z-10 flex items-center justify-center">
                立即咨询
                <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover/btn:translate-x-1 group-hover/btn:scale-110" />
              </span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Telegram Modal */}
      <TelegramModal
        isOpen={showTelegramModal}
        onClose={() => setShowTelegramModal(false)}
      />
    </>
  );
};

export default ServiceCard;
