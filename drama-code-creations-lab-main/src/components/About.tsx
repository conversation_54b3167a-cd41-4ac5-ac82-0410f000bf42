import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  Target, 
  Award, 
  Clock, 
  CheckCircle, 
  Star,
  Code,
  Lightbulb,
  Shield,
  Zap,
  Heart,
  TrendingUp
} from 'lucide-react';
import TelegramModal from './TelegramModal';

const About = () => {
  const [showTelegramModal, setShowTelegramModal] = useState(false);

  const stats = [
    { icon: <Users className="w-6 h-6" />, value: '50+', label: '成功项目' },
    { icon: <Clock className="w-6 h-6" />, value: '2年+', label: '服务经验' },
    { icon: <Star className="w-6 h-6" />, value: '98%', label: '客户满意度' },
    { icon: <TrendingUp className="w-6 h-6" />, value: '24h', label: '响应时间' },
  ];

  const values = [
    {
      icon: <Code className="w-8 h-8 text-primary" />,
      title: '技术专业',
      description: '掌握最新技术栈，提供高质量的技术解决方案'
    },
    {
      icon: <Lightbulb className="w-8 h-8 text-primary" />,
      title: '创新思维',
      description: '用创新的思维解决复杂问题，为客户创造价值'
    },
    {
      icon: <Shield className="w-8 h-8 text-primary" />,
      title: '可靠保障',
      description: '严格的质量控制，确保项目按时按质交付'
    },
    {
      icon: <Heart className="w-8 h-8 text-primary" />,
      title: '用心服务',
      description: '以客户为中心，提供贴心的技术支持和服务'
    }
  ];

  const services = [
    {
      title: '需求分析',
      description: '深入了解客户需求，制定最适合的技术方案',
      features: ['业务分析', '技术评估', '方案设计', '风险评估']
    },
    {
      title: '开发实施',
      description: '采用敏捷开发模式，确保项目高效推进',
      features: ['敏捷开发', '代码审查', '测试保障', '进度跟踪']
    },
    {
      title: '部署上线',
      description: '专业的部署流程，确保系统稳定运行',
      features: ['环境配置', '性能优化', '安全加固', '监控部署']
    },
    {
      title: '维护支持',
      description: '持续的技术支持，保障系统长期稳定运行',
      features: ['bug修复', '功能升级', '性能优化', '技术咨询']
    }
  ];

  return (
    <section id="about" className="py-20 bg-gradient-to-b from-muted/20 to-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <div className="inline-flex items-center px-4 py-2 bg-primary/10 border border-primary/20 rounded-full text-primary text-sm font-medium mb-6">
            <Users className="w-4 h-4 mr-2" />
            关于我们
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            专业的<span className="bg-gradient-primary bg-clip-text text-transparent">技术团队</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            我们是一支充满激情的技术团队，致力于为客户提供最优质的定制化技术服务
            <br />
            用技术的力量，让您的想法变成现实
          </p>
        </div>

        {/* Company Introduction */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-20">
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-foreground mb-4">
              Drama Code 抓马代码
            </h3>
            <p className="text-muted-foreground leading-relaxed">
              成立于2022年，我们是一家专注于定制化技术服务的创新公司。我们相信每个客户都有独特的需求，
              因此我们不提供标准化的产品，而是为每个客户量身定制最适合的技术解决方案。
            </p>
            <p className="text-muted-foreground leading-relaxed">
              从网站开发到小程序制作，从自动化脚本到复杂系统开发，我们的技术覆盖面广泛，
              能够满足不同行业、不同规模客户的技术需求。
            </p>
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => setShowTelegramModal(true)}
                className="bg-gradient-primary hover:shadow-glow hover:scale-105 transition-all duration-300"
              >
                <Zap className="w-4 h-4 mr-2" />
                联系我们
              </Button>
            </div>
          </div>
          
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-primary opacity-20 rounded-2xl blur-xl"></div>
            <Card className="relative bg-card/50 backdrop-blur-sm border-primary/20">
              <CardContent className="p-8">
                <h4 className="text-xl font-bold text-foreground mb-6">我们的数据</h4>
                <div className="grid grid-cols-2 gap-6">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3 text-primary">
                        {stat.icon}
                      </div>
                      <div className="text-2xl font-bold text-foreground mb-1">{stat.value}</div>
                      <div className="text-sm text-muted-foreground">{stat.label}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Core Values */}
        <div className="mb-20">
          <h3 className="text-2xl font-bold text-foreground text-center mb-12">
            我们的核心价值
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="group bg-card/50 backdrop-blur-sm border-border hover:border-primary/50 transition-all duration-500 hover:shadow-glow hover:scale-105">
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-primary/20 transition-colors duration-300">
                    {value.icon}
                  </div>
                  <h4 className="text-lg font-bold text-foreground mb-3 group-hover:text-primary transition-colors duration-300">
                    {value.title}
                  </h4>
                  <p className="text-muted-foreground leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">
                    {value.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Service Process */}
        <div className="mb-20">
          <h3 className="text-2xl font-bold text-foreground text-center mb-12">
            服务流程
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {services.map((service, index) => (
              <Card key={index} className="group relative bg-card/50 backdrop-blur-sm border-border hover:border-primary/50 transition-all duration-500 hover:shadow-glow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-bold text-foreground group-hover:text-primary transition-colors duration-300">
                      {service.title}
                    </h4>
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-primary text-sm font-bold">
                      {index + 1}
                    </div>
                  </div>
                  <p className="text-muted-foreground mb-4 leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">
                    {service.description}
                  </p>
                  <div className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center text-sm text-muted-foreground group-hover:text-foreground/70 transition-colors duration-300">
                        <CheckCircle className="w-3 h-3 text-primary mr-2 flex-shrink-0" />
                        {feature}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Mission Statement */}
        <div className="text-center p-8 bg-gradient-to-r from-primary/10 to-accent/10 rounded-2xl border border-primary/20">
          <Target className="w-12 h-12 text-primary mx-auto mb-4" />
          <h3 className="text-2xl font-bold text-foreground mb-4">
            我们的使命
          </h3>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed mb-6">
            "用技术改变世界，让每个想法都能成为现实。我们不仅仅是代码的搬运工，
            更是您梦想的实现者。无论项目大小，我们都用心对待，力求完美。"
          </p>
          <Button
            onClick={() => setShowTelegramModal(true)}
            size="lg"
            className="bg-gradient-primary hover:shadow-glow hover:scale-105 transition-all duration-300"
          >
            <Heart className="w-5 h-5 mr-2" />
            加入我们的故事
          </Button>
        </div>
      </div>

      {/* Telegram Modal */}
      <TelegramModal 
        isOpen={showTelegramModal} 
        onClose={() => setShowTelegramModal(false)} 
      />
    </section>
  );
};

export default About;
