import React, { useState, useRef, useEffect, ReactNode } from 'react';

interface LazyLoadProps {
  children: ReactNode;
  placeholder?: ReactNode;
  rootMargin?: string;
  threshold?: number;
  className?: string;
  fallback?: ReactNode;
  onIntersect?: () => void;
}

const LazyLoad: React.FC<LazyLoadProps> = ({
  children,
  placeholder,
  rootMargin = '50px',
  threshold = 0.1,
  className,
  fallback,
  onIntersect,
}) => {
  const [isInView, setIsInView] = useState(false);
  const [hasError, setHasError] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          onIntersect?.();
          observer.disconnect();
        }
      },
      {
        rootMargin,
        threshold,
      }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => observer.disconnect();
  }, [rootMargin, threshold, onIntersect]);

  const DefaultPlaceholder = () => (
    <div className="animate-pulse bg-muted rounded-lg h-32 flex items-center justify-center">
      <div className="text-muted-foreground text-sm">加载中...</div>
    </div>
  );

  const ErrorFallback = () => (
    <div className="bg-muted border border-border rounded-lg h-32 flex items-center justify-center">
      <div className="text-center text-muted-foreground">
        <svg
          className="w-8 h-8 mx-auto mb-2"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fillRule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
            clipRule="evenodd"
          />
        </svg>
        <span className="text-xs">内容加载失败</span>
      </div>
    </div>
  );

  if (hasError && fallback) {
    return <>{fallback}</>;
  }

  if (hasError) {
    return <ErrorFallback />;
  }

  return (
    <div ref={elementRef} className={className}>
      {isInView ? (
        <ErrorBoundary onError={() => setHasError(true)}>
          {children}
        </ErrorBoundary>
      ) : (
        placeholder || <DefaultPlaceholder />
      )}
    </div>
  );
};

// Simple error boundary for lazy loaded content
interface ErrorBoundaryProps {
  children: ReactNode;
  onError: () => void;
}

class ErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  { hasError: boolean }
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch() {
    this.props.onError();
  }

  render() {
    if (this.state.hasError) {
      return null; // Let parent handle error display
    }

    return this.props.children;
  }
}

export default LazyLoad;
