import { Card, CardContent } from '@/components/ui/card';
import { Clock, Mail, MapPin, MessageCircle, Phone } from 'lucide-react';

const Contact = () => {
  return (
    <section id="contact" className="py-20 bg-gradient-to-b from-muted/20 to-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <div className="inline-flex items-center px-4 py-2 bg-primary/10 border border-primary/20 rounded-full text-primary text-sm font-medium mb-6">
            <MessageCircle className="w-4 h-4 mr-2" />
            联系我们
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            开始您的<span className="bg-gradient-primary bg-clip-text text-transparent">项目之旅</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            无论您有什么想法或需求，我们都随时准备为您提供专业的技术支持
            <br />
            让我们一起将您的创意变成现实
          </p>
        </div>

        {/* Contact Info */}
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Contact Cards */}
            <Card className="bg-card/50 backdrop-blur-sm border-border hover:border-primary/50 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Mail className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground">邮箱联系</h3>
                    <p className="text-muted-foreground"><EMAIL></p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card/50 backdrop-blur-sm border-border hover:border-primary/50 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Phone className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground">电话咨询</h3>
                    <p className="text-muted-foreground">+86 138-0000-0000</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card/50 backdrop-blur-sm border-border hover:border-primary/50 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Clock className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground">服务时间</h3>
                    <p className="text-muted-foreground">周一至周日 9:00-22:00</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card/50 backdrop-blur-sm border-border hover:border-primary/50 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <MapPin className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground">服务范围</h3>
                    <p className="text-muted-foreground">全国远程服务</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>


        </div>

        {/* Bottom Note */}
        <div className="text-center mt-12 p-6 bg-primary/5 rounded-lg border border-primary/10">
          <p className="text-muted-foreground">
            💡 <strong>免费咨询：</strong>我们提供免费的项目咨询服务，帮助您评估项目可行性和技术方案
          </p>
        </div>
      </div>
    </section>
  );
};

export default Contact;
