import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  MessageCircle, 
  Mail, 
  Phone, 
  MapPin, 
  Clock, 
  Send,
  CheckCircle,
  Zap,
  Users,
  Star
} from 'lucide-react';
import TelegramModal from './TelegramModal';

const Contact = () => {
  const [showTelegramModal, setShowTelegramModal] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    project: '',
    message: ''
  });

  const contactMethods = [
    {
      icon: <MessageCircle className="w-6 h-6" />,
      title: 'Telegram',
      description: '即时响应，快速沟通',
      value: '@ZHUAMACODE',
      action: '立即联系',
      primary: true
    },
    {
      icon: <Mail className="w-6 h-6" />,
      title: '邮箱咨询',
      description: '详细需求，专业回复',
      value: '<EMAIL>',
      action: '发送邮件',
      primary: false
    },
    {
      icon: <Phone className="w-6 h-6" />,
      title: '微信咨询',
      description: '扫码添加，在线咨询',
      value: '扫码添加微信',
      action: '获取二维码',
      primary: false
    }
  ];

  const workingHours = [
    { day: '周一 - 周五', time: '9:00 - 18:00' },
    { day: '周六 - 周日', time: '10:00 - 16:00' },
  ];

  const features = [
    { icon: <Zap className="w-5 h-5 text-yellow-400" />, text: '24小时内响应' },
    { icon: <Users className="w-5 h-5 text-blue-400" />, text: '专业技术团队' },
    { icon: <Star className="w-5 h-5 text-green-400" />, text: '免费需求评估' },
    { icon: <CheckCircle className="w-5 h-5 text-purple-400" />, text: '质量保证承诺' },
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // 这里可以添加表单提交逻辑
    setShowTelegramModal(true);
  };

  return (
    <section id="contact" className="py-20 bg-gradient-to-b from-background to-muted/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <div className="inline-flex items-center px-4 py-2 bg-primary/10 border border-primary/20 rounded-full text-primary text-sm font-medium mb-6">
            <MessageCircle className="w-4 h-4 mr-2" />
            联系我们
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            开始您的<span className="bg-gradient-primary bg-clip-text text-transparent">项目之旅</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            无论您有什么想法，我们都愿意倾听并为您提供专业的技术建议
            <br />
            让我们一起把您的创意变成现实
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Contact Methods */}
          <div className="lg:col-span-1 space-y-6">
            <h3 className="text-xl font-bold text-foreground mb-6">联系方式</h3>
            
            {contactMethods.map((method, index) => (
              <Card key={index} className={`group cursor-pointer transition-all duration-300 hover:shadow-glow hover:scale-105 ${
                method.primary ? 'border-primary/50 bg-primary/5' : 'bg-card/50 backdrop-blur-sm border-border hover:border-primary/30'
              }`}>
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                      method.primary ? 'bg-primary/20 text-primary' : 'bg-primary/10 text-primary'
                    } group-hover:bg-primary/30 transition-colors duration-300`}>
                      {method.icon}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-lg font-bold text-foreground mb-1 group-hover:text-primary transition-colors duration-300">
                        {method.title}
                      </h4>
                      <p className="text-sm text-muted-foreground mb-2 group-hover:text-foreground/80 transition-colors duration-300">
                        {method.description}
                      </p>
                      <p className="text-sm font-medium text-foreground mb-3">
                        {method.value}
                      </p>
                      <Button
                        onClick={() => setShowTelegramModal(true)}
                        size="sm"
                        variant={method.primary ? "default" : "outline"}
                        className={method.primary ? "bg-gradient-primary hover:shadow-glow" : "border-primary/30 hover:bg-primary/5"}
                      >
                        {method.action}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {/* Working Hours */}
            <Card className="bg-card/50 backdrop-blur-sm border-border">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <Clock className="w-5 h-5 text-primary" />
                  <h4 className="text-lg font-bold text-foreground">工作时间</h4>
                </div>
                <div className="space-y-2">
                  {workingHours.map((schedule, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span className="text-muted-foreground">{schedule.day}</span>
                      <span className="text-foreground font-medium">{schedule.time}</span>
                    </div>
                  ))}
                </div>
                <div className="mt-4 p-3 bg-primary/10 rounded-lg">
                  <p className="text-sm text-primary">
                    <Zap className="w-4 h-4 inline mr-1" />
                    紧急项目支持24/7在线
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Contact Form */}
          <div className="lg:col-span-2">
            <Card className="bg-card/50 backdrop-blur-sm border-border">
              <CardContent className="p-8">
                <h3 className="text-xl font-bold text-foreground mb-6">项目咨询表单</h3>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        您的姓名 *
                      </label>
                      <Input
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="请输入您的姓名"
                        className="bg-background/50 border-border focus:border-primary"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        联系邮箱 *
                      </label>
                      <Input
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder="<EMAIL>"
                        className="bg-background/50 border-border focus:border-primary"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      项目类型
                    </label>
                    <Input
                      name="project"
                      value={formData.project}
                      onChange={handleInputChange}
                      placeholder="网站开发 / 小程序 / 脚本开发 / 其他"
                      className="bg-background/50 border-border focus:border-primary"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      项目描述 *
                    </label>
                    <Textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      placeholder="请详细描述您的项目需求、预期功能、时间要求等..."
                      rows={6}
                      className="bg-background/50 border-border focus:border-primary resize-none"
                      required
                    />
                  </div>

                  <Button
                    type="submit"
                    size="lg"
                    className="w-full bg-gradient-primary hover:shadow-glow hover:scale-105 transition-all duration-300"
                  >
                    <Send className="w-5 h-5 mr-2" />
                    提交咨询
                  </Button>
                </form>

                {/* Features */}
                <div className="mt-8 pt-6 border-t border-border">
                  <h4 className="text-lg font-bold text-foreground mb-4">为什么选择我们？</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        {feature.icon}
                        <span className="text-sm text-muted-foreground">{feature.text}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Location Info */}
        <div className="mt-16 text-center p-8 bg-gradient-to-r from-primary/10 to-accent/10 rounded-2xl border border-primary/20">
          <MapPin className="w-8 h-8 text-primary mx-auto mb-4" />
          <h3 className="text-xl font-bold text-foreground mb-2">
            服务范围
          </h3>
          <p className="text-muted-foreground">
            我们为全球客户提供远程技术服务，主要服务区域包括中国大陆、港澳台地区以及海外华人市场
          </p>
        </div>
      </div>

      {/* Telegram Modal */}
      <TelegramModal 
        isOpen={showTelegramModal} 
        onClose={() => setShowTelegramModal(false)} 
      />
    </section>
  );
};

export default Contact;
