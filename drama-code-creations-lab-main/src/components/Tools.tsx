import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
    Code,
    Cog,
    Cpu,
    Database,
    Download,
    ExternalLink,
    FileText,
    Shield,
    Star,
    Wrench,
    Zap
} from 'lucide-react';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import TelegramModal from './TelegramModal';

interface Tool {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: React.ReactNode;
  features: string[];
  tags: string[];
  popularity: number;
}

const Tools = () => {
  const { t } = useTranslation();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showTelegramModal, setShowTelegramModal] = useState(false);

  const categories = [
    { id: 'all', name: t('tools.categories.all'), icon: <Wrench className="w-4 h-4" /> },
    { id: 'development', name: t('tools.categories.development'), icon: <Code className="w-4 h-4" /> },
    { id: 'data', name: t('tools.categories.data'), icon: <Database className="w-4 h-4" /> },
    { id: 'automation', name: t('tools.categories.automation'), icon: <Cog className="w-4 h-4" /> },
    { id: 'utilities', name: t('tools.categories.utilities'), icon: <Zap className="w-4 h-4" /> },
  ];

  const tools: Tool[] = [
    {
      id: 'api-tester',
      name: 'API接口测试器',
      description: '强大的API接口测试工具，支持多种请求方式，自动生成测试报告',
      category: 'development',
      icon: <Code className="w-6 h-6" />,
      features: ['RESTful API测试', '自动化测试脚本', '性能监控', '报告生成'],
      tags: ['API', '测试', '自动化'],
      popularity: 95
    },
    {
      id: 'data-processor',
      name: '数据处理工具',
      description: '高效的数据清洗、转换和分析工具，支持多种数据格式',
      category: 'data',
      icon: <Database className="w-6 h-6" />,
      features: ['数据清洗', '格式转换', '批量处理', '可视化分析'],
      tags: ['数据', '分析', '清洗'],
      popularity: 88
    },
    {
      id: 'automation-suite',
      name: '自动化工具套件',
      description: '全面的自动化解决方案，包含任务调度、监控和报警功能',
      category: 'automation',
      icon: <Cog className="w-6 h-6" />,
      features: ['任务调度', '系统监控', '自动报警', '日志分析'],
      tags: ['自动化', '监控', '调度'],
      popularity: 92
    },
    {
      id: 'security-scanner',
      name: '安全扫描器',
      description: '专业的安全漏洞扫描工具，帮助识别和修复安全问题',
      category: 'utilities',
      icon: <Shield className="w-6 h-6" />,
      features: ['漏洞扫描', '安全评估', '合规检查', '修复建议'],
      tags: ['安全', '扫描', '漏洞'],
      popularity: 90
    },
    {
      id: 'performance-monitor',
      name: '性能监控工具',
      description: '实时监控系统性能，提供详细的性能分析和优化建议',
      category: 'utilities',
      icon: <Cpu className="w-6 h-6" />,
      features: ['实时监控', '性能分析', '资源优化', '告警通知'],
      tags: ['性能', '监控', '优化'],
      popularity: 87
    },
    {
      id: 'report-generator',
      name: '报告生成器',
      description: '智能报告生成工具，支持多种模板和自定义格式',
      category: 'utilities',
      icon: <FileText className="w-6 h-6" />,
      features: ['模板定制', '数据可视化', '自动生成', '多格式导出'],
      tags: ['报告', '模板', '导出'],
      popularity: 85
    }
  ];

  const filteredTools = selectedCategory === 'all'
    ? tools
    : tools.filter(tool => tool.category === selectedCategory);

  return (
    <section id="tools" className="relative py-20 bg-background overflow-hidden">
      {/* 科技感背景 */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background"></div>
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full code-pattern"></div>
        </div>
        {/* 光效线条 */}
        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary/30 to-transparent"></div>
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-accent/30 to-transparent"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <div className="inline-flex items-center px-6 py-3 bg-accent/10 border-2 border-accent/20 rounded-full text-accent text-sm font-medium mb-6 hover:bg-accent/20 transition-all duration-300 group">
            <Wrench className="w-4 h-4 mr-2 group-hover:rotate-12 transition-transform duration-300" />
            {t('tools.badge')}
            <Zap className="w-4 h-4 ml-2 text-yellow-400 animate-pulse" />
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            {t('tools.title')}
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            {t('tools.subtitle')}
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              onClick={() => setSelectedCategory(category.id)}
              className="flex items-center gap-2 px-6 py-3 rounded-full transition-all duration-300 hover:scale-105"
            >
              {category.icon}
              {category.name}
            </Button>
          ))}
        </div>

        {/* Tools Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredTools.map((tool, index) => (
            <div
              key={tool.id}
              className="animate-fade-in"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <Card className="h-full hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group border-2 hover:border-primary/20">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors duration-300">
                        {tool.icon}
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg text-foreground group-hover:text-primary transition-colors duration-300">
                          {tool.name}
                        </h3>
                        <div className="flex items-center gap-2 mt-1">
                          <div className="flex items-center gap-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span className="text-sm text-muted-foreground">{tool.popularity}%</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <p className="text-muted-foreground mb-4 leading-relaxed">
                    {tool.description}
                  </p>

                  <div className="space-y-3 mb-4">
                    <h4 className="font-medium text-sm text-foreground">主要功能：</h4>
                    <ul className="space-y-1">
                      {tool.features.slice(0, 3).map((feature, idx) => (
                        <li key={idx} className="text-sm text-muted-foreground flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {tool.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      className="flex-1"
                      onClick={() => setShowTelegramModal(true)}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      获取工具
                    </Button>
                    <Button size="sm" variant="outline">
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>

      {/* Telegram Modal */}
      <TelegramModal
        isOpen={showTelegramModal}
        onClose={() => setShowTelegramModal(false)}
      />
    </section>
  );
};

export default Tools;
