import { cn } from '@/lib/utils';
import React from 'react';

// 基础加载动画
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  color?: 'primary' | 'secondary' | 'white';
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  className,
  color = 'primary'
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12'
  };

  const colorClasses = {
    primary: 'border-primary',
    secondary: 'border-secondary',
    white: 'border-white'
  };

  return (
    <div
      className={cn(
        'animate-spin rounded-full border-2 border-t-transparent',
        sizeClasses[size],
        colorClasses[color],
        className
      )}
      role="status"
      aria-label="加载中"
    />
  );
};

// 脉冲加载动画
export const LoadingPulse: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn('animate-pulse bg-muted rounded', className)} />
);

// 骨架屏组件
interface SkeletonProps {
  className?: string;
  variant?: 'text' | 'rectangular' | 'circular';
  width?: string | number;
  height?: string | number;
  lines?: number;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  className,
  variant = 'text',
  width,
  height,
  lines = 1
}) => {
  const baseClasses = 'animate-pulse bg-muted';

  const variantClasses = {
    text: 'rounded h-4',
    rectangular: 'rounded',
    circular: 'rounded-full'
  };

  if (variant === 'text' && lines > 1) {
    return (
      <div className={className}>
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={cn(
              baseClasses,
              variantClasses[variant],
              index === lines - 1 ? 'w-3/4' : 'w-full',
              index > 0 && 'mt-2'
            )}
            style={{ width: index === lines - 1 ? '75%' : width, height }}
          />
        ))}
      </div>
    );
  }

  return (
    <div
      className={cn(baseClasses, variantClasses[variant], className)}
      style={{ width, height }}
    />
  );
};

// 卡片骨架屏
export const CardSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn('p-6 border rounded-lg', className)}>
    <div className="flex items-center space-x-4 mb-4">
      <Skeleton variant="circular" width={40} height={40} />
      <div className="flex-1">
        <Skeleton variant="text" width="60%" height={16} />
        <Skeleton variant="text" width="40%" height={14} className="mt-2" />
      </div>
    </div>
    <Skeleton variant="rectangular" height={120} className="mb-4" />
    <Skeleton variant="text" lines={3} />
  </div>
);

// 列表骨架屏
interface ListSkeletonProps {
  items?: number;
  className?: string;
}

export const ListSkeleton: React.FC<ListSkeletonProps> = ({
  items = 5,
  className
}) => (
  <div className={className}>
    {Array.from({ length: items }).map((_, index) => (
      <div key={index} className="flex items-center space-x-4 py-3">
        <Skeleton variant="circular" width={32} height={32} />
        <div className="flex-1">
          <Skeleton variant="text" width="70%" height={16} />
          <Skeleton variant="text" width="50%" height={14} className="mt-1" />
        </div>
      </div>
    ))}
  </div>
);

// 表格骨架屏
interface TableSkeletonProps {
  rows?: number;
  columns?: number;
  className?: string;
}

export const TableSkeleton: React.FC<TableSkeletonProps> = ({
  rows = 5,
  columns = 4,
  className
}) => (
  <div className={cn('w-full', className)}>
    {/* 表头 */}
    <div className="flex space-x-4 pb-4 border-b">
      {Array.from({ length: columns }).map((_, index) => (
        <Skeleton key={index} variant="text" width="100%" height={20} />
      ))}
    </div>

    {/* 表格行 */}
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <div key={rowIndex} className="flex space-x-4 py-3 border-b border-muted">
        {Array.from({ length: columns }).map((_, colIndex) => (
          <Skeleton key={colIndex} variant="text" width="100%" height={16} />
        ))}
      </div>
    ))}
  </div>
);

// 页面加载组件
interface PageLoadingProps {
  message?: string;
  className?: string;
}

export const PageLoading: React.FC<PageLoadingProps> = ({
  message = '页面加载中...',
  className
}) => (
  <div className={cn(
    'flex flex-col items-center justify-center min-h-[400px] space-y-4',
    className
  )}>
    <LoadingSpinner size="lg" />
    <p className="text-muted-foreground">{message}</p>
  </div>
);

// 按钮加载状态
interface LoadingButtonProps {
  loading?: boolean;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  onClick?: () => void;
}

export const LoadingButton: React.FC<LoadingButtonProps> = ({
  loading = false,
  children,
  className,
  disabled,
  onClick
}) => (
  <button
    className={cn(
      'inline-flex items-center justify-center px-4 py-2 rounded-md',
      'bg-primary text-primary-foreground hover:bg-primary/90',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'transition-colors duration-200',
      className
    )}
    disabled={disabled || loading}
    onClick={onClick}
  >
    {loading && (
      <LoadingSpinner size="sm" color="white" className="mr-2" />
    )}
    {children}
  </button>
);

// 内容加载包装器
interface ContentLoaderProps {
  loading: boolean;
  skeleton?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

export const ContentLoader: React.FC<ContentLoaderProps> = ({
  loading,
  skeleton,
  children,
  className
}) => (
  <div className={className}>
    {loading ? (
      skeleton || <Skeleton variant="rectangular" height={200} />
    ) : (
      children
    )}
  </div>
);

// 懒加载包装器
interface LazyWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}

export const LazyWrapper: React.FC<LazyWrapperProps> = ({
  children,
  fallback,
  className
}) => (
  <React.Suspense
    fallback={
      fallback || (
        <div className={cn('flex justify-center py-8', className)}>
          <LoadingSpinner size="lg" />
        </div>
      )
    }
  >
    {children}
  </React.Suspense>
);

// 进度条组件
interface ProgressBarProps {
  progress: number; // 0-100
  className?: string;
  showLabel?: boolean;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  className,
  showLabel = false,
  color = 'primary'
}) => {
  const colorClasses = {
    primary: 'bg-primary',
    secondary: 'bg-secondary',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    error: 'bg-red-500'
  };

  return (
    <div className={cn('w-full', className)}>
      <div className="flex justify-between items-center mb-1">
        {showLabel && (
          <span className="text-sm text-muted-foreground">
            {Math.round(progress)}%
          </span>
        )}
      </div>
      <div className="w-full bg-muted rounded-full h-2">
        <div
          className={cn(
            'h-2 rounded-full transition-all duration-300 ease-out',
            colorClasses[color]
          )}
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
    </div>
  );
};

// 注意：useLoadingState hook 已移至 hooks/useLoadingState.ts 以符合 React Fast Refresh 要求
