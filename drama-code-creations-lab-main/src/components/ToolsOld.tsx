import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
    Code,
    Cog,
    Cpu,
    Database,
    Download,
    ExternalLink,
    FileText,
    Shield,
    Star,
    Users,
    Wrench,
    Zap
} from 'lucide-react';
import React, { useState } from 'react';
import TelegramModal from './TelegramModal';

interface Tool {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: React.ReactNode;
  features: string[];
  tags: string[];
  popularity: number;
  isNew?: boolean;
  isFree?: boolean;
}

const Tools = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showTelegramModal, setShowTelegramModal] = useState(false);

  const categories = [
    { id: 'all', name: '全部工具', icon: <Wrench className="w-4 h-4" /> },
    { id: 'development', name: '开发工具', icon: <Code className="w-4 h-4" /> },
    { id: 'data', name: '数据处理', icon: <Database className="w-4 h-4" /> },
    { id: 'automation', name: '自动化脚本', icon: <Cog className="w-4 h-4" /> },
    { id: 'utilities', name: '实用工具', icon: <Zap className="w-4 h-4" /> },
  ];

  const tools: Tool[] = [
    {
      id: 'api-tester',
      name: 'API接口测试器',
      description: '强大的API接口测试工具，支持多种请求方式，自动生成测试报告',
      category: 'development',
      icon: <Shield className="w-6 h-6 text-primary" />,
      features: ['支持REST/GraphQL', '自动化测试', '性能监控', '团队协作'],
      tags: ['API', '测试', '开发'],
      popularity: 95,
      isNew: true,
      isFree: false
    },
    {
      id: 'code-generator',
      name: '代码生成器',
      description: '智能代码生成工具，支持多种编程语言和框架模板',
      category: 'development',
      icon: <Code className="w-6 h-6 text-primary" />,
      features: ['多语言支持', '模板定制', '代码规范', '快速生成'],
      tags: ['代码', '生成', '模板'],
      popularity: 88,
      isFree: true
    },
    {
      id: 'excel-processor',
      name: 'Excel数据处理器',
      description: '批量处理Excel文件，数据清洗、格式转换、报表生成一键完成',
      category: 'data',
      icon: <FileText className="w-6 h-6 text-primary" />,
      features: ['批量处理', '数据清洗', '格式转换', '自动报表'],
      tags: ['Excel', '数据', '批量'],
      popularity: 92,
      isNew: true,
      isFree: false
    },
    {
      id: 'website-monitor',
      name: '网站监控脚本',
      description: '24/7网站状态监控，异常自动告警，性能数据统计分析',
      category: 'automation',
      icon: <Cpu className="w-6 h-6 text-primary" />,
      features: ['实时监控', '异常告警', '性能分析', '多渠道通知'],
      tags: ['监控', '自动化', '告警'],
      popularity: 90,
      isFree: false
    },
    {
      id: 'qr-generator',
      name: '二维码生成器',
      description: '支持多种格式的二维码生成，批量生成，自定义样式',
      category: 'utilities',
      icon: <Zap className="w-6 h-6 text-primary" />,
      features: ['多格式支持', '批量生成', '样式定制', '高清输出'],
      tags: ['二维码', '生成', '批量'],
      popularity: 85,
      isFree: true
    },
    {
      id: 'backup-tool',
      name: '自动备份工具',
      description: '智能文件备份系统，支持增量备份、云端同步、定时任务',
      category: 'automation',
      icon: <Shield className="w-6 h-6 text-primary" />,
      features: ['增量备份', '云端同步', '定时任务', '版本管理'],
      tags: ['备份', '自动化', '云端'],
      popularity: 87,
      isFree: false
    }
  ];

  const filteredTools = selectedCategory === 'all'
    ? tools
    : tools.filter(tool => tool.category === selectedCategory);

  return (
    <section id="tools" className="py-20 bg-gradient-to-b from-muted/20 to-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <div className="inline-flex items-center px-4 py-2 bg-primary/10 border border-primary/20 rounded-full text-primary text-sm font-medium mb-6">
            <Wrench className="w-4 h-4 mr-2" />
            实用工具
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            专业<span className="bg-gradient-primary bg-clip-text text-transparent">开发工具</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            我们提供各类专业开发工具和自动化脚本
            <br />
            提升您的工作效率，简化复杂任务
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              onClick={() => setSelectedCategory(category.id)}
              className={`transition-all duration-300 ${
                selectedCategory === category.id
                  ? 'bg-gradient-primary shadow-glow'
                  : 'hover:border-primary/50'
              }`}
            >
              {category.icon}
              <span className="ml-2">{category.name}</span>
            </Button>
          ))}
        </div>

        {/* Tools Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredTools.map((tool) => (
            <Card key={tool.id} className="group relative overflow-hidden bg-card/50 backdrop-blur-sm border-border hover:border-primary/50 transition-all duration-500 hover:shadow-glow hover:scale-105">
              <CardContent className="p-6">
                {/* Tool Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors duration-300">
                      {tool.icon}
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-foreground group-hover:text-primary transition-colors duration-300">
                        {tool.name}
                      </h3>
                      <div className="flex items-center space-x-2 mt-1">
                        {tool.isNew && (
                          <Badge variant="secondary" className="text-xs bg-primary/20 text-primary">
                            新品
                          </Badge>
                        )}
                        {tool.isFree && (
                          <Badge variant="outline" className="text-xs border-green-500/50 text-green-400">
                            免费
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1 text-yellow-400">
                    <Star className="w-4 h-4 fill-current" />
                    <span className="text-sm font-medium">{tool.popularity}</span>
                  </div>
                </div>

                {/* Description */}
                <p className="text-muted-foreground mb-4 leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">
                  {tool.description}
                </p>

                {/* Features */}
                <div className="space-y-2 mb-4">
                  {tool.features.map((feature, index) => (
                    <div key={index} className="flex items-center text-sm text-muted-foreground group-hover:text-foreground/70 transition-colors duration-300">
                      <div className="w-1.5 h-1.5 bg-primary rounded-full mr-3 flex-shrink-0 group-hover:animate-pulse" />
                      {feature}
                    </div>
                  ))}
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-6">
                  {tool.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs border-primary/30 text-primary/80">
                      {tag}
                    </Badge>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <Button
                    onClick={() => setShowTelegramModal(true)}
                    className="flex-1 bg-gradient-primary hover:shadow-glow transition-all duration-300 group/btn"
                    size="sm"
                  >
                    <Download className="w-4 h-4 mr-2 group-hover/btn:animate-bounce" />
                    获取工具
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowTelegramModal(true)}
                    className="border-primary/30 hover:bg-primary/5"
                    size="sm"
                  >
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16 p-8 bg-gradient-to-r from-primary/10 to-accent/10 rounded-2xl border border-primary/20">
          <h3 className="text-2xl font-bold text-foreground mb-4">
            需要定制化工具？
          </h3>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            我们可以根据您的具体需求开发专属工具和脚本，提升您的工作效率
          </p>
          <Button
            onClick={() => setShowTelegramModal(true)}
            size="lg"
            className="bg-gradient-primary hover:shadow-glow hover:scale-105 transition-all duration-300"
          >
            <Users className="w-5 h-5 mr-2" />
            联系定制开发
          </Button>
        </div>
      </div>

      {/* Telegram Modal */}
      <TelegramModal
        isOpen={showTelegramModal}
        onClose={() => setShowTelegramModal(false)}
      />
    </section>
  );
};

export default Tools;
