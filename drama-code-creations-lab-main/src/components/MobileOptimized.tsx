import { useResponsive, useTouchOptimization } from '@/hooks/useResponsive';
import { viewport } from '@/lib/responsive';
import React, { useEffect } from 'react';

/**
 * 移动端优化组件
 * 提供移动端特定的优化功能
 */
interface MobileOptimizedProps {
  children: React.ReactNode;
  className?: string;
}

export const MobileOptimized: React.FC<MobileOptimizedProps> = ({
  children,
  className = ''
}) => {
  const { isMobile, isTablet, isTouchDevice } = useResponsive();
  useTouchOptimization();

  useEffect(() => {
    // 设置视口高度CSS变量
    viewport.setViewportHeight();

    // 移动端特定优化
    if (isMobile) {
      // 防止双击缩放
      const preventZoom = (e: TouchEvent) => {
        if (e.touches.length > 1) {
          e.preventDefault();
        }
      };

      let lastTouchEnd = 0;
      const preventDoubleClickZoom = (e: TouchEvent) => {
        const now = new Date().getTime();
        if (now - lastTouchEnd <= 300) {
          e.preventDefault();
        }
        lastTouchEnd = now;
      };

      document.addEventListener('touchstart', preventZoom, { passive: false });
      document.addEventListener('touchend', preventDoubleClickZoom, { passive: false });

      return () => {
        document.removeEventListener('touchstart', preventZoom);
        document.removeEventListener('touchend', preventDoubleClickZoom);
      };
    }

    // 非移动端情况下返回空的清理函数
    return () => {};
  }, [isMobile]);

  const containerClasses = `
    ${className}
    ${isMobile ? 'mobile-optimized' : ''}
    ${isTablet ? 'tablet-optimized' : ''}
    ${isTouchDevice ? 'touch-optimized' : ''}
  `.trim();

  return (
    <div className={containerClasses}>
      {children}
    </div>
  );
};

/**
 * 响应式容器组件
 */
interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className = '',
  maxWidth = 'xl'
}) => {
  const { isMobile, isTablet } = useResponsive();

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full'
  };

  const containerClasses = `
    mx-auto container-responsive
    ${maxWidthClasses[maxWidth]}
    ${isMobile ? 'px-4' : isTablet ? 'px-6' : 'px-8'}
    ${className}
  `.trim();

  return (
    <div className={containerClasses}>
      {children}
    </div>
  );
};

/**
 * 响应式网格组件
 */
interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  cols?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: 'sm' | 'md' | 'lg' | 'xl';
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className = '',
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 'md'
}) => {
  const { isMobile, isTablet } = useResponsive();

  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8'
  };

  const getGridCols = () => {
    if (isMobile) return `grid-cols-${cols.mobile || 1}`;
    if (isTablet) return `grid-cols-${cols.tablet || 2}`;
    return `grid-cols-${cols.desktop || 3}`;
  };

  const gridClasses = `
    grid ${getGridCols()} ${gapClasses[gap]}
    ${className}
  `.trim();

  return (
    <div className={gridClasses}>
      {children}
    </div>
  );
};

/**
 * 响应式文本组件
 */
interface ResponsiveTextProps {
  children: React.ReactNode;
  className?: string;
  size?: {
    mobile?: string;
    tablet?: string;
    desktop?: string;
  };
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div';
}

export const ResponsiveText: React.FC<ResponsiveTextProps> = ({
  children,
  className = '',
  size = { mobile: 'text-base', tablet: 'text-lg', desktop: 'text-xl' },
  as: Component = 'p'
}) => {
  const { isMobile, isTablet } = useResponsive();

  const getTextSize = () => {
    if (isMobile) return size.mobile;
    if (isTablet) return size.tablet;
    return size.desktop;
  };

  const textClasses = `
    ${getTextSize()}
    ${className}
  `.trim();

  return (
    <Component className={textClasses}>
      {children}
    </Component>
  );
};

/**
 * 响应式按钮组件
 */
interface ResponsiveButtonProps {
  children: React.ReactNode;
  className?: string;
  size?: {
    mobile?: 'sm' | 'default' | 'lg';
    tablet?: 'sm' | 'default' | 'lg';
    desktop?: 'sm' | 'default' | 'lg';
  };
  fullWidth?: boolean;
  onClick?: () => void;
  disabled?: boolean;
  variant?: 'default' | 'outline' | 'ghost';
}

export const ResponsiveButton: React.FC<ResponsiveButtonProps> = ({
  children,
  className = '',
  size = { mobile: 'default', tablet: 'default', desktop: 'lg' },
  fullWidth = false,
  onClick,
  disabled = false,
  variant = 'default'
}) => {
  const { isMobile, isTablet, isTouchDevice } = useResponsive();

  const getButtonSize = () => {
    if (isMobile) return size.mobile || 'default';
    if (isTablet) return size.tablet || 'default';
    return size.desktop || 'default';
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    default: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  const variantClasses = {
    default: 'bg-primary text-primary-foreground hover:bg-primary/90',
    outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
    ghost: 'hover:bg-accent hover:text-accent-foreground'
  };

  const buttonClasses = `
    inline-flex items-center justify-center rounded-md font-medium transition-colors
    focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring
    disabled:pointer-events-none disabled:opacity-50
    ${sizeClasses[getButtonSize()]}
    ${variantClasses[variant]}
    ${fullWidth ? 'w-full' : ''}
    ${isTouchDevice ? 'min-h-[44px] min-w-[44px]' : ''}
    ${className}
  `.trim();

  return (
    <button
      className={buttonClasses}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

/**
 * 响应式卡片组件
 */
interface ResponsiveCardProps {
  children: React.ReactNode;
  className?: string;
  padding?: {
    mobile?: string;
    tablet?: string;
    desktop?: string;
  };
  hover?: boolean;
}

export const ResponsiveCard: React.FC<ResponsiveCardProps> = ({
  children,
  className = '',
  padding = { mobile: 'p-4', tablet: 'p-6', desktop: 'p-8' },
  hover = true
}) => {
  const { isMobile, isTablet } = useResponsive();

  const getPadding = () => {
    if (isMobile) return padding.mobile;
    if (isTablet) return padding.tablet;
    return padding.desktop;
  };

  const cardClasses = `
    bg-card text-card-foreground rounded-lg border shadow-sm
    ${getPadding()}
    ${hover ? 'hover:shadow-md transition-shadow duration-200' : ''}
    ${className}
  `.trim();

  return (
    <div className={cardClasses}>
      {children}
    </div>
  );
};

export default MobileOptimized;
