import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  loading?: 'lazy' | 'eager';
  priority?: boolean;
  placeholder?: string;
  sizes?: string;
  srcSet?: string;
  onLoad?: () => void;
  onError?: () => void;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className,
  loading = 'lazy',
  priority = false,
  placeholder,
  sizes,
  srcSet,
  onLoad,
  onError,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || loading === 'eager') {
      setIsInView(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '50px', // Start loading 50px before the image enters viewport
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority, loading]);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  // Generate WebP srcSet if not provided
  const generateSrcSet = (originalSrc: string): string => {
    if (srcSet) return srcSet;
    
    const baseName = originalSrc.split('.').slice(0, -1).join('.');
    const extension = originalSrc.split('.').pop();
    
    // Generate different sizes for responsive images
    const sizes = [320, 640, 768, 1024, 1280, 1920];
    return sizes
      .map(size => `${baseName}-${size}w.webp ${size}w`)
      .join(', ');
  };

  // Placeholder component
  const Placeholder = () => (
    <div
      className={cn(
        'bg-muted animate-pulse flex items-center justify-center',
        className
      )}
      style={{ width, height }}
      aria-hidden="true"
    >
      {placeholder ? (
        <span className="text-muted-foreground text-sm">{placeholder}</span>
      ) : (
        <svg
          className="w-8 h-8 text-muted-foreground"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fillRule="evenodd"
            d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
            clipRule="evenodd"
          />
        </svg>
      )}
    </div>
  );

  // Error component
  const ErrorFallback = () => (
    <div
      className={cn(
        'bg-muted border border-border flex items-center justify-center',
        className
      )}
      style={{ width, height }}
      role="img"
      aria-label={`图片加载失败: ${alt}`}
    >
      <div className="text-center text-muted-foreground">
        <svg
          className="w-8 h-8 mx-auto mb-2"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fillRule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
            clipRule="evenodd"
          />
        </svg>
        <span className="text-xs">图片加载失败</span>
      </div>
    </div>
  );

  if (hasError) {
    return <ErrorFallback />;
  }

  return (
    <div className="relative" ref={imgRef}>
      {/* Placeholder shown while loading */}
      {!isLoaded && <Placeholder />}
      
      {/* Actual image */}
      {isInView && (
        <picture>
          {/* WebP source for modern browsers */}
          <source
            srcSet={generateSrcSet(src)}
            sizes={sizes || '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'}
            type="image/webp"
          />
          
          {/* Fallback image */}
          <img
            src={src}
            alt={alt}
            width={width}
            height={height}
            className={cn(
              'transition-opacity duration-300',
              isLoaded ? 'opacity-100' : 'opacity-0',
              className
            )}
            loading={loading}
            decoding="async"
            onLoad={handleLoad}
            onError={handleError}
            style={{
              position: isLoaded ? 'static' : 'absolute',
              top: isLoaded ? 'auto' : 0,
              left: isLoaded ? 'auto' : 0,
            }}
          />
        </picture>
      )}
    </div>
  );
};

export default OptimizedImage;
