import serviceMiniProgramImg from '@/assets/service-miniprogram.jpg';
import serviceScriptImg from '@/assets/service-script.jpg';
import serviceWebImg from '@/assets/service-web.jpg';
import { Globe, Heart } from 'lucide-react';
import { useState } from 'react';
import ServiceCard from './ServiceCard';
import TelegramModal from './TelegramModal';

const Services = () => {
  const [showTelegramModal, setShowTelegramModal] = useState(false);
  const services = [
    {
      title: '网站搭建',
      description: '提供从需求分析到上线部署的全流程网站开发服务，包括企业官网、电商平台、管理系统等各类网站解决方案。',
      features: [
        '响应式设计，完美适配所有设备',
        'SEO优化，提升搜索引擎排名',
        '高性能架构，确保访问速度',
        '安全防护，保障数据安全',
        '后期维护，持续技术支持'
      ],
      image: serviceWebImg
    },
    {
      title: '脚本开发',
      description: '专业的自动化脚本开发服务，帮助您简化重复性工作，提高工作效率。支持数据处理、任务自动化、系统集成等场景。',
      features: [
        '数据采集与处理自动化',
        '定时任务与监控脚本',
        'API接口集成与开发',
        '系统自动化部署脚本',
        '自定义业务逻辑实现'
      ],
      image: serviceScriptImg
    },
    {
      title: '小程序开发',
      description: '微信小程序、支付宝小程序等各类小程序开发服务，从UI设计到功能开发，提供一站式解决方案。',
      features: [
        '原生小程序开发',
        '跨平台适配优化',
        '支付与会员系统集成',
        '数据统计与分析',
        '小程序商城解决方案'
      ],
      image: serviceMiniProgramImg
    }
  ];

  return (
    <section id="services" className="py-20 bg-gradient-to-b from-background to-muted/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Enhanced Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <div className="inline-flex items-center px-6 py-3 bg-primary/10 border-2 border-primary/20 rounded-full text-primary text-sm font-medium mb-6 hover:bg-primary/20 transition-all duration-300 group">
            <Globe className="w-4 h-4 mr-2 group-hover:rotate-12 transition-transform duration-300" />
            专业服务
            <Heart className="w-4 h-4 ml-2 text-red-400 animate-pulse" />
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            我们的<span className="bg-gradient-primary bg-clip-text text-transparent hover:animate-glow transition-all duration-300">核心服务</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            <span className="inline-block hover:text-primary transition-colors duration-300">专注于为客户提供高质量的定制化技术解决方案</span>
            <br />
            <span className="inline-block hover:text-accent transition-colors duration-300">每一个项目都精心打造，确保满足您的独特需求</span>
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div
              key={service.title}
              className="animate-fade-in"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <ServiceCard {...service} />
            </div>
          ))}
        </div>


      </div>

      {/* Telegram Modal */}
      <TelegramModal
        isOpen={showTelegramModal}
        onClose={() => setShowTelegramModal(false)}
      />
    </section>
  );
};

export default Services;
