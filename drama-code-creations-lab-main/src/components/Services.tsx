import { Globe, Heart } from 'lucide-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import ServiceCard from './ServiceCard';
import TelegramModal from './TelegramModal';

const Services = () => {
  const { t } = useTranslation();
  const [showTelegramModal, setShowTelegramModal] = useState(false);

  // 辅助函数：安全获取翻译数组
  const getTranslationArray = (key: string): string[] => {
    const result = t(key, { returnObjects: true });
    return Array.isArray(result) ? result : [];
  };

  // 服务数据 - 使用新的科技感设计
  const services = [
    {
      title: t('services.web.title'),
      description: t('services.web.description'),
      features: getTranslationArray('services.web.features'),
      serviceType: 'web' as const
    },
    {
      title: t('services.script.title'),
      description: t('services.script.description'),
      features: getTranslationArray('services.script.features'),
      serviceType: 'script' as const
    },
    {
      title: t('services.miniprogram.title'),
      description: t('services.miniprogram.description'),
      features: getTranslationArray('services.miniprogram.features'),
      serviceType: 'miniprogram' as const
    },
    {
      title: t('services.mobile.title'),
      description: t('services.mobile.description'),
      features: getTranslationArray('services.mobile.features'),
      serviceType: 'mobile' as const
    }
  ];

  return (
    <section id="services" className="relative py-20 bg-background overflow-hidden">
      {/* 科技感背景 */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background"></div>
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full default-pattern"></div>
        </div>
        {/* 光效线条 */}
        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary/30 to-transparent"></div>
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-accent/30 to-transparent"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Enhanced Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <div className="inline-flex items-center px-6 py-3 bg-primary/10 border-2 border-primary/20 rounded-full text-primary text-sm font-medium mb-6 hover:bg-primary/20 transition-all duration-300 group">
            <Globe className="w-4 h-4 mr-2 group-hover:rotate-12 transition-transform duration-300" />
            {t('services.badge')}
            <Heart className="w-4 h-4 ml-2 text-red-400 animate-pulse" />
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            {t('services.title')} <span className="bg-gradient-primary bg-clip-text text-transparent hover:animate-glow transition-all duration-300">{t('services.titleHighlight')}</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            <span className="inline-block hover:text-primary transition-colors duration-300">{t('services.description1')}</span>
            <br />
            <span className="inline-block hover:text-accent transition-colors duration-300">{t('services.description2')}</span>
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
          {services.map((service, index) => (
            <div
              key={service.title}
              className="animate-fade-in"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <ServiceCard {...service} />
            </div>
          ))}
        </div>
      </div>

      {/* Telegram Modal */}
      <TelegramModal
        isOpen={showTelegramModal}
        onClose={() => setShowTelegramModal(false)}
      />
    </section>
  );
};

export default Services;
