/**
 * 性能监控工具
 * 集成Web Vitals和自定义性能指标追踪
 */

// Web Vitals 类型定义
interface WebVitalMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
}

// 性能指标类型
interface PerformanceMetrics {
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  ttfb?: number; // Time to First Byte
  customMetrics?: Record<string, number>;
}

// 性能监控配置
interface PerformanceConfig {
  enableWebVitals: boolean;
  enableCustomMetrics: boolean;
  enableResourceTiming: boolean;
  enableNavigationTiming: boolean;
  reportingEndpoint?: string;
  sampleRate: number; // 0-1, 采样率
}

class PerformanceMonitor {
  private config: PerformanceConfig;
  private metrics: PerformanceMetrics = {};
  private observers: PerformanceObserver[] = [];

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = {
      enableWebVitals: true,
      enableCustomMetrics: true,
      enableResourceTiming: true,
      enableNavigationTiming: true,
      sampleRate: 1.0,
      ...config,
    };

    this.init();
  }

  private init() {
    if (typeof window === 'undefined') return;

    // 采样控制
    if (Math.random() > this.config.sampleRate) return;

    if (this.config.enableWebVitals) {
      this.initWebVitals();
    }

    if (this.config.enableCustomMetrics) {
      this.initCustomMetrics();
    }

    if (this.config.enableResourceTiming) {
      this.initResourceTiming();
    }

    if (this.config.enableNavigationTiming) {
      this.initNavigationTiming();
    }
  }

  private initWebVitals() {
    // 动态导入web-vitals库（如果安装了的话）
    import('web-vitals')
      .then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        getCLS(this.onWebVital.bind(this));
        getFID(this.onWebVital.bind(this));
        getFCP(this.onWebVital.bind(this));
        getLCP(this.onWebVital.bind(this));
        getTTFB(this.onWebVital.bind(this));
      })
      .catch(() => {
        // 如果没有安装web-vitals，使用自定义实现
        this.initCustomWebVitals();
      });
  }

  private initCustomWebVitals() {
    // FCP - First Contentful Paint
    if ('PerformanceObserver' in window) {
      const fcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
        if (fcpEntry) {
          this.recordMetric('FCP', fcpEntry.startTime);
        }
      });
      fcpObserver.observe({ entryTypes: ['paint'] });
      this.observers.push(fcpObserver);
    }

    // LCP - Largest Contentful Paint
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.recordMetric('LCP', lastEntry.startTime);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(lcpObserver);
    }

    // CLS - Cumulative Layout Shift
    if ('PerformanceObserver' in window) {
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        this.recordMetric('CLS', clsValue);
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(clsObserver);
    }
  }

  private initCustomMetrics() {
    // 页面加载时间
    window.addEventListener('load', () => {
      const loadTime = performance.now();
      this.recordMetric('PageLoadTime', loadTime);
    });

    // DOM内容加载时间
    document.addEventListener('DOMContentLoaded', () => {
      const domLoadTime = performance.now();
      this.recordMetric('DOMContentLoaded', domLoadTime);
    });
  }

  private initResourceTiming() {
    if ('PerformanceObserver' in window) {
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          this.analyzeResourceTiming(entry as PerformanceResourceTiming);
        });
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);
    }
  }

  private initNavigationTiming() {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        this.analyzeNavigationTiming(navigation);
      }
    });
  }

  private onWebVital(metric: WebVitalMetric) {
    this.recordMetric(metric.name, metric.value, metric.rating);
    this.reportMetric(metric.name, metric.value, { rating: metric.rating });
  }

  private recordMetric(name: string, value: number, rating?: string) {
    const metricKey = name.toLowerCase() as keyof PerformanceMetrics;
    if (metricKey in this.metrics) {
      (this.metrics as any)[metricKey] = value;
    } else {
      if (!this.metrics.customMetrics) {
        this.metrics.customMetrics = {};
      }
      this.metrics.customMetrics[name] = value;
    }

    console.log(`[Performance] ${name}: ${value.toFixed(2)}ms${rating ? ` (${rating})` : ''}`);
  }

  private analyzeResourceTiming(entry: PerformanceResourceTiming) {
    const duration = entry.responseEnd - entry.requestStart;
    const resourceType = this.getResourceType(entry.name);
    
    this.recordMetric(`${resourceType}LoadTime`, duration);
    
    // 检查慢资源
    if (duration > 1000) {
      console.warn(`[Performance] Slow resource detected: ${entry.name} (${duration.toFixed(2)}ms)`);
    }
  }

  private analyzeNavigationTiming(navigation: PerformanceNavigationTiming) {
    const metrics = {
      DNS: navigation.domainLookupEnd - navigation.domainLookupStart,
      TCP: navigation.connectEnd - navigation.connectStart,
      Request: navigation.responseStart - navigation.requestStart,
      Response: navigation.responseEnd - navigation.responseStart,
      Processing: navigation.domComplete - navigation.responseEnd,
    };

    Object.entries(metrics).forEach(([name, value]) => {
      this.recordMetric(`Navigation${name}`, value);
    });
  }

  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'JavaScript';
    if (url.includes('.css')) return 'CSS';
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) return 'Image';
    if (url.includes('.woff') || url.includes('.ttf')) return 'Font';
    return 'Other';
  }

  private reportMetric(name: string, value: number, metadata?: any) {
    if (this.config.reportingEndpoint) {
      // 发送到分析服务
      fetch(this.config.reportingEndpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          metric: name,
          value,
          metadata,
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent,
        }),
      }).catch(console.error);
    }

    // 发送到Google Analytics（如果可用）
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'web_vital', {
        event_category: 'Performance',
        event_label: name,
        value: Math.round(value),
        custom_map: { metric_rating: metadata?.rating },
      });
    }
  }

  // 公共方法
  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  public markStart(name: string) {
    performance.mark(`${name}-start`);
  }

  public markEnd(name: string) {
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);
    
    const measure = performance.getEntriesByName(name, 'measure')[0];
    if (measure) {
      this.recordMetric(name, measure.duration);
    }
  }

  public destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor({
  enableWebVitals: true,
  enableCustomMetrics: true,
  sampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0, // 生产环境10%采样
});

// 导出工具函数
export const markPerformance = {
  start: (name: string) => performanceMonitor.markStart(name),
  end: (name: string) => performanceMonitor.markEnd(name),
  getMetrics: () => performanceMonitor.getMetrics(),
};

// React Hook for performance monitoring
export const usePerformanceMonitoring = () => {
  return {
    markStart: performanceMonitor.markStart.bind(performanceMonitor),
    markEnd: performanceMonitor.markEnd.bind(performanceMonitor),
    getMetrics: performanceMonitor.getMetrics.bind(performanceMonitor),
  };
};
