/**
 * 可访问性工具函数
 * 提供键盘导航、焦点管理、ARIA属性等辅助功能
 */

/**
 * 处理键盘事件（Enter和Space键）
 * @param event 键盘事件
 * @param callback 回调函数
 */
export const handleKeyboardActivation = (
  event: React.KeyboardEvent,
  callback: () => void
) => {
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault();
    callback();
  }
};

/**
 * 处理ESC键事件
 * @param event 键盘事件
 * @param callback 回调函数
 */
export const handleEscapeKey = (
  event: React.KeyboardEvent,
  callback: () => void
) => {
  if (event.key === 'Escape') {
    event.preventDefault();
    callback();
  }
};

/**
 * 焦点陷阱 - 在模态框或下拉菜单中限制焦点
 * @param containerRef 容器引用
 * @param isActive 是否激活焦点陷阱
 */
export const useFocusTrap = (
  containerRef: React.RefObject<HTMLElement>,
  isActive: boolean
) => {
  React.useEffect(() => {
    if (!isActive || !containerRef.current) return;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return;

      if (event.shiftKey) {
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement?.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          event.preventDefault();
          firstElement?.focus();
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    firstElement?.focus();

    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }, [isActive, containerRef]);
};

/**
 * 生成唯一ID，用于ARIA属性
 * @param prefix 前缀
 * @returns 唯一ID
 */
export const generateId = (prefix: string = 'id'): string => {
  return `${prefix}-${Math.random().toString(36).substring(2, 11)}`;
};

/**
 * 检查元素是否在视口中
 * @param element HTML元素
 * @returns 是否在视口中
 */
export const isElementInViewport = (element: HTMLElement): boolean => {
  const rect = element.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
};

/**
 * 平滑滚动到指定元素
 * @param elementId 元素ID
 * @param offset 偏移量
 */
export const scrollToElement = (elementId: string, offset: number = 0): void => {
  const element = document.getElementById(elementId);
  if (element) {
    const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
    const offsetPosition = elementPosition - offset;

    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    });
  }
};

/**
 * 颜色对比度计算
 * @param color1 颜色1 (hex格式)
 * @param color2 颜色2 (hex格式)
 * @returns 对比度比值
 */
export const calculateContrastRatio = (color1: string, color2: string): number => {
  const getLuminance = (hex: string): number => {
    const rgb = parseInt(hex.slice(1), 16);
    const r = (rgb >> 16) & 0xff;
    const g = (rgb >> 8) & 0xff;
    const b = (rgb >> 0) & 0xff;

    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * (rs || 0) + 0.7152 * (gs || 0) + 0.0722 * (bs || 0);
  };

  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);

  return (brightest + 0.05) / (darkest + 0.05);
};

/**
 * 检查颜色对比度是否符合WCAG标准
 * @param color1 颜色1
 * @param color2 颜色2
 * @param level AA或AAA级别
 * @returns 是否符合标准
 */
export const isContrastCompliant = (
  color1: string,
  color2: string,
  level: 'AA' | 'AAA' = 'AA'
): boolean => {
  const ratio = calculateContrastRatio(color1, color2);
  return level === 'AA' ? ratio >= 4.5 : ratio >= 7;
};

import React from 'react';

/**
 * 屏幕阅读器专用文本
 * @param text 文本内容
 * @returns JSX元素
 */
export const ScreenReaderOnly: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <span className= "sr-only" > { children } </span>
);

/**
 * 跳转到主内容的链接
 */
export const SkipToContent: React.FC = () => (
  <a
    href= "#main-content"
className = "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md z-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
  >
  跳转到主内容
  </a>
);
