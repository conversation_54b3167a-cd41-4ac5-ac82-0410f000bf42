/**
 * 图片优化工具库
 * 提供WebP支持检测、响应式图片、懒加载等功能
 */

// WebP支持检测
export const supportsWebP = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
};

// AVIF支持检测
export const supportsAVIF = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const avif = new Image();
    avif.onload = avif.onerror = () => {
      resolve(avif.height === 2);
    };
    avif.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
  });
};

// 图片格式优先级
export const getOptimalImageFormat = async (): Promise<'avif' | 'webp' | 'jpg'> => {
  if (await supportsAVIF()) return 'avif';
  if (await supportsWebP()) return 'webp';
  return 'jpg';
};

// 响应式图片源生成 - 接口已移除，直接使用内联类型

export const generateResponsiveImageSources = (
  baseSrc: string,
  format: 'avif' | 'webp' | 'jpg' = 'webp'
): string[] => {
  const breakpoints = [640, 768, 1024, 1280, 1536];
  const baseUrl = baseSrc.replace(/\.[^/.]+$/, ''); // 移除扩展名

  return breakpoints.map(width =>
    `${baseUrl}-${width}w.${format} ${width}w`
  );
};

// 懒加载图片组件 (保留供未来使用)
// interface LazyImageProps extends ResponsiveImageOptions {
//   placeholder?: string;
//   onLoad?: () => void;
//   onError?: () => void;
// }

export class LazyImageLoader {
  private observer: IntersectionObserver | null = null;
  private loadedImages = new Set<string>();

  constructor() {
    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              this.loadImage(entry.target as HTMLImageElement);
            }
          });
        },
        {
          rootMargin: '50px 0px',
          threshold: 0.01
        }
      );
    }
  }

  private async loadImage(img: HTMLImageElement) {
    const src = img.dataset.src;
    if (!src || this.loadedImages.has(src)) return;

    try {
      // 预加载图片
      const image = new Image();
      image.onload = () => {
        img.src = src;
        img.classList.remove('lazy-loading');
        img.classList.add('lazy-loaded');
        this.loadedImages.add(src);

        // 触发自定义事件
        img.dispatchEvent(new CustomEvent('imageLoaded'));
      };

      image.onerror = () => {
        img.classList.add('lazy-error');
        img.dispatchEvent(new CustomEvent('imageError'));
      };

      image.src = src;
    } catch (error) {
      console.error('Image loading failed:', error);
    }

    if (this.observer) {
      this.observer.unobserve(img);
    }
  }

  observe(img: HTMLImageElement) {
    if (this.observer) {
      this.observer.observe(img);
    } else {
      // 降级处理：直接加载
      this.loadImage(img);
    }
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}

// 全局懒加载实例
export const lazyImageLoader = new LazyImageLoader();

// 图片压缩工具
export const compressImage = (
  file: File,
  quality: number = 0.8,
  maxWidth: number = 1920,
  maxHeight: number = 1080
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 计算新尺寸
      let { width, height } = img;

      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }

      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      canvas.width = width;
      canvas.height = height;

      // 绘制并压缩
      ctx?.drawImage(img, 0, 0, width, height);

      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Image compression failed'));
          }
        },
        'image/jpeg',
        quality
      );
    };

    img.onerror = () => reject(new Error('Image loading failed'));
    img.src = URL.createObjectURL(file);
  });
};

// 图片预加载
export const preloadImages = (urls: string[]): Promise<void[]> => {
  return Promise.all(
    urls.map(url =>
      new Promise<void>((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve();
        img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
        img.src = url;
      })
    )
  );
};

// 图片尺寸获取
export const getImageDimensions = (src: string): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.naturalWidth, height: img.naturalHeight });
    };
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = src;
  });
};

// 图片缓存管理
class ImageCache {
  private cache = new Map<string, string>();
  private maxSize = 50; // 最大缓存数量

  set(key: string, value: string) {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        this.cache.delete(firstKey);
      }
    }
    this.cache.set(key, value);
  }

  get(key: string): string | undefined {
    return this.cache.get(key);
  }

  has(key: string): boolean {
    return this.cache.has(key);
  }

  clear() {
    this.cache.clear();
  }
}

export const imageCache = new ImageCache();

// 图片URL优化
export const optimizeImageUrl = (
  src: string,
  width?: number,
  height?: number,
  quality: number = 80,
  format?: 'avif' | 'webp' | 'jpg'
): string => {
  // 如果是外部URL，直接返回
  if (src.startsWith('http')) {
    return src;
  }

  // 构建优化参数
  const params = new URLSearchParams();
  if (width) params.set('w', width.toString());
  if (height) params.set('h', height.toString());
  if (quality !== 80) params.set('q', quality.toString());
  if (format) params.set('f', format);

  const queryString = params.toString();
  return queryString ? `${src}?${queryString}` : src;
};

// 图片错误处理
export const handleImageError = (
  img: HTMLImageElement,
  fallbackSrc?: string
) => {
  if (fallbackSrc && img.src !== fallbackSrc) {
    img.src = fallbackSrc;
  } else {
    // 显示默认占位符
    img.style.backgroundColor = '#f3f4f6';
    img.style.display = 'flex';
    img.style.alignItems = 'center';
    img.style.justifyContent = 'center';
    img.innerHTML = '<span style="color: #9ca3af;">图片加载失败</span>';
  }
};

// 图片性能监控
export const trackImagePerformance = (src: string, startTime: number) => {
  const loadTime = performance.now() - startTime;

  // 发送性能数据到分析服务
  if ('sendBeacon' in navigator) {
    const data = JSON.stringify({
      type: 'image-load',
      src,
      loadTime,
      timestamp: Date.now()
    });

    navigator.sendBeacon('/api/analytics', data);
  }
};
